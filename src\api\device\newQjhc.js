import request from '@/router/axios';

export const getDeviceList = (current, size, params) => {
  return request({
    url: '/api/sb/sbtj/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
// 保存移液器
export const yyqSubmit = (row) => {
  return request({
    url: '/api/sb/hcjl/yyqSubmit',
    method: 'post',
    data: row
  })
}
// 移液器详情
export const getYyqDetail = (hcid) => {
  return request({
    url: '/api/sb/hcjl/yyqDetail',
    method: 'get',
    params: {
      hcid
    }
  })
}

// 保存电子天平
export const dztpSubmit = (row) => {
  return request({
    url: '/api/sb/hcjl/dztpSubmit',
    method: 'post',
    data: row
  })
}
// 电子天平详情
export const getDztpDetail = (hcid) => {
  return request({
    url: '/api/sb/hcjl/dztpDetail',
    method: 'get',
    params: {
      hcid
    }
  })
}

// 保存水浴锅
export const sygSubmit = (row) => {
  return request({
    url: '/api/sb/hcjl/sygSubmit',
    method: 'post',
    data: row
  })
}

// 水浴锅详情
export const getSygDetail = (hcid) => {
  return request({
    url: '/api/sb/hcjl/sygDetail',
    method: 'get',
    params: {
      hcid
    }
  })
}

// 保存分光光度计
export const fggdjSubmit = (row) => {
  return request({
    url: '/api/sb/hcjl/fggdjSubmit',
    method: 'post',
    data: row
  })
}


// 光度计详情
export const getGdjDetail = (hcid) => {
  return request({
    url: '/api/sb/hcjl/fggdjDetail',
    method: 'get',
    params: {
      hcid
    }
  })
}

export const remove = (hcid) => {
  return request({
    url: '/api/sb/hcjl/remove',
    method: 'post',
    params: {
      hcid,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/sb/sbtj/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/sb/sbtj/submit',
    method: 'post',
    data: row
  })
}

