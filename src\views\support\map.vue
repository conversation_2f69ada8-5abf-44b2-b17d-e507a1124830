<template>
  <div style="width: 100%;height:100%">
    <div id="map"></div>
  </div>
</template>

<script>
  export default {
    name: "map",
    data(){
      return {
        map: null,
      }
    },
    mounted() {
      this.initMap()
    },
    methods:{
      initMap(){
        this.map = new BMapGL.Map('map')
        this.map.setMapStyleV2({
          styleId: '93bc7c8393da779cb8295e6bfcf6bb12'
        });
        this.map.centerAndZoom(new BMapGL.Point(118.58215,37.44878), 15)
        this.map.enableScrollWheelZoom();
        this.map.setDisplayOptions({
          poiIcon: true
        })
        this.drawPoint()
      },
      drawPoint(){
        var point = new BMapGL.Point(118.58215,37.44878);
        var marker = new BMapGL.Marker(point);        // 创建标注
        this.map.addOverlay(marker);
        var opts = {
          position: new BMapGL.Point(118.58215,37.44878), // 指定文本标注所在的地理位置
          offset: new BMapGL.Size(20, -30) // 设置文本偏移量
        };
        var label = new BMapGL.Label('监测点1', opts);             // 创建文本标注对象
        label.setStyle({
          borderRadius: '5px',
          padding: '3px'
        });                                 // 自定义文本标注样式
        this.map.addOverlay(label);
      }
    }
  }
</script>

<style scoped>
 #map{
   width: 100%;
   height: 100%;
 }
</style>
