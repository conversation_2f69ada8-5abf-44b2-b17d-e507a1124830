<template>
  <div>
    <div @click="print" class="print">打印</div>
    <div id="print">
      <h3 class="title">多功能食品安全分析仪期间核查记录表</h3>
      <table>
        <tr>
          <td class="w10">仪器编号</td>
          <td class="w15">{{fggdjhc.sbbh}}</td>
          <td class="w20">规格型号</td>
          <td class="w20">{{fggdjhc.ggxh}}</td>
          <td class="w15">核查依据</td>
          <td class="w15">{{fggdjhc.zcfg}}</td>
        </tr>
        <tr>
          <td class="w10">核查日期</td>
          <td class="w15">{{fggdjhc.qjhcrq}}</td>
          <td class="w20">环境温度</td>
          <td class="w20">{{fggdjhc.hjwd}}</td>
          <td class="w15">环境湿度</td>
          <td class="w15">{{fggdjhc.hjsd}}</td>
        </tr>
        <tr>
          <td class="w10">标准储备液浓度</td>
          <td class="w15">{{fggdjhc.bzwz}}</td>
          <td class="w20">标准储备液编号</td>
          <td class="w20">{{fggdjhc.bzcbybh}}</td>
          <td class="w15">标准物质CAS号</td>
          <td class="w15">{{fggdjhc.bzwz}}</td>
        </tr>

        <tr>
          <td class="tl w10">外观检查记录</td>
          <td class="tl" colspan="5">
           {{fggdjhc.wgjcjl}}
          </td>
        </tr>
        <tr>
          <td class="tl w10" colspan="6">核查结果：</td>
        </tr>
      </table>

      <table>
        <tr>
          <td class="w10" >加标倍数</td>
          <td class="w15">加标项目/量</td>
          <td class="w20">检测结果(抑制率)</td>
          <td class="w20">允差范围</td>
          <td class="w20">核查结果判定</td>
        </tr>
        <tr v-for="(item,index) in hcjl" :key="index">
          <td class="w5">
            <span v-if="index == 0 || index==1">0倍检出限加标测试</span>
            <span v-if="index == 2 || index==3">0.5倍检出限加标测试</span>
            <span v-if="index == 4 || index==5">1倍检出限加标测试</span>
          </td>
          <td class="w5">{{item.jbxm}}</td>
          <td class="w10">{{item.jcjg}}</td>
          <td class="w10">{{item.ycfw}}</td>
          <td class="w20">{{item.hcjgpd}}</td>
        </tr>

        <!-- <tr>
          <td class="tl w10" colspan="10">投入使用意见： □合格 &nbsp;&nbsp; □不合格&nbsp;&nbsp; □限用:&nbsp;&nbsp;
            □限用条件：</td>
        </tr> -->

        <tr>
          <td class="tl w10" colspan="10">
            <div class="flex">
              核查人：
              <img v-if="fggdjhc.tjrqm" :src="fggdjhc.tjrqm" class="avatar" />
              &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;
              <div class="flex">
                审核人 ：
                <img v-if="fggdjhc.tjrqm" :src="fggdjhc.tjrqm" class="avatar" />
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">
          注：针对“限用”的仪器设备应在“投入使用意见”栏目中明确规定使用时的“限用范围/限用要求”。
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import table from "../../views/util/table.vue";
export default {
  components: { table },
  name: "qctif07",
  data() {
    return {
      urlPath: this.getUrlPath(), //iframe src 路径
    };
  },
  created() {},
  props: {
    fggdjhc: null,
    hcjl: null,
  },
  methods: {
    print() {
      const printContents = document.getElementById("print").innerHTML;
      const originalContents = document.body.innerHTML;

      document.body.innerHTML = printContents;
      window.print();

     window.location.reload()
    },
  },
};
</script>

<style  scoped>
.title {
  text-align: center;
  position: relative;
}
table {
  border-collapse: collapse;
  width: 100%;
}
table th,
table td {
  padding: 5px;
  text-align: center;
  border: 1px solid #ddd;
}
table th {
  background-color: #f2f2f2;
  font-weight: bold;
  font-size: 1em;
}
.w15 {
  width: 15%;
}
.flex{
  display: flex;
}
.tf {
  text-align: left;
  font-weight: bold;
}
.tl {
  text-align: left;
}
.w20 {
  width: 20%;
}
.w10 {
  width: 10%;
}
.w5 {
  width: 5%;
}
img {
  width: 60px;
}
.print {
  width: 100px;
  background: #04a7b3;
  color: #fff;
  text-align: center;
  padding: 10px;
}
</style>
