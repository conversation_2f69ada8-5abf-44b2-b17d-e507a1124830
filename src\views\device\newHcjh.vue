<template>
  <basic-container back>
    <div id="print_box">
      <div class="card_title">基本信息</div>
      <el-form style="width: 100%" label-position="top" :inline="true" size="small" label-width="80px"
               :model="hcjh">
        <el-form-item label="核查年度">
          <el-date-picker
            style="width: 500px"
            v-model="hcjh.year"
            type="year"
            placeholder="选择年">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="编制日期">
          <el-date-picker
            style="width: 500px" v-model="hcjh.qjhcrq"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="设备管理员">
          <el-select style="width: 500px" v-model="hcjh.sbmc" placeholder="请选择">
            <el-option
              v-for="item in userList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="街道负责人">
          <el-select style="width: 500px" v-model="hcjh.sbmc" placeholder="请选择">
            <el-option
              v-for="item in userList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <table style="width: 100%">
        <thead>
        <tr style="font-size: 14px;color: #444444;font-weight: normal">
          <td rowspan="2">设备编号</td>
          <td rowspan="2">设备类型</td>
          <td rowspan="2">设备名称</td>
          <td rowspan="2">型号规格</td>
          <td rowspan="2">所属任务</td>
<!--          <td rowspan="2">设备校准日期</td>-->
          <td colspan="4">期间核查日期</td>
        </tr>
        <tr>
          <td>1</td>
          <td>2</td>
          <td>3</td>
          <td>4</td>
        </tr>
        </thead>
        <tbody>
        <tr style="font-size: 14px;color: #444444;font-weight: normal" v-for="(item,index) in deviceList" :key="index">
          <td>{{index + 1}}</td>
          <td>{{item.sbbh}}</td>
          <td>{{item.sblx}}</td>
          <td>{{item.sbmc}}</td>
          <td>{{item.rwmc}}</td>
          <td>{{item.sbxzsj}}</td>
          <td>
            <el-date-picker
              v-model="item.qjhcrq"
              type="date"
              size="small"
              style="width: 140px"
              placeholder="选择日期">
            </el-date-picker>
          </td>
          <td>
            <el-date-picker
              v-model="item.qjhcrq"
              type="date"
              size="small"
              style="width: 140px"
              placeholder="选择日期">
            </el-date-picker>
          </td>
          <td>
            <el-date-picker
              v-model="item.qjhcrq"
              type="date"
              size="small"
              style="width: 140px"
              placeholder="选择日期">
            </el-date-picker>
          </td>
          <td>
            <el-date-picker
              v-model="item.qjhcrq"
              type="date"
              size="small"
              style="width: 140px"
              placeholder="选择日期">
            </el-date-picker>
          </td>
        </tr>
        </tbody>
      </table>
      <el-row :gutter="20">
        <el-col :span="6">
          <div style="margin: 10px 0;font-size: 14px;color: #444444;">编制人员签名</div>
          <el-upload
            class="avatar-uploader"
            action="/api/manage/put-object"
            :limit="1"
            :file-list="bzSign"
            :on-success="handleAvatarSuccess">
            <img v-if="imageUrl" :src="imageUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-col>
        <el-col :span="6">
          <div style="margin: 10px 0;font-size: 14px;color: #444444;">审核人签名</div>
          <el-upload
            class="avatar-uploader"
            action="/api/manage/put-object"
            :limit="1"
            :file-list="shSign"
            :on-success="handleAvatarSuccess2">
            <img v-if="imageUrl2" :src="imageUrl2" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-col>
      </el-row>
    </div>
    <el-divider></el-divider>
    <div class="btn_box">
      <div>
        <el-button size="small">导出</el-button>
        <el-button style="margin-left: 15px" size="small" @click="showPrint">打印</el-button>
      </div>
      <el-button type="primary" size="small" @click="save">保存</el-button>
    </div>
  </basic-container>
</template>

<script>
  export default {
    name: "newQjhc",
    data() {
      return {
        hcjh:{},
        deviceList:[],
        bzSign: [],
        shSign: [],
        imageUrl: '',
        imageUrl2: '',
      }
    },
    mounted() {
      this.deviceList = JSON.parse(sessionStorage.getItem('qjhc_list'))
    },
    methods:{
      showPrint(){
        this.$Print('#test_print')
      },
      save(){
        this.$message.error('此处缺接口')
      },
      handleAvatarSuccess2(res, file) {
        console.log(1212,res)
        this.imageUrl2 = res.data;
      },
      handleAvatarSuccess(res, file) {
        console.log(1212,res)
        this.imageUrl = res.data;
        // this.infoOne.tjrqm = URL.createObjectURL(file.raw);
      },
    }
  }
</script>

<style>
  .avatar-uploader .el-upload {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 24px;
    color: #8c939d;
    width: 128px;
    height: 128px;
    line-height: 128px;
    text-align: center;
  }
  .avatar {
    width: 128px;
    height: 128px;
    display: block;
  }
</style>

<style scoped lang="less">

  .btn_box{
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
  }

  .info_title{
    padding: 15px 0;

  }
  .card_title{
    margin-top: 15px;
    padding-bottom: 15px;
    border-bottom: 2px dotted #ccc;
  }
  table,
  td,
  th {
    text-align: center;
    border: 1px solid #ccc;
    border-collapse: collapse;
  }

  table td {
    padding: 5px;
  }

  .out_box {
    padding: 15px;
    border: 1px solid #ccc;
    margin-top: 15px;
  }
</style>
