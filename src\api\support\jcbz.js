import request from '@/router/axios';
export const downLoadModel = () => {
  return request({
    url: '/api/jszc/jcbz/downLoadModel',
    method: 'get',
    responseType: 'blob',
    params: {
    }
  })
}
export const getList = (current, size, params) => {
  return request({
    url: '/api/jszc/jcbz/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/jszc/jcbz/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/jszc/jcbz/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/jszc/jcbz/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/jszc/jcbz/submit',
    method: 'post',
    data: row
  })
}

