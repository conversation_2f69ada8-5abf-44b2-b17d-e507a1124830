<template>
  <div class="avue-logo">
    <transition name="fade">
      <div v-if="keyCollapse"
           class="avue-logo_subtitle"
           key="0">
        <!--        {{website.logo}}-->
        <img style="width: 32px" :src="website.logo" alt="">
      </div>
    </transition>
    <transition-group name="fade">
      <template v-if="!keyCollapse">
        <span class="avue-logo_title big_logo_box"
              key="1">
          <img style="width: 26px;margin-right: 10px" :src="website.logo" alt="">
          {{website.indexTitle}}
        </span>
      </template>
    </transition-group>
  </div>
</template>

<script>
  import {mapGetters} from "vuex";

  export default {
    name: "logo",
    data() {
      return {};
    },
    created() {
    },
    computed: {
      ...mapGetters(["website", "keyCollapse"])
    },
    methods: {}
  };
</script>

<style lang="scss">
  .fade-leave-active {
    transition: opacity 0.2s;
  }

  .fade-enter-active {
    transition: opacity 2.5s;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  .avue-logo {
    position: fixed;
    top: 0;
    left: 0;
    width: 200px;
    height: 64px;
    line-height: 64px;
    background-color: #20222a;
    font-size: 20px;
    overflow: hidden;
    box-sizing: border-box;
    /*box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);*/
    color: rgba(255, 255, 255, 0.8);
    z-index: 1024;
    display: flex;
    align-items: center;
    justify-content: center;
    .big_logo_box{
      width: 200px;
      display: flex;
      align-items: center;
      font-size: 16px !important;
      padding-left: 40px;
    }
    &_title {
      display: block;
      text-align: center;
      font-weight: 300;
      font-size: 20px;
    }

    &_subtitle {
      text-align: center;
      font-size: 18px;
      font-weight: bold;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
</style>
