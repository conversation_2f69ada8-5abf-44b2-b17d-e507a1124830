export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchShow: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "采样id",
      prop: "cyid",
      type: "input",
    },
    {
      label: "采样编号",
      prop: "cybh",
      type: "input",
    },
    {
      label: "受检单位",
      prop: "sjdw",
      type: "input",
    },
    {
      label: "档口号",
      prop: "dkh",
      type: "input",
    },
    {
      label: "受检单位地址",
      prop: "sjdwdz",
      type: "input",
    },
    {
      label: "经营者姓名",
      prop: "jyzxm",
      type: "input",
    },
    {
      label: "证件号码",
      prop: "zjhm",
      type: "input",
    },
    {
      label: "联系电话",
      prop: "lxdh",
      type: "input",
    },
    {
      label: "溯源设备",
      prop: "sysb",
      type: "input",
    },
    {
      label: "分光空白值",
      prop: "fgkbz",
      type: "input",
    },
    {
      label: "总价",
      prop: "zj",
      type: "input",
    },
    {
      label: "大写金额",
      prop: "dxje",
      type: "input",
    },
    {
      label: "所处街道",
      prop: "scjd",
      type: "input",
    },
    {
      label: "抽样检测日期",
      prop: "cyjcrq",
      type: "input",
    },
    {
      label: "检测计数",
      prop: "jcjs",
      type: "input",
    },
    {
      label: "收据单号",
      prop: "sjdh",
      type: "input",
    },
    {
      label: "受检单位签名",
      prop: "sjdwqm",
      type: "input",
    },
    {
      label: "抽样员1签名",
      prop: "cyyoqm",
      type: "input",
    },
    {
      label: "抽样员2签名",
      prop: "cyytqm",
      type: "input",
    },
    {
      label: "不合格问题备注",
      prop: "bz",
      type: "input",
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
