
<template>
  <div>
    <div @click="print" class="print">打印</div>
    <div id="print">
      <h3 class="title">水浴锅/恒温培养箱期间核查记录表</h3>
      <table>
        <tr>
          <td class="w10">仪器编号：</td>
          <td class="w15">    {{ syghc.sbbh }}</td>
          <td class="w20">规格型号：</td>
          <td class="w20"> {{ syghc.ggxh }}</td>
          <td class="w15">核查依据：</td>
          <td class="w15"> {{ syghc.zcfg }}</td>
        </tr>
        <tr>
          <td class="w10">核查日期：</td>
          <td class="w15"> {{ syghc.qjhcrq }}</td>
          <td class="w20">环境温度：</td>
          <td class="w20">{{ syghc.hjwd }}</td>
          <td class="w15">核查用温度计编号</td>
          <td class="w15">   {{ syghc.wdjbh }}</td>
        </tr>
        <tr>
          <td class="w10">校准机构：     {{ syghc.xzjg }}</td>
          <td class="w15"></td>
          <td class="w20">校准证书编号</td>
          <td class="w20"> {{ syghc.xzzsbh }}</td>
          <td class="w15">校准日期</td>
          <td class="w15">  {{ syghc.wdjxzrq }}</td>
        </tr>
        <tr>
          <td class="tl w10">外观检查记录：</td>
          <td class="tl w10" colspan="5">{{ syghc.wgjcjl }}</td>
        </tr>
        <tr>
          <td class="tl w10">水浴锅/恒温培养箱质量核查记录：</td>
          <td class="tl w10"></td>
          <td class="tl w20">核查温度点：水浴锅80℃；恒温培养箱36℃</td>
          <td class="tl w10" colspan="3"></td>
        </tr>
      </table>
      <table>
        <tr>
          <td class="w5">核查位点</td>
          <td class="w10">核查点/uL</td>
          <td class="w5">第一次实测温度值(℃)</td>
          <td class="w5">第二次实测温度值(℃)</td>
          <td class="w5">第三次实测温度值(℃)</td>
          <td class="w5">温度波动度</td>
          <td class="w5">温度均匀度(℃)</td>
          <td class="w5">温度波动度允差(℃)</td>
          <td class="w5">温度均匀度允差(℃)</td>
        </tr>
        <tr v-for="(item, index) in allList">
          <td class="w5">{{ index + 1 }}</td>
          <td class="w5" rowspan="5" v-if="index == 0">
            <img src="./7-4.png" />
            <br />
            水浴锅/恒温培养箱上层核查位点
          </td>
          <td class="w5" rowspan="6" v-if="index == 5">
            <img src="./7-5.png" />
            <br />
            水浴锅/恒温培养箱下层核查位点
          </td>
          <td class="w5">        {{ item.wd1 }}</td>
          <td class="w5">  {{ item.wd2 }}</td>
          <td class="w5"> {{ item.wd3 }}</td>
          <td class="w5">  {{ Number(item.wdbdd).toFixed(2) }}</td>
          <td class="w5">       {{ Number(item.wdjyd).toFixed(2) }}</td>
          <td class="w5" rowspan="5" v-if="index == 0 || index == 5">±2</td>
          <td class="w5" rowspan="5" v-if="index == 0 || index == 5">2</td>
        </tr>
      </table>
      <table>
        <tr>
          <td class="tf w10" colspan="9">核查结论:</td>
        </tr>

        <tr>
          <td class="tl w10" colspan="10">
          {{ syghc.hcjl }}
          </td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">
            <div class="flex">
              核查人：
              <img v-if="syghc.tjrqm" :src="syghc.tjrqm" class="avatar" />
              &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;
              <div class="flex">
                审核人 ：
                <img v-if="syghc.tjrqm" :src="syghc.tjrqm" class="avatar" />
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td class="tf w10" colspan="10">备注：</td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">
            1、温度波动度=（Tmax-Tmin）/2(其中Tmax表示每层五个核查点中温度读数的最大值；Tmin表示每层五个核查点中温度读数的最小值）
          </td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">
            2、温度均匀度=【（第1个位点最大值-第1个位点最小值）+（第2个位点最大值-第2个位点最小值）+（第3个位点最大值-第3个位点最小值）+（第4个位点最大值-第4个位点最小值）+（第5个位点最大值-第5个位点最小值）】/15
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import table from "../../views/util/table.vue";
export default {
  components: { table },
  name: "qctif07",
  data() {
    return {

    };
  },

  props: {
    syghc: null,
    allList: null,
  },
  created() {
  },

  methods: {
    print() {
      const printContents = document.getElementById("print").innerHTML;
      const originalContents = document.body.innerHTML;

      document.body.innerHTML = printContents;
      window.print();

     window.location.reload()
    },
  },
};
</script>

<style  scoped>
.title {
  text-align: center;
  position: relative;
}
table {
  border-collapse: collapse;
  width: 100%;
}
table th,
table td {
  padding: 5px;
  text-align: center;
  border: 1px solid #ddd;
}
table th {
  background-color: #f2f2f2;
  font-weight: bold;
  font-size: 1em;
}
.flex{
  display: flex;
}
.w15 {
  width: 15%;
}
.tf {
  text-align: left;
  font-weight: bold;
}
.tl {
  text-align: left;
}
.w20 {
  width: 20%;
}
.w10 {
  width: 10%;
}
.w5 {
  width: 5%;
}
img {
  width: 60px;
}
.print {
  width: 100px;
  background: #04a7b3;
  color: #fff;
  text-align: center;
  padding: 10px;
}
</style>
