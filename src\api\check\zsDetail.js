import request from '@/router/axios';

export const getCyDetail = (id) => {
  return request({
    url: '/api/jc/cysj/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/jc/yxzsjl/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/jc/yxzsjl/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/jc/yxzsjl/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/jc/yxzsjl/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/jc/yxzsjl/submit',
    method: 'post',
    data: row
  })
}

