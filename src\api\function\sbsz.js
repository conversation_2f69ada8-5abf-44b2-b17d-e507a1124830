import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/gnsz/sbsz/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/gnsz/sbsz/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/gnsz/sbsz/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/gnsz/sbsz/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/gnsz/sbsz/submit',
    method: 'post',
    data: row
  })
}

