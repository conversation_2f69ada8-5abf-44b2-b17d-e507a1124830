<template>
  <basic-container :search="false">
    <div class="flex_main_box" v-loading="loading">
      <div v-for="(item,index) in iconList" @click="goRouter(item.lj)" class="flex_item_box hover_box" @mouseover="current=index" @mouseout="current=-1" :key="index">
        <div class="flex_item" :style="{background: item.bj}">
          <i style="color: #fff;font-size: 36px" :class="item.tbxz" ></i>
          <div class="menu_icon" v-show="current==index">
            <i class="el-icon-edit" @click.stop="edit(item)"></i>
            <i class="el-icon-delete" @click.stop="delTab(item)" style="margin-left: 15px"></i>
          </div>
        </div>
        <div class="flex_name">{{item.mc}}</div>
      </div>
      <div class="flex_item_box hover_box" @click="addForm">
        <div class="flex_item" :style="{background: '#ccc'}">
          <i class="fa fa-plus"  style="color: #fff;font-size: 36px" />
        </div>
        <div class="flex_name">新增</div>
      </div>
    </div>
    <el-dialog :title="isEdit?'编辑':'新增'" :visible.sync="dialogTableVisible" append-to-body>
      <avue-form :option="option" v-model="form" @submit="save">
        <template slot-scope="{disabled,size}" slot="text">
          <div class="flex_item_box" @click="dialogTableVisible=true">
            <div class="flex_item" :style="{background: form.bj}">
              <i :class="form.tbxz" style="color: #fff;font-size: 36px"/>
            </div>
            <div class="flex_name">{{form.mc}}</div>
          </div>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {mapGetters} from "vuex";
  import iconList from "@/config/iconList";
  import {getGztList,add,remove} from "@/api/desk/home";

  export default {
    name: "wel",
    data() {
      return {
        dialogTableVisible: false,
        isEdit: false,
        current: -1,
        iconList:[],
        option: {
          column:[
            {
              label: '名称',
              type: 'input',
              prop: 'mc',
              rules: [
                {
                  required: true,
                  message: "请输入名称",
                }
              ]
            },
            {
              label: '链接',
              prop: "lj",
              type: "tree",
              dicData: [],
              hide: true,
              addDisabled: false,
              cascader: ['mc'],
              parent: false,
              props: {
                label: "name",
                value: 'path',
              },
              rules: [
                {
                  required: true,
                  message: "请选择菜单链接",
                  trigger: "click"
                }
              ]
            },
            {
              label: '背景色',
              prop: 'bj',
              type: 'color',
              rules: [
                {
                  required: true,
                  message: "请选择背景色",
                  trigger: "click"
                }
              ]
            },
            {
              label: '图标',
              prop: 'tbxz',
              type: "icon",
              slot: true,
              iconList: iconList,
              rules: [
                {
                  required: true,
                  message: "请选择图标",
                  trigger: "click"
                }
              ]
            },
            {
              label: '预览',
              prop: 'text',
              formslot: true,
            }
          ]
        },
        form:{},
        loading:false,
      };
    },
    computed: {
      ...mapGetters(["userInfo","menu","menuId"]),
    },
    mounted() {
      this.onLoad()
      this.initData()
    },
    methods: {
      addForm(){
        this.form = {}
        this.dialogTableVisible=true
        this.isEdit = false
        console.log(333,this.form)
      },
      goRouter(path){
        this.$router.push(path)
      },
      edit(e){
        this.dialogTableVisible = true
        this.isEdit = true
        this.form = e
      },
      delTab(e){
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(e.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      save(){
        const row = this.form
        add(row).then(() => {
          this.onLoad();
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.dialogTableVisible = false
          this.form = {}
        }, error => {
          window.console.log(error);
        });
      },
      onLoad(){
        this.loading = true;
        getGztList(1, 9999).then(res => {
          const data = res.data.data;
          this.iconList = data.records;
          this.loading = false;
        });
      },
      initData() {
        const column = this.findObject(this.option.column, "lj");
        column.dicData =  this.menu;
      },
      handleChange(val) {
        window.console.log(val);
      }
    },
  };
</script>

<style lang="less" scoped>
  .flex_item_box{
    width: 200px;
    margin: 10px;
    cursor: pointer;
    transition: 0.3s;
    text-align: center;
  }
  .hover_box:hover{
    transform: scale(1.05);
  }
  .flex_name{
    width: 100%;
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
  }
  .flex_item{
    width: 200px;
    height: 130px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    position: relative;
  }
  .flex_main_box{
    width: 100%;
    display: flex;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;
    .menu_icon{
      position: absolute;
      top: 10px;
      right: 15px;
      color: #fff;
      cursor: pointer;
    }

  }
</style>

