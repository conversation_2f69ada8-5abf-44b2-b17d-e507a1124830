import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/xc/model/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
// 记录详情
export const getRecordDetail = (id) => {
  return request({
    url: '/api/xc/jl/detail',
    method: 'get',
    params: {
      id
    }
  })
}
// 部门详情
export const getDepart = (id) => {
  return request({
    url: '/api/blade-system/dept/detail',
    method: 'get',
    params: {
      id
    }
  })
}
// 类型详情
export const getType = () => {
  return request({
    url: '/api/blade-system/dict/dictionary?code=shop_type',
    method: 'get',
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/xc/model/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/xc/model/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/xc/model/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/xc/model/submit',
    method: 'post',
    data: row
  })
}

