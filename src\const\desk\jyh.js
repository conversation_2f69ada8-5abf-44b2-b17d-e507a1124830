export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  labelWidth: 120,
  searchLabelWidth:100,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  menuWidth: 280,
  align: 'center',
  column: [
    {
      label: "商户id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "商户名称",
      prop: "jyhmc",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "商户类型",
      prop: "jyhlx",
      type: "select",
      searchSpan:4,
      search: true,
      dicUrl: "/api/blade-system/dict/dictionary?code=bus_type",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "受检单位",
      prop: "scid",
      type: "select",
      filterable: true,
      dicUrl: "/api/sh/sczt/list?current=1&size=99999",
      props: {
        label: "scmc",
        value: "id",
        res: 'data.records'
      },
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "所属任务",
      prop: "rwid",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      parent:false,
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      search: true,
      width: 100,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "任务名称",
      prop: "rwmc",
      type: "input",
    },
    {
      label: "摊位号",
      prop: "twh",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "营业执照编号",
      prop: "yyzzh",
      type: "input",
      width: 100,
    },

    {
      label: "食品经营许可证",
      prop: "spjyxkz",
      type: "input",
      width: 100,
    },
    {
      label: "成立日期",
      prop: "clrq",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      searchSpan:4,
      search: true,
      searchRange:true,
    },
    {
      label: "法人姓名",
      prop: "frmc",
      type: "input",
    },
    {
      label: "商户地址",
      prop: "jydz",
      type: "input",
    },
    {
      label: "所属城市",
      prop: "sscs",
      type: "input",
    },
    {
      label: "主体类型",
      prop: "ztlx",
      type: "input",
    },
    {
      label: "许可项目",
      prop: "zyyw",
      type: "input",
    },
    {
      label: "诚信管理",
      prop: "cxgl",
      type: "select",
      search: true,
      searchSpan:4,
      width: '150px',
      dicData: [
        {
          label: '1分',
          value: '1',
        },
        {
          label: '2分',
          value: '2',
        },
        {
          label: '3分',
          value: '3',
        },
        {
          label: '4分',
          value: '4',
        },
        {
          label: '5分',
          value: '5',
        }
      ],
    },
    {
      label: "是否黑名单",
      prop: "hmd",
      type: "select",
      search: true,
      searchSpan:4,
      width: 100,
      dicData: [
        {
          label: '否',
          value: '否',
        },
        {
          label: '是',
          value: '是',
        },
      ],
    },
    {
      label: "营业执照照片",
      prop: "yyzzt",
      type: 'upload',
      listType: 'picture-card',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: false,
      hide: true,
      multiple: true,
    },
    {
      label: "食品经营许可证照片",
      prop: "spjyt",
      type: 'upload',
      dataType: 'string',
      listType: 'picture-card',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: false,
      hide: true,
      multiple: true,
    },

    {
      label: "商户二维码",
      prop: "qrcode",
      type: "upload",
      listType: 'picture-img',
      span: 12,
      width: 100,
      row: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },

    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
