import Layout from '@/page/index/'

export default [
  {
    path: '/operator/detail',
    name: '经营户详情页',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false
    },
    component: () => import( /* webpackChunkName: "views" */ '@/views/desk/operatorDetail')
  },
  {
    path: '/check/qrcode',
    name: '采样数据二维码',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false
    },
    component: () => import( /* webpackChunkName: "views" */ '@/views/check/cysj-qrcode')
  },
  {
    path: '/quality/myzz/tag',
    component: Layout,
    children: [{
      path: '',
      name: '样品标签制作',
      meta: {
        i18n: 'yxzs'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/quality/tag')
    }]
  },
  {
    path: '/patrol/xctj/newTemplate',
    component: Layout,
    children: [{
      path: '',
      name: '新增模板',
      meta: {
        i18n: 'yxzs'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/patrol/newTemplate')
    }]
  },
  {
    path: '/patrol/xctj/newTemplate',
    component: Layout,
    children: [{
      path: '',
      name: '新增模板',
      meta: {
        i18n: 'yxzs'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/patrol/newTemplate')
    }]
  },
  {
    path: '/patrol/xctj/new',
    component: Layout,
    children: [{
      path: '',
      name: '新增巡查',
      meta: {
        i18n: 'yxzs',
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/patrol/newPatrol')
    }]
  },
  {
    path: '/patrol/xctj/detail',
    component: Layout,
    children: [{
      path: '',
      name: '巡查详情',
      meta: {
        i18n: 'yxzs'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/patrol/detail')
    }]
  },
  {
    path: '/check/yxzs/detail',
    component: Layout,
    children: [{
      path: '',
      name: '追溯详情',
      meta: {
        i18n: 'yxzs'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/check/zsDetail')
    }]
  },
  {
    path: '/device/qjhc/add',
    component: Layout,
    children: [{
      path: '',
      name: '新增设备期间核查',
      meta: {
        i18n: 'qjhc'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/device/newQjhc')
    }]
  },
  {
    path: '/device/qjhc/detail',
    component: Layout,
    children: [{
      path: '',
      name: '设备期间核查详情',
      meta: {
        i18n: 'qjhc'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/device/qjhcDetail')
    }]
  },
  {
    path: '/device/qjhc/plan',
    component: Layout,
    children: [{
      path: '',
      name: '设备期间核查计划',
      meta: {
        i18n: 'qjhc'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/device/newHcjh')
    }]
  },
  {
  path: '/wel',
  component: Layout,
  redirect: '/wel/index',
  children: [{
    path: 'index',
    name: '首页',
    meta: {
      i18n: 'dashboard'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/wel/index')
  }, {
    path: 'dashboard',
    name: '控制台',
    meta: {
      i18n: 'dashboard',
      menu: false,
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/wel/dashboard')
  }]
}, {
  path: '/test',
  component: Layout,
  redirect: '/test/index',
  children: [{
    path: 'index',
    name: '测试页',
    meta: {
      i18n: 'test'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/util/test')
  }]
}, {
  path: '/dict-horizontal',
  component: Layout,
  redirect: '/dict-horizontal/index',
  children: [{
    path: 'index',
    name: '字典管理',
    meta: {
      i18n: 'dict'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/util/demo/dict-horizontal')
  }]
}, {
  path: '/dict-vertical',
  component: Layout,
  redirect: '/dict-vertical/index',
  children: [{
    path: 'index',
    name: '字典管理',
    meta: {
      i18n: 'dict'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/util/demo/dict-vertical')
  }]
}, {
  path: '/info',
  component: Layout,
  redirect: '/info/index',
  children: [{
    path: 'index',
    name: '个人信息',
    meta: {
      i18n: 'info'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/system/userinfo')
  }]
}, {
  path: '/work/process/leave',
  component: Layout,
  redirect: '/work/process/leave/form',
  children: [{
    path: 'form/:processDefinitionId',
    name: '请假流程',
    meta: {
      i18n: 'work'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/work/process/leave/form')
  }, {
    path: 'handle/:taskId/:processInstanceId/:businessId',
    name: '处理请假流程',
    meta: {
      i18n: 'work'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/work/process/leave/handle')
  }, {
    path: 'detail/:processInstanceId/:businessId',
    name: '请假流程详情',
    meta: {
      i18n: 'work'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/work/process/leave/detail')
  }]
}]
