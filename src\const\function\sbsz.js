export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchShow: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  columnBtn: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "设备编号",
      prop: "sbbh",
      type: "input",
    },
    {
      label: "设备名称",
      prop: "sbmc",
      type: "input",
      search: true,
    },
    {
      label: "设备类型",
      prop: "sblx",
      type: "select",
      dicData: [
        {
          label: '多功能检测仪',
          value: '1',
        },
        // {
        //   label: '监控设备',
        //   value: '2',
        // },
      ],
    },
    {
      label: "设备状态",
      prop: "sbzt",
      type: "select",
      dicData: [
        {
          label: '待校准',
          value: '1',
        },
        {
          label: '已校准',
          value: '2',
        },
        {
          label: '无需校准',
          value: '3',
        }
      ],
    },
    {
      label: "设备url",
      prop: "sburl",
      type: "input",
    },
    {
      label: "设备经纬度",
      prop: "coordinate",
      type: "input",
    },
    {
      label: "序列号",
      prop: "xlh",
      type: "input",
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
