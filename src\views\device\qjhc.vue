<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          v-if="permission.sbtj_add"
          @click="handleAdd">新 增
        </el-button>
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.qjhc_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button style="margin-left: 15px" type="text" icon="el-icon-date" size="mini" @click="showPlan">生成期间核查计划
        </el-button>

      </template>
      <template slot-scope="{row}" slot="hcxq">
        <el-button v-if="permission.qjhc_detail" type="text" size="mini" @click="showDetail(row)">查看详情</el-button>
      </template>
    </avue-crud>

    <el-dialog title="新增期间核查" width="90%" append-to-body :visible.sync="dialogVisible" :close-on-press-escape="false"
               fullscreen
               :close-on-click-modal="false">
      <div>
        <el-tabs type="border-card" style="margin-bottom: 10px">
          <el-tab-pane label="移液器">
            <div>
              <div class="card_title" style="margin-bottom: 10px">基本信息</div>
              <avue-form :option="yyqOption" v-model="yyqhc">
                <template #sbid-type="{item,value,label}">{{item}}</template>
              </avue-form>
              <div class="card_title">
                <span class="required_span">*</span>
                外观检测记录
              </div>
              <el-input style="margin-top: 10px" v-model="yyqhc.wg"
                        placeholder="请输入外观检测记录"></el-input>
              <div class="card_title">
                <span class="required_span">*</span>
                称量记录
              </div>
              <div class="info_title">称量记录（1）</div>
              <table>
                <thead>
                <tr>
                  <th>序号</th>
                  <th>核查点/μL</th>
                  <th>质量值/mg</th>
                  <th>温度/℃</th>
                  <th>K(T)值/cm3/g</th>
                  <th>V20实际容积值/μL</th>
                  <th>平均值/μL</th>
                  <th>测试相对误差/%</th>
                  <th>允许误差/%</th>
                  <th>标准偏差</th>
                  <th>测试重复性/%</th>
                  <th>要求重复性/%</th>
                  <th>单项核查结论</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in infoOne" :key="index">
                  <td>{{index + 1}}</td>
                  <td rowspan="6" v-if="index == 0">
                    <el-input-number :precision="0" :controls="false" style="width: 80px" size="mini"
                                     v-model="infoOneCheckPoint"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini" v-model="item.zlz"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number disabled :precision="1" :controls="false" style="width: 80px" size="mini" v-model="yyqhc.hjwd"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="6" :controls="false" style="width: 80px" size="mini" v-model="item.ktz"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <span v-if="item.zlz * item.ktz">{{(item.zlz * item.ktz).toFixed(3)}}</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoOneAverage.toFixed(2)}}</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoOneCspjwc.toFixed(2)}}%</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <el-input style="width: 80px" size="mini" v-model="infoOneAllowError" placeholder=""></el-input>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoOneStandardDev.toFixed(9)}}</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoOneTestRepeat.toFixed(2)}}%</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <el-input style="width: 80px" size="mini" v-model="infoOneRepeatRequire" placeholder=""></el-input>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <el-radio-group v-model="item.dxhcjl">
                      <div style="text-align: left;margin-bottom: 10px">
                        <el-radio label="合格">合格</el-radio>
                      </div>
                      <div>
                        <el-radio label="不合格">不合格</el-radio>
                      </div>
                    </el-radio-group>
                  </td>

                </tr>
                </tbody>
              </table>
              <div class="info_title">称量记录（2）</div>
              <table>
                <thead>
                <tr>
                  <th>序号</th>
                  <th>核查点/ul</th>
                  <th>质量值/mg</th>
                  <th>温度/℃</th>
                  <th>K(T)值/cm3/g</th>
                  <th>V20实际容积值/ul</th>
                  <th>平均值/ul</th>
                  <th>测试相对误差/%</th>
                  <th>允许误差/%</th>
                  <th>标准偏差</th>
                  <th>测试重复性/%</th>
                  <th>要求重复性/%</th>
                  <th>单项核查结论</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in infoTwo" :key="index">
                  <td>{{index + 1}}</td>
                  <td rowspan="6" v-if="index == 0">
                    <el-input-number :precision="0" :controls="false" style="width: 80px" size="mini"
                                     v-model="infoTwoCheckPoint"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini" v-model="item.zlz"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number disabled :precision="1" :controls="false" style="width: 80px" size="mini" v-model="yyqhc.hjwd"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="6" :controls="false" style="width: 80px" size="mini" v-model="item.ktz"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <span v-if="item.zlz * item.ktz">{{(item.zlz * item.ktz).toFixed(3)}}</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoTwoAverage.toFixed(2)}}</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoTwoCspjwc.toFixed(2)}}%</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <el-input style="width: 80px" size="mini" v-model="infoTwoAllowError" placeholder=""></el-input>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoTwoStandardDev.toFixed(9)}}</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoTwoTestRepeat.toFixed(2)}}%</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <el-input style="width: 80px" size="mini" v-model="infoTwoRepeatRequire" placeholder=""></el-input>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <el-radio-group v-model="item.dxhcjl">
                      <div style="text-align: left;margin-bottom: 10px">
                        <el-radio label="合格">合格</el-radio>
                      </div>
                      <div>
                        <el-radio label="不合格">不合格</el-radio>
                      </div>
                    </el-radio-group>
                  </td>

                </tr>
                </tbody>
              </table>
              <div class="info_title">称量记录（3）</div>
              <table>
                <thead>
                <tr>
                  <th>序号</th>
                  <th>核查点/ul</th>
                  <th>质量值/mg</th>
                  <th>温度/℃</th>
                  <th>K(T)值/cm3/g</th>
                  <th>V20实际容积值/ul</th>
                  <th>平均值/ul</th>
                  <th>测试相对误差/%</th>
                  <th>允许误差/%</th>
                  <th>标准偏差</th>
                  <th>测试重复性/%</th>
                  <th>要求重复性/%</th>
                  <th>单项核查结论</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in infoThree" :key="index">
                  <td>{{index + 1}}</td>
                  <td rowspan="6" v-if="index == 0">
                    <el-input-number :precision="0" :controls="false" style="width: 80px" size="mini"
                                     v-model="infoThreeCheckPoint"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini" v-model="item.zlz"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number disabled :precision="1" :controls="false" style="width: 80px" size="mini" v-model="yyqhc.hjwd"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="6" :controls="false" style="width: 80px" size="mini" v-model="item.ktz"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <span v-if="item.zlz * item.ktz">{{(item.zlz * item.ktz).toFixed(3)}}</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoThreeAverage.toFixed(2)}}</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoThreeCspjwc.toFixed(2)}}%</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <el-input style="width: 80px" size="mini" v-model="infoThreeAllowError" placeholder=""></el-input>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoThreeStandardDev.toFixed(9)}}</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>{{infoThreeTestRepeat.toFixed(2)}}%</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <el-input style="width: 80px" size="mini" v-model="infoThreeRepeatRequire"
                              placeholder=""></el-input>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <el-radio-group v-model="item.dxhcjl">
                      <div style="text-align: left;margin-bottom: 10px">
                        <el-radio label="合格">合格</el-radio>
                      </div>
                      <div>
                        <el-radio label="不合格">不合格</el-radio>
                      </div>
                    </el-radio-group>
                  </td>

                </tr>
                </tbody>
              </table>
              <div style="margin: 15px 0">
                <span class="required_span">*</span>
                核查结论
                <el-select style="margin-left: 10px" v-model="yyqhc.hcjl" placeholder="请选择" size="small">
                  <el-option label="合格" value="合格"></el-option>
                  <el-option label="不合格" value="不合格"></el-option>
                </el-select>
              </div>
              <el-row :gutter="20">
                <el-col :span="6">
                  <div style="margin: 10px 0">提交人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess"
                    :before-upload="beforeAvatarUpload">
                    <img v-if="yyqhc.tjrqm" :src="yyqhc.tjrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
                <el-col :span="6">
                  <div style="margin: 10px 0">核查人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess2"
                    :before-upload="beforeAvatarUpload">
                    <img v-if="yyqhc.hcrqm" :src="yyqhc.hcrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
                <el-col :span="6">
                  <div style="margin: 10px 0">审核人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess3"
                    :before-upload="beforeAvatarUpload">
                    <img v-if="yyqhc.shrqm" :src="yyqhc.shrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
              </el-row>
              <el-divider></el-divider>
              <div class="btn_box">
<!--                <el-button size="small" @click="saveYyq(1)">保存</el-button>-->
                <el-button style="margin-left: 20px;width: 300px" type="primary" @click="saveYyq(0)">提交</el-button>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="电子天平">
            <div>
              <div class="card_title" style="margin-bottom: 10px">基本信息</div>
              <avue-form :option="dztpOption" v-model="dztphc">
                <template #sbid-type="{item,value,label}">{{item}}</template>
              </avue-form>
              <div class="card_title">外观检测记录</div>
              <el-radio-group v-model="dztphc.wgjcjl" style="margin-top: 15px">
                <el-radio label="可以正常使用，外观符合要求">可以正常使用，外观符合要求</el-radio>
                <el-radio label="外观不符合要求">外观不符合要求</el-radio>
              </el-radio-group>
              <div class="card_title">称量记录</div>
              <div class="info_title">称量核查</div>
              <table>
                <thead>
                <tr>
                  <th>序号</th>
                  <th>核查点/g</th>
                  <th>天平读数（第1次）/g</th>
                  <th>天平读数（第2次）/g</th>
                  <th>天平读数（第3次）/g</th>
                  <th>天平读数（第4次）/g</th>
                  <th>平均值/g</th>
                  <th>相对误差/g</th>
                  <th>允差/g</th>
                  <th>内部校准结果</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in clhc" :key="index">
                  <td>{{index + 1}}</td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.hcd"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds1"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds2"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds3"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds4"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <span>{{((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4) / 4).toFixed(2)}}</span>
                  </td>
                  <td>
                    <span>{{((item.hcd - ((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4) / 4)) / ((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4) / 4)).toFixed(5)}}</span>
                  </td>
                  <td>
                    <el-input style="width: 80px" size="mini" v-model="item.yxwc" placeholder=""></el-input>
                  </td>
                  <td>
                    <el-radio-group v-model="item.nbxzjg">
                      <div style="text-align: left;margin-bottom: 10px">
                        <el-radio label="合格">合格</el-radio>
                      </div>
                      <div>
                        <el-radio label="不合格">不合格</el-radio>
                      </div>
                    </el-radio-group>
                  </td>
                </tr>
                </tbody>
              </table>
              <div class="info_title">偏载核验</div>
              <img src="../../assets/img/dztp.jpg" style="width: 500px" alt="">
              <div>
                <el-radio-group disable v-model="pzhy.type" style="margin-bottom: 15px">
                  <el-radio :label="3">方形偏载校准位置</el-radio>
                  <el-radio :label="6">圆形偏载校准位置</el-radio>
                  <el-radio :label="9">三角形偏载校准位置</el-radio>
                </el-radio-group>
              </div>
              <table>
                <thead>
                <tr>
                  <th>序号</th>
                  <th>偏载校准点/g</th>
                  <th>天平读数（第1次）/g</th>
                  <th>天平读数（第2次）/g</th>
                  <th>天平读数（第3次）/g</th>
                  <th>天平读数（第4次）/g</th>
                  <th>平均值/g</th>
                  <th>相对误差/g</th>
                  <th>允差/g</th>
                  <th>内部校准结果</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in pzhy" :key="index">
                  <td>{{index + 1}}</td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini" v-model="item.hcd"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds1"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds2"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds3"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds4"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <span>{{((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4) / 4).toFixed(2)}}</span>
                  </td>
                  <td>
                    <span>{{((item.hcd - ((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4) / 4)) / ((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4) / 4)).toFixed(5)}}</span>
                  </td>
                  <td>
                    <el-input style="width: 80px" size="mini" v-model="item.yxwc" placeholder=""></el-input>
                  </td>
                  <td>
                    <el-radio-group v-model="item.nbxzjg">
                      <div style="text-align: left;margin-bottom: 10px">
                        <el-radio label="合格">合格</el-radio>
                      </div>
                      <div>
                        <el-radio label="不合格">不合格</el-radio>
                      </div>
                    </el-radio-group>
                  </td>
                </tr>
                </tbody>
              </table>
              <div class="info_title">重复性核查（只核查一次）</div>
              <table>
                <thead>
                <tr>
                  <th>序号</th>
                  <th>重复性校准点/g</th>
                  <th>天平读数（第1次）/g</th>
                  <th>天平读数（第2次）/g</th>
                  <th>天平读数（第3次）/g</th>
                  <th>天平读数（第4次）/g</th>
                  <th>平均值/g</th>
                  <th>相对误差/g</th>
                  <th>允差/g</th>
                  <th>内部校准结果</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in cfxhc" :key="index">
                  <td>{{index + 1}}</td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini" v-model="item.hcd"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds1"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds2"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds3"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini"
                                     v-model="item.tpds4"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <span>{{((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4) / 4).toFixed(2)}}</span>
                  </td>
                  <td>
                    <span>{{((item.hcd - ((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4) / 4)) / ((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4) / 4)).toFixed(5)}}</span>
                  </td>
                  <td>
                    <el-input style="width: 80px" size="mini" v-model="item.yxwc" placeholder=""></el-input>
                  </td>
                  <td>
                    <el-radio-group v-model="item.nbxzjg">
                      <div style="text-align: left;margin-bottom: 10px">
                        <el-radio label="合格">合格</el-radio>
                      </div>
                      <div>
                        <el-radio label="不合格">不合格</el-radio>
                      </div>
                    </el-radio-group>
                  </td>
                </tr>
                </tbody>
              </table>
              <div style="margin: 15px 0">
                <span class="required_span">*</span>
                核查结论
                <el-select style="margin-left: 10px" v-model="dztphc.hcjl" placeholder="请选择" size="small">
                  <el-option label="合格" value="合格"></el-option>
                  <el-option label="不合格" value="不合格"></el-option>
                </el-select>
              </div>
              <el-row :gutter="20">
                <el-col :span="6">
                  <div style="margin: 10px 0">提交人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="uploadDztp1"
                    :before-upload="beforeAvatarUpload">
                    <img v-if="dztphc.tjrqm" :src="dztphc.tjrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
                <el-col :span="6">
                  <div style="margin: 10px 0">核查人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="uploadDztp2"
                    :before-upload="beforeAvatarUpload">
                    <img v-if="dztphc.hcrqm" :src="dztphc.hcrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
                <el-col :span="6">
                  <div style="margin: 10px 0">审核人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="uploadDztp3"
                    :before-upload="beforeAvatarUpload">
                    <img v-if="dztphc.shrqm" :src="dztphc.shrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
              </el-row>
              <el-divider></el-divider>
              <div class="btn_box">
<!--                <el-button size="small" @click="saveDztp(1)">保存</el-button>-->
                <el-button style="margin-left: 20px;width: 300px" type="primary" @click="saveDztp(0)">提交</el-button>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="水浴锅">
            <div>
              <div class="card_title" style="margin-bottom: 10px">基本信息</div>
              <avue-form :option="sygOption" v-model="syghc">
                <template #sbid-type="{item,value,label}">{{item}}</template>
              </avue-form>
              <div class="card_title">外观检测记录</div>
              <el-radio-group v-model="syghc.wgjcjl" style="margin-top: 15px">
                <el-radio label="可以正常使用，外观符合要求">可以正常使用，外观符合要求</el-radio>
                <el-radio label="外观不符合要求">外观不符合要求</el-radio>
              </el-radio-group>
              <div class="card_title">位点图</div>
              <img src="../../assets/img/syg.jpg" style="width: 300px" alt="">
              <div>水浴锅/恒温培养箱的可接受温度波动度和均匀度是+-2C</div>
              <div class="card_title">上层期间核查记录</div>
              <table style="margin-top: 15px">
                <thead>
                <tr>
                  <th>序号</th>
                  <th>第1次实测温度值℃</th>
                  <th>第2次实测温度值℃</th>
                  <th>第3次实测温度值℃</th>
                  <th>温度波动度℃</th>
                  <th>温度均匀度℃</th>
                  <th>温度波动度允差℃</th>
                  <th>温度均匀度允差℃</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in schc" :key="index">
                  <td>{{index + 1}}</td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini" v-model="item.wd1"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini" v-model="item.wd2"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini" v-model="item.wd3"
                                     placeholder=""></el-input-number>
                  </td>
                  <td rowspan="5" v-if="index == 0">
                    <el-input style="width: 80px" size="mini" v-model="topTemWave" placeholder=""></el-input>
                  </td>
                  <td rowspan="5" v-if="index == 0">
                    <el-input style="width: 80px" size="mini" v-model="topTemAve" placeholder=""></el-input>
                  </td>
                  <td rowspan="5" v-if="index == 0">
                    <span>±2℃</span>
                  </td>
                  <td rowspan="5" v-if="index == 0">
                    <span>±2℃</span>
                  </td>
                </tr>
                </tbody>
              </table>
              <div class="card_title">下层期间核查记录</div>
              <table style="margin-top: 15px">
                <thead>
                <tr>
                  <th>序号</th>
                  <th>第1次实测温度值℃</th>
                  <th>第2次实测温度值℃</th>
                  <th>第3次实测温度值℃</th>
                  <th>温度波动度℃</th>
                  <th>温度均匀度℃</th>
                  <th>温度波动度允差℃</th>
                  <th>温度均匀度允差℃</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in xchc" :key="index">
                  <td>{{index + 1}}</td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini" v-model="item.wd1"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini" v-model="item.wd2"
                                     placeholder=""></el-input-number>
                  </td>
                  <td>
                    <el-input-number :precision="2" :controls="false" style="width: 80px" size="mini" v-model="item.wd3"
                                     placeholder=""></el-input-number>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <el-input style="width: 80px" size="mini" v-model="bottomTemWave" placeholder=""></el-input>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <el-input style="width: 80px" size="mini" v-model="bottomTemAve" placeholder=""></el-input>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>±2℃</span>
                  </td>
                  <td rowspan="6" v-if="index == 0">
                    <span>±2℃</span>
                  </td>
                </tr>
                </tbody>
              </table>
              <div style="margin: 15px 0">
                <span class="required_span">*</span>
                核查结论
                <el-select style="margin-left: 10px" v-model="syghc.hcjl" placeholder="请选择" size="small">
                  <el-option label="合格" value="合格"></el-option>
                  <el-option label="不合格" value="不合格"></el-option>
                </el-select>
              </div>
              <el-row :gutter="20">
                <el-col :span="6">
                  <div style="margin: 10px 0">提交人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="uploadSyg1"
                    :before-upload="beforeAvatarUpload">
                    <img v-if="syghc.tjrqm" :src="syghc.tjrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
                <el-col :span="6">
                  <div style="margin: 10px 0">核查人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="uploadSyg2"
                    :before-upload="beforeAvatarUpload">
                    <img v-if="syghc.hcrqm" :src="syghc.hcrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
                <el-col :span="6">
                  <div style="margin: 10px 0">审核人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="uploadSyg3"
                    :before-upload="beforeAvatarUpload">
                    <img v-if="syghc.shrqm" :src="syghc.shrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
              </el-row>
              <el-divider></el-divider>
              <div class="btn_box">
<!--                <el-button size="small" @click="saveSyg(1)">保存</el-button>-->
                <el-button style="margin-left: 20px;width: 300px" type="primary" @click="saveSyg(0)">提交</el-button>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="分光光度计">
            <div>
              <div class="card_title" style="margin-bottom: 10px">基本信息</div>
              <avue-form :option="gdjOption" v-model="fggdjhc">
                <template #sbid-type="{item,value,label}">{{item}}</template>
              </avue-form>
              <div class="card_title">外观检测记录</div>
              <el-radio-group v-model="fggdjhc.wgjcjl" style="margin-top: 15px">
                <el-radio label="外观符合要求">外观符合要求</el-radio>
                <el-radio label="外观不符合要求">外观不符合要求</el-radio>
              </el-radio-group>
              <div class="card_title">分光光度计核查记录（每个加标倍数记录2次）</div>
              <table style="margin-top: 15px">
                <thead>
                <tr>
                  <th>序号</th>
                  <th>加标倍数</th>
                  <th>加标项目</th>
                  <th>检测结果（抑制率）</th>
                  <th>允差范围</th>
                  <th>核查结果判定</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in hcjl" :key="index">
                  <td>{{index + 1}}</td>
                  <td>
                    <span v-if="index == 0 || index==1">0倍检出限加标测试</span>
                    <span v-if="index == 2 || index==3">0.5倍检出限加标测试</span>
                    <span v-if="index == 4 || index==5">1倍检出限加标测试</span>
                  </td>
                  <td>
                    <el-input style="width: 80px" size="mini" v-model="item.jbxm"
                                     placeholder=""></el-input>
                  </td>
                  <td>
                    <el-input style="width: 80px" size="mini" v-model="item.jcjg"
                              placeholder=""></el-input>
                  </td>
                  <td>
                    <el-input style="width: 80px" size="mini" v-model="item.ycfw"
                              placeholder=""></el-input>
                  </td>
                  <td>
                    <el-radio-group v-model="item.hcjgpd">
                      <div style="text-align: left;margin-bottom: 5px">
                        <el-radio label="符合">符合</el-radio>
                      </div>
                      <div style="text-align: left;margin-bottom: 5px">
                        <el-radio label="不符合">不符合</el-radio>
                      </div>
                      <div>
                        <el-radio label="部分符合">部分符合</el-radio>
                      </div>
                    </el-radio-group>
                  </td>
                </tr>
                </tbody>
              </table>
              <div style="margin: 15px 0">
                <span class="required_span">*</span>
                核查结论
                <el-select style="margin-left: 10px" v-model="fggdjhc.hcjl" placeholder="请选择" size="small">
                  <el-option label="合格" value="合格"></el-option>
                  <el-option label="限用" value="限用"></el-option>
                  <el-option label="停用" value="停用"></el-option>
                </el-select>
              </div>
              <el-row :gutter="20">
                <el-col :span="6">
                  <div style="margin: 10px 0">提交人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="uploadGdj1"
                    :before-upload="beforeAvatarUpload">
                    <img v-if="fggdjhc.tjrqm" :src="fggdjhc.tjrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
                <el-col :span="6">
                  <div style="margin: 10px 0">核查人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="uploadGdj2">
                    <img v-if="fggdjhc.hcrqm" :src="fggdjhc.hcrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
                <el-col :span="6">
                  <div style="margin: 10px 0">审核人签名</div>
                  <el-upload
                    class="avatar-uploader"
                    action="/api/manage/put-object"
                    :show-file-list="false"
                    :on-success="uploadGdj3">
                    <img v-if="fggdjhc.shrqm" :src="fggdjhc.shrqm" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-col>
              </el-row>
              <el-divider></el-divider>
              <div class="btn_box">
<!--                <el-button size="small">保存</el-button>-->
                <el-button style="margin-left: 20px;width: 300px" type="primary" @click="saveGdj(0)">提交</el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
     <!-- <div class="box">
      <div class="dn">下载</div>
      <div class="sx">刷新</div>
      <div class="cx">查询</div>
    </div> -->
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/device/qjhc";
  import {getDeviceList, yyqSubmit, dztpSubmit, sygSubmit, fggdjSubmit} from "@/api/device/newQjhc";
  import option from "@/const/device/qjhc";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        detailShow: false,

        dialogVisible: false,
        yyqList: [],
        dztpList: [],
        sygList: [],
        gdjList: [],
        deviceList: [],
        yyqOption: {
          labelWidth: '80px',
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: '设备名称',
              prop: 'sbid',
              type: 'select',
              filterable: true,
              props: {
                label: 'sbmc',
                value: 'id',
                res: 'data.records'
              },
              dicUrl: '/api/sb/sbtj/page?current=1&size=99999&bzyqmc=1',
              typeformat (item, label, value) {
                return `${item['sbmc']}-${item['sbbh']}`
              },
              change: (e) => {
                this.yyqList.map(item => {
                  if (item.id == e.value) {
                    this.yyqhc.sbbh = item.sbbh
                    this.yyqhc.ggxh = item.ggxh
                    this.yyqhc.sbgly = item.sbgly
                    this.yyqhc.sbmc = item.sbmc
                  }
                })
              },
              rules: [{
                required: true,
                message: "请选择",
                trigger: "blur"
              }],
            },
            {
              label: '设备编号',
              prop: 'sbbh',
              type: 'input',
              disabled: true,
            },
            {
              label: '规格型号',
              prop: 'ggxh',
              type: 'input',
              disabled: true,
            },
            {
              label: '设备管理员',
              prop: 'sbgly',
              type: 'select',
              dicUrl: "/api/blade-user/page?current=1&size=100000&deptId=",
              props: {
                label: "name",
                value: "id",
                res: 'data.records'
              },
              disabled: true,
            },
            // {
            //   label: '设备校准日期',
            //   prop: 'sbxzsj',
            //   type: "date",
            //   valueFormat: "yyyy-MM-dd",
            //   rules: [{
            //     required: true,
            //     message: "请选择",
            //     trigger: "blur"
            //   }],
            // },
            {
              label: '期间核查日期',
              prop: 'qjhcrq',
              type: "date",
              valueFormat: "yyyy-MM-dd",
              rules: [{
                required: true,
                message: "请选择",
                trigger: "blur"
              }],
            },
            {
              label: '期间核查依据',
              prop: 'zcfg',
              type: 'input',
              rules: [{
                required: true,
                message: "请输入",
                trigger: "blur"
              }],
            },
            {
              label: '街道负责人',
              prop: 'jdfzr',
              type: 'input',
              rules: [{
                required: true,
                message: "请输入",
                trigger: "blur"
              }],
            },
            {
              label: '核查用电子天平编号',
              prop: 'dztpbh',
              type: 'input',
            },
            {
              label: '电子天平校准日期',
              prop: 'dztpxzsj',
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },
            {
              label: '天平精密度(mg)',
              prop: 'dztpjd',
              type: 'number',
              controls: false,
              precision: 2,
            },
            {
              label: '环境温度(℃)',
              prop: 'hjwd',
              type: 'number',
              controls: false,
              precision: 2,
            },
            {
              label: '环境湿度(%rh)',
              prop: 'hjsd',
              type: 'number',
              controls: false,
              precision: 2,
            },
          ]
        },
        yyqhc: {},
        infoOneCheckPoint: null,
        infoOneAllowError: null,
        infoOneRepeatRequire: null,
        infoOne: [
          {
            jlgs: 1,
            sort: 1,
            hcd: '',
            yxwc: '±4.0%',
          },
          {jlgs: 1, sort: 2},
          {jlgs: 1, sort: 3},
          {jlgs: 1, sort: 4},
          {jlgs: 1, sort: 5},
          {jlgs: 1, sort: 6},
        ],
        infoTwoCheckPoint: null,
        infoTwoAllowError: null,
        infoTwoRepeatRequire: null,
        infoTwo: [
          {jlgs: 2, sort: 1},
          {jlgs: 2, sort: 2},
          {jlgs: 2, sort: 3},
          {jlgs: 2, sort: 4},
          {jlgs: 2, sort: 5},
          {jlgs: 2, sort: 6},
        ],
        infoThreeCheckPoint: null,
        infoThreeAllowError: null,
        infoThreeRepeatRequire: null,
        infoThree: [
          {jlgs: 3, sort: 1},
          {jlgs: 3, sort: 2},
          {jlgs: 3, sort: 3},
          {jlgs: 3, sort: 4},
          {jlgs: 3, sort: 5},
          {jlgs: 3, sort: 6},
        ],
        dztpOption:{
          labelWidth: '80px',
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: '设备名称',
              prop: 'sbid',
              type: 'select',
              filterable: true,
              props: {
                label: 'sbmc',
                value: 'id',
                res: 'data.records'
              },
              typeformat (item, label, value) {
                return `${item['sbmc']}-${item['sbbh']}`
              },
              dicUrl: '/api/sb/sbtj/page?current=1&size=99999&bzyqmc=2',
              change: (e) => {
                this.dztpList.map(item => {
                  if (item.id == e.value) {
                    this.dztphc.sbbh = item.sbbh
                    this.dztphc.ggxh = item.ggxh
                    this.dztphc.sbgly = item.sbgly
                    this.dztphc.sbmc = item.sbmc
                  }
                })
              },
              rules: [{
                required: true,
                message: "请选择",
                trigger: "blur"
              }],
            },
            {
              label: '设备编号',
              prop: 'sbbh',
              type: 'input',
              disabled: true,
            },
            {
              label: '设备管理员',
              prop: 'sbgly',
              type: 'select',
              dicUrl: "/api/blade-user/page?current=1&size=100000&deptId=",
              props: {
                label: "name",
                value: "id",
                res: 'data.records'
              },
              disabled: true,
            },
            {
              label: '规格型号',
              prop: 'ggxh',
              type: 'input',
              disabled: true,
            },
            // {
            //   label: '设备校准日期',
            //   prop: 'sbxzsj',
            //   type: "date",
            //   valueFormat: "yyyy-MM-dd",
            //   rules: [{
            //     required: true,
            //     message: "请选择",
            //     trigger: "blur"
            //   }],
            // },
            {
              label: '期间核查依据',
              prop: 'zcfg',
              type: 'input',
              rules: [{
                required: true,
                message: "请输入",
                trigger: "blur"
              }],
            },
            {
              label: '期间核查日期',
              prop: 'qjhcrq',
              type: "date",
              valueFormat: "yyyy-MM-dd",
              rules: [{
                required: true,
                message: "请选择",
                trigger: "blur"
              }],
            },
            // {
            //   label: '街道负责人',
            //   prop: 'jdfzr',
            //   type: 'input',
            //   rules: [{
            //     required: true,
            //     message: "请输入",
            //     trigger: "blur"
            //   }],
            // },
            {
              label: '环境温度(℃)',
              prop: 'hjwd',
              type: 'number',
              controls: false,
              precision: 2,
            },
            {
              label: '环境湿度(%rh)',
              prop: 'hjsd',
              type: 'number',
              controls: false,
              precision: 2,
            },
            {
              label: '核查用砝码编号',
              prop: 'fmbh',
              type: 'input',
            },
            {
              label: '砝码检定证书编号',
              prop: 'fmjdzsbh',
              type: 'input',
            },
            {
              label: '砝码检定日期',
              prop: 'fmjdrq',
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },

          ]
        },
        dztphc: {},
        clhc: [
          {jlgs: 1,sort: 1},
          {jlgs: 1,sort: 2},
          {jlgs: 1,sort: 3},
          {jlgs: 1,sort: 4},
          {jlgs: 1,sort: 5},
          {jlgs: 1,sort: 6},
          {jlgs: 1,sort: 7},
          {jlgs: 1,sort: 8},
          {jlgs: 1,sort: 9},
          {jlgs: 1,sort: 10},
          ],
        pzhy: [{jlgs: 2, sort: 1}, {jlgs: 2, sort: 2}, {jlgs: 2, sort: 2}, {jlgs: 2, sort: 4}, {jlgs: 2, sort: 5}],
        cfxhc: [{jlgs: 3}],
        sygOption:{
          labelWidth: '80px',
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: '设备名称',
              prop: 'sbid',
              type: 'select',
              filterable: true,
              props: {
                label: 'sbmc',
                value: 'id',
                res: 'data.records'
              },
              dicUrl: '/api/sb/sbtj/page?current=1&size=99999&bzyqmc=3',
              typeformat (item, label, value) {
                return `${item['sbmc']}-${item['sbbh']}`
              },
              change: (e) => {
                this.sygList.map(item => {
                  if (item.id == e.value) {
                    this.syghc.sbbh = item.sbbh
                    this.syghc.ggxh = item.ggxh
                    this.syghc.sbgly = item.sbgly
                    this.syghc.sbmc = item.sbmc
                  }
                })
              },
              rules: [{
                required: true,
                message: "请选择",
                trigger: "blur"
              }],
            },
            {
              label: '设备编号',
              prop: 'sbbh',
              type: 'input',
              disabled: true,
            },
            {
              label: '设备管理员',
              prop: 'sbgly',
              type: 'select',
              dicUrl: "/api/blade-user/page?current=1&size=100000&deptId=",
              props: {
                label: "name",
                value: "id",
                res: 'data.records'
              },
              disabled: true,
            },
            {
              label: '规格型号',
              prop: 'ggxh',
              type: 'input',
              disabled: true,
            },
            // {
            //   label: '设备校准日期',
            //   prop: 'sbxzsj',
            //   type: "date",
            //   valueFormat: "yyyy-MM-dd",
            //   rules: [{
            //     required: true,
            //     message: "请选择",
            //     trigger: "blur"
            //   }],
            // },
            {
              label: '期间核查依据',
              prop: 'zcfg',
              type: 'input',
              rules: [{
                required: true,
                message: "请输入",
                trigger: "blur"
              }],
            },
            {
              label: '期间核查日期',
              prop: 'qjhcrq',
              type: "date",
              valueFormat: "yyyy-MM-dd",
              rules: [{
                required: true,
                message: "请选择",
                trigger: "blur"
              }],
            },
            {
              label: '环境温度(℃)',
              prop: 'hjwd',
              type: 'number',
              controls: false,
              precision: 2,
            },
            {
              label: '环境湿度(%rh)',
              prop: 'hjsd',
              type: 'number',
              controls: false,
              precision: 2,
            },
            {
              label: '核查用温度计编号',
              prop: 'wdjbh',
              type: 'input',
            },
            {
              label: '校准机构',
              prop: 'xzjg',
              type: 'input',
            },
            {
              label: '校准证书编号',
              prop: 'xzzsbh',
              type: 'input',
            },
            {
              label: '校准日期',
              prop: 'wdjxzrq',
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },

          ]
        },
        syghc: {},
        schc: [{jlgs: 1,sort: 1}, {jlgs: 1,sort: 2}, {jlgs: 1,sort: 3}, {jlgs: 1,sort: 4}, {jlgs: 1,sort: 5}],
        xchc: [{jlgs: 2,sort: 1}, {jlgs: 2,sort: 2}, {jlgs: 2,sort: 3}, {jlgs: 2,sort: 4}, {jlgs: 2,sort: 5}],
        gdjOption:{
          labelWidth: '80px',
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: '设备名称',
              prop: 'sbid',
              type: 'select',
              filterable: true,
              props: {
                label: 'sbmc',
                value: 'id',
                res: 'data.records'
              },
              dicUrl: '/api/sb/sbtj/page?current=1&size=99999&bzyqmc=4',
              typeformat (item, label, value) {
                return `${item['sbmc']}-${item['sbbh']}`
              },
              change: (e) => {
                this.gdjList.map(item => {
                  if (item.id == e.value) {
                    this.fggdjhc.sbbh = item.sbbh
                    this.fggdjhc.ggxh = item.ggxh
                    this.fggdjhc.sbmc = item.sbmc
                  }
                })
              },
              rules: [{
                required: true,
                message: "请选择",
                trigger: "blur"
              }],
            },
            {
              label: '设备编号',
              prop: 'sbbh',
              type: 'input',
              disabled: true,
            },
            {
              label: '规格型号',
              prop: 'ggxh',
              type: 'input',
              disabled: true,
            },
            // {
            //   label: '设备管理员',
            //   prop: 'sbgly',
            //   type: 'select',
            //   dicUrl: "/api/blade-user/page?current=1&size=100000&deptId=",
            //   props: {
            //     label: "name",
            //     value: "id",
            //     res: 'data.records'
            //   },
            //   disabled: true,
            // },

            // {
            //   label: '设备校准日期',
            //   prop: 'sbxzsj',
            //   type: "date",
            //   valueFormat: "yyyy-MM-dd",
            //   rules: [{
            //     required: true,
            //     message: "请选择",
            //     trigger: "blur"
            //   }],
            // },
            {
              label: '期间核查依据',
              prop: 'zcfg',
              type: 'input',
              rules: [{
                required: true,
                message: "请输入",
                trigger: "blur"
              }],
            },
            {
              label: '期间核查日期',
              prop: 'qjhcrq',
              type: "date",
              valueFormat: "yyyy-MM-dd",
              rules: [{
                required: true,
                message: "请选择",
                trigger: "blur"
              }],
            },
            {
              label: '环境温度(℃)',
              prop: 'hjwd',
              type: 'number',
              controls: false,
              precision: 2,
            },
            {
              label: '环境湿度(%rh)',
              prop: 'hjsd',
              type: 'number',
              controls: false,
              precision: 2,
            },
            {
              label: '标准储备液浓度',
              prop: 'bzcbynd',
              type: 'input',
            },
            {
              label: '标准储备液编号',
              prop: 'bzcbybh',
              type: 'input',
            },
            {
              label: '标准物质CAS号',
              prop: 'bzwz',
              type: 'input',
            },
            {
              label: '工作液浓度',
              prop: 'gzynd',
              type: 'input',
            },
          ]
        },
        fggdjhc: {},
        hcjl: [{jlgs: 1,sort: 1, ycfw: '<50%'}, {jlgs: 1,sort: 2, ycfw: '<50%'}, {jlgs: 1,sort: 3, ycfw: '<50%'}, {jlgs: 1,sort: 4, ycfw: '<50%'}, {jlgs: 1,sort: 5, ycfw: '≥50%'}, {jlgs: 1,sort: 6, ycfw: '≥50%'}],
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        // export excel, avue reconfig
        option.excelBtn = this.vaildData(this.permission.qjhc_export, false);

        return {
          addBtn: this.vaildData(this.permission.sbtj_add, false),
          viewBtn: this.vaildData(this.permission.sbtj_view, false),
          delBtn: this.vaildData(this.permission.sbtj_delete, false),
          editBtn: this.vaildData(this.permission.sbtj_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.hcid);
        });
        return ids.join(",");
      },
      //移液器测量一
      infoOneAverage() {
        let a = 0;
        this.infoOne.map(item => {
          if (item.zlz * item.ktz) {
            a += Number(item.zlz * item.ktz)
          }
        })
        return (a / 6)
      },
      infoOneCspjwc() {
        return (this.infoOneCheckPoint - this.infoOneAverage) * 100 / this.infoOneAverage
      },
      infoOneStandardDev() {
        const list = this.infoOne.map(item => {
          if (item.zlz * item.ktz) {
            return item.zlz * item.ktz
          } else {
            return 0
          }
        })
        return this.standardDeviation(list, true)
      },
      infoOneTestRepeat() {
        return (this.infoOneStandardDev / this.infoOneAverage) * 100
      },
      //移液器测量二
      infoTwoAverage() {
        let a = 0;
        this.infoTwo.map(item => {
          if (item.zlz * item.ktz) {
            a += Number(item.zlz * item.ktz)
          }
        })
        return (a / 6)
      },
      infoTwoCspjwc() {
        return (this.infoTwoCheckPoint - this.infoTwoAverage) * 100 / this.infoTwoAverage
      },
      infoTwoStandardDev() {
        const list = this.infoTwo.map(item => {
          if (item.zlz * item.ktz) {
            return item.zlz * item.ktz
          } else {
            return 0
          }
        })
        return this.standardDeviation(list, true)
      },
      infoTwoTestRepeat() {
        return (this.infoTwoStandardDev / this.infoTwoAverage) * 100
      },
      //移液器测量三
      infoThreeAverage() {
        let a = 0;
        this.infoThree.map(item => {
          if (item.zlz * item.ktz) {
            a += Number(item.zlz * item.ktz)
          }
        })
        return (a / 6)
      },
      infoThreeCspjwc() {
        return (this.infoThreeCheckPoint - this.infoThreeAverage) * 100 / this.infoThreeAverage
      },
      infoThreeStandardDev() {
        const list = this.infoThree.map(item => {
          if (item.zlz * item.ktz) {
            return item.zlz * item.ktz
          } else {
            return 0
          }
        })
        return this.standardDeviation(list, true)
      },
      infoThreeTestRepeat() {
        return (this.infoThreeStandardDev / this.infoThreeAverage) * 100
      },

      //水浴锅
      topTemWave(){
        let wdList = []
        this.schc.map(function(o) {
          wdList.push(o.wd1 || 0)
          wdList.push(o.wd2 || 0)
          wdList.push(o.wd3 || 0)

        })
        const maxWd = Math.max.apply(Math, wdList)
        const minWd = Math.min.apply(Math, wdList)
        return (maxWd - minWd)/2
      },
      topTemAve(){
        let wdDiff = []
        this.schc.map(function(o) {
          wdDiff.push(Math.max(o.wd1,o.wd2,o.wd3) - Math.min(o.wd1,o.wd2,o.wd3) || 0)
        })
        const wdTotal = wdDiff.reduce((n,m)=> n + m)
        return wdTotal
      },
      bottomTemWave(){
        let wdList = []
        this.xchc.map(function(o) {
          wdList.push(o.wd1 || 0)
          wdList.push(o.wd2 || 0)
          wdList.push(o.wd3 || 0)

        })
        const maxWd = Math.max.apply(Math, wdList)
        const minWd = Math.min.apply(Math, wdList)
        return (maxWd - minWd)/2
      },
      bottomTemAve(){
        let wdDiff = []
        this.xchc.map(function(o) {
          wdDiff.push(Math.max(o.wd1,o.wd2,o.wd3) - Math.min(o.wd1,o.wd2,o.wd3) || 0)
        })
        const wdTotal = wdDiff.reduce((n,m)=> n + m)
        return wdTotal
      },
    },
    mounted() {
      this.getDevice()
    },
    methods: {
      standardDeviation(arr, usePopulation = false) {
        const mean = arr.reduce((acc, val) => acc + val, 0) / arr.length;
        return Math.sqrt(
          arr.reduce((acc, val) => acc.concat((val - mean) ** 2), []).reduce((acc, val) => acc + val, 0) /
          (arr.length - (usePopulation ? 0 : 1))
        );
      },
      saveYyq(e) {
        const infoOne = this.infoOne.map(item => {
          return {
            ...item,
            wd: this.yyqhc.hjwd,
            hcd: this.infoOneCheckPoint,
            cscfx: this.infoOneTestRepeat,
            csxdwc: this.infoOneCspjwc,
            jsbzpc: this.infoOneStandardDev,
            yqcfx: this.infoOneRepeatRequire,
            yxwc: this.infoOneAllowError,
            pjz: this.infoOneAverage,
            vsj: item.zlz * item.ktz
          }
        })
        const infoTwo = this.infoTwo.map(item => {
          return {
            ...item,
            wd: this.yyqhc.hjwd,
            hcd: this.infoTwoCheckPoint,
            cscfx: this.infoTwoTestRepeat,
            csxdwc: this.infoTwoCspjwc,
            jsbzpc: this.infoTwoStandardDev,
            yqcfx: this.infoTwoRepeatRequire,
            yxwc: this.infoTwoAllowError,
            pjz: this.infoTwoAverage,
            vsj: item.zlz * item.ktz
          }
        })
        const infoThree = this.infoThree.map(item => {
          return {
            ...item,
            wd: this.yyqhc.hjwd,
            hcd: this.infoThreeCheckPoint,
            cscfx: this.infoThreeTestRepeat,
            csxdwc: this.infoThreeCspjwc,
            jsbzpc: this.infoThreeStandardDev,
            yqcfx: this.infoThreeRepeatRequire,
            yxwc: this.infoThreeAllowError,
            pjz: this.infoThreeAverage,
            vsj: item.zlz * item.ktz
          }
        })
        if(!this.yyqhc.hcjl){
          this.$message.error('请完善必填项后提交')
          return;
        }
        yyqSubmit({
          infoOne: infoOne,
          infoTwo: infoTwo,
          infoThree: infoThree,
          yyqhc: {
            ...this.yyqhc,
            status: e
          },
        }).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.refreshChange()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      },

      getDeviceDetail(id) {
        getDetail(id).then(res => {
        });
      },
      saveDztp(e) {
        const clhc = this.clhc.map(item=>{
          return {
            ...item,
            pjz: ((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4)/4).toFixed(2),
            wc: ((item.hcd - (item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4)/4 ) / (item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4)/4).toFixed(5)
          }
        })
        const cfxhc = this.cfxhc.map(item=>{
          return {
            ...item,
            pjz: ((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4)/4).toFixed(2),
            wc: ((item.hcd - (item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4)/4 ) / (item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4)/4).toFixed(5)
          }
        })
        const pzhy = this.pzhy.map(item=>{
          return {
            ...item,
            pjz: ((item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4)/4).toFixed(2),
            wc: ((item.hcd - (item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4)/4 ) / (item.tpds1 + item.tpds2 + item.tpds3 + item.tpds4)/4).toFixed(5)
          }
        })
        if(!this.dztphc.hcjl){
          this.$message.error('请完善必填项后提交')
          return;
        }
        dztpSubmit({
          dztphc: {
            ...this.dztphc,
            status: e
          },
          cfxhc: cfxhc,
          clhc: clhc,
          pzhy: pzhy,
        }).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.refreshChange()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      },
      saveSyg(e) {
        if(!this.syghc.hcjl){
          this.$message.error('请完善必填项后提交')
          return;
        }
        const schc = this.schc.map(item=>{
          return {
            ...item,
            wdbdd: this.topTemWave,
            wdjyd: this.topTemAve,
          }
        })
        const xchc = this.xchc.map(item=>{
          return {
            ...item,
            wdbdd: this.bottomTemWave,
            wdjyd: this.bottomTemAve,
          }
        })
        sygSubmit({
          syghc: {
            ...this.syghc,
            status: e
          },
          schc: schc,
          xchc: xchc,
        }).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.refreshChange()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      },
      saveGdj(e) {
        if(!this.fggdjhc.hcjl){
          this.$message.error('请完善必填项后提交')
          return;
        }
        fggdjSubmit({
          fggdjhc: {
            ...this.fggdjhc,
            status: e
          },
          hcjl: this.hcjl,
        }).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.refreshChange()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      },
      changeDevice(e) {
        this.getDeviceDetail(e)
      },
      getDevice() {
        getDeviceList(1, 100000, {
          bzyqmc: '1'
        }).then(res => {
          this.yyqList = res.data.data.records
        })
        getDeviceList(1, 100000, {
          bzyqmc: '2'
        }).then(res => {
          this.dztpList = res.data.data.records
        })
        getDeviceList(1, 100000, {
          bzyqmc: '3'
        }).then(res => {
          this.sygList = res.data.data.records
        })
        getDeviceList(1, 100000, {
          bzyqmc: '4'
        }).then(res => {
          this.gdjList = res.data.data.records
        })
      },
      handleAvatarSuccess3(res, file) {
        this.yyqhc = {
          ...this.yyqhc,
          shrqm: res.data
        };
      },
      handleAvatarSuccess2(res, file) {
        this.yyqhc = {
          ...this.yyqhc,
          hcrqm: res.data
        };
      },
      handleAvatarSuccess(res, file) {
        this.yyqhc = {
          ...this.yyqhc,
          tjrqm: res.data
        };
      },

      uploadGdj1(res, file) {
        this.fggdjhc = {
          ...this.fggdjhc,
          tjrqm: res.data
        };
      },
      uploadGdj2(res, file) {
        this.fggdjhc = {
          ...this.fggdjhc,
          hcrqm: res.data
        };
      },
      uploadGdj3(res, file) {
        this.fggdjhc = {
          ...this.fggdjhc,
          shrqm: res.data
        };
      },

      uploadSyg1(res, file) {
        this.syghc = {
          ...this.syghc,
          tjrqm: res.data
        };
      },
      uploadSyg2(res, file) {
        this.syghc = {
          ...this.syghc,
          hcrqm: res.data
        };
      },
      uploadSyg3(res, file) {
        this.syghc = {
          ...this.syghc,
          shrqm: res.data
        };
      },
      uploadDztp1(res, file) {
        this.dztphc = {
          ...this.dztphc,
          tjrqm: res.data
        };
      },
      uploadDztp2(res, file) {
        this.dztphc = {
          ...this.dztphc,
          hcrqm: res.data
        };
      },
      uploadDztp3(res, file) {
        this.dztphc = {
          ...this.dztphc,
          shrqm: res.data
        };
      },
      beforeAvatarUpload(file) {
        // const isLt2M = file.size / 1024 / 1024 < 2;
        // if (!isLt2M) {
        //   this.$message.error('上传头像图片大小不能超过 2MB!');
        // }
        // return isJPG && isLt2M;
        return true
      },

      showPlan() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        sessionStorage.setItem('qjhc_list', JSON.stringify(this.selectionList))
        this.$router.push({
          path: '/device/qjhc/plan',
        })
      },
      showDetail(e) {
        this.$router.push({
          path: '/device/qjhc/detail',
          query: {
            hcid: e.hcid,
            type: e.bzyqmc
          }
        })
      },
      handleAdd() {
        this.dialogVisible = true
        // this.$router.push('/device/qjhc/add')
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.hcid);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.dialogVisible = false
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
  .avatar-uploader .el-upload {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 24px;
    color: #8c939d;
    width: 128px;
    height: 128px;
    line-height: 128px;
    text-align: center;
  }

  .avatar {
    width: 128px;
    height: 128px;
    display: block;
  }
</style>
<style lang="less" scoped>
  .btn_box {
    width: 100%;
    margin-bottom: 50px;
    text-align: center;
  }

  .info_title {
    padding: 15px 0;

  }

  .required_span {
    color: #F56C6C;
    font-size: 13px;
    margin-right: 4px;
    font-weight: normal;
  }

  .card_title {
    margin-top: 15px;
    padding-bottom: 15px;
    font-weight: bold;
    border-bottom: 2px dotted #ccc;
  }

  table {
    width: 100%;
    table-layout: fixed;
    font-size: 12px !important;
  }

  table,
  td,
  th {
    text-align: center;
    border: 1px solid #ccc;
    border-collapse: collapse;
    font-weight: normal;
    font-size: 12px !important;
  }

  table tr {
    height: 30px;
    padding: 0 5px !important;
    font-size: 12px !important;
    word-wrap: break-word !important;
  }

  table td {
    padding: 5px;
    font-size: 12px !important;
    word-wrap: break-word !important;
  }

  .out_box {
    padding: 15px;
    border: 1px solid #ccc;
    margin-top: 15px;
  }

</style>
