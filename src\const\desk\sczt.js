export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  searchShow: false,
  labelWidth: 120,
  align: 'center',
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "市场名称",
      prop: "scmc",
      type: "input",
    },
    {
      label: "市场类型",
      prop: "sclx",
      type: "select",
      searchSpan:4,
      search: true,
      dicUrl: "/api/blade-system/dict/dictionary?code=market_type",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
    },
    {
      label: "市场负责人",
      prop: "scFzr",
      type: "input",
      width: 100,
    },
    {
      label: "负责人电话",
      prop: "scFzrdh",
      type: "input",
      width: 100,
    },
    {
      label: "食安负责人",
      prop: "saFzr",
      type: "input",
      width: 100,
    },
    {
      label: "食安电话",
      prop: "saFzrdh",
      type: "input",
    },
    {
      label: "摊位数量",
      prop: "twsl",
      type: "input",
    },
    {
      label: "市场规模",
      prop: "scgm",
      type: "select",
      searchSpan:4,
      search: true,
      dicData: [
        {
          label: '小型',
          value: '小型',
        },
        {
          label: '中型',
          value: '中型',
        },
        {
          label: '大型',
          value: '大型',
        },
      ],
    },
    {
      label: "地址",
      prop: "dz",
      type: "input",
    },
    {
      label: "责任监管人",
      prop: "jgr",
      type: "input",
      width: 100,
    },
    {
      label: "监管人电话",
      prop: "jgrDh",
      type: "input",
      width: 100,
    },
    {
      label: "监管单位",
      prop: "jgdw",
      type: "input",
    },
    {
      label: "营业执照编号",
      prop: "yyzzbh",
      type: "input",
    },
    {
      label: "营业执照照片",
      prop: "yyzzt",
      type: 'upload',
      listType: 'picture-card',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: false,
      hide: true,
      multiple: true,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
