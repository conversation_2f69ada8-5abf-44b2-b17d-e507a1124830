export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchShow: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  labelWidth: 120,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "归属任务",
      prop: "rwid",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "快检项目",
      prop: "jcxm",
      type: "select",
      filterable: true,
      dicUrl: "/api/ypgl/jcxm/page?code=check_method?current=1&size=9999",
      props: {
        label: "xmmc",
        value: "xmbh",
        res: 'data.records'
      },
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "样品名称",
      prop: "ypmc",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "报告编号",
      prop: "bgbh",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
      hide: true,
    },
    {
      label: "样品编号",
      prop: "ypbh",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "转定量时间",
      prop: "zdlsj",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },


    {
      label: "单位",
      prop: "dw",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "快检结果",
      prop: "jcjg",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "限量要求",
      prop: "xlyq",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "定量检测项目",
      prop: "dljcxm",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "定量检测结果",
      prop: "dljcjg",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "定量结论",
      prop: "dljl",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },

    {
      label: "结果一致性",
      prop: "jgyzx",
      type: "select",
      search: true,
      searchSpan:4,
      width: '150px',
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
      dicData: [
        {
          label: '一致',
          value: '一致',
        },
        {
          label: '不一致',
          value: '不一致',
        },
      ],
    },

    {
      label: "检测文件上传",
      prop: "img",
      type: 'upload',
      listType: 'text',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      accept: 'Application/pdf',
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: true,
      hide: true,
      multiple: true,
      rules: [
        {
          required: true,
          message: "请上传",
          trigger: "blur"
        }
      ],

    },

    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
