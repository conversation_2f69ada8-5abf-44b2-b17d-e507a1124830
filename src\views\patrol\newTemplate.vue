<template>
  <basic-container>
    <div class="title_box">
      <span class="required_span">*</span>
      <el-input style="width: 380px" v-model="temName" :disabled="this.id" placeholder="请输入巡查单模板名称"></el-input>
      <el-divider></el-divider>
    </div>
    <div class="main_box">
      <el-row :gutter="20">
        <el-col :span="4">
          <div class="left_box">
            <div class="left_title">表头项目</div>
            <el-checkbox-group v-model="headerList" :disabled="id" @change="changeHeader">
              <el-checkbox style="display:block;" checked label="所属任务" disabled></el-checkbox>
              <el-checkbox style="display:block;" checked label="单位类型" disabled></el-checkbox>
              <el-checkbox style="display:block;" checked label="巡查日期" disabled></el-checkbox>
              <el-checkbox style="display:block;" checked label="被检单位名称" disabled></el-checkbox>
              <el-checkbox style="display:block;" checked label="联系人" disabled></el-checkbox>
              <el-checkbox style="display:block;" checked label="联系人电话" disabled></el-checkbox>
              <el-checkbox style="display:block;" label="快检室信息"></el-checkbox>
              <el-checkbox style="display:block;" label="被检单位地址"></el-checkbox>
              <el-checkbox style="display:block;" label="统一社会信用代码"></el-checkbox>
              <el-checkbox style="display:block;" label="注册号/许可证号"></el-checkbox>
              <el-checkbox style="display:block;" label="所属市场名称"></el-checkbox>
              <el-checkbox style="display:block;" label="商铺名称"></el-checkbox>
              <el-checkbox style="display:block;" label="商铺地址"></el-checkbox>
              <el-checkbox style="display:block;" label="商铺面积"></el-checkbox>
              <el-checkbox style="display:block;" label="食品经营许可证号"></el-checkbox>
              <el-checkbox style="display:block;" label="食品经营许可证发放时间"></el-checkbox>
              <el-checkbox style="display:block;" label="经营许可证地址"></el-checkbox>
              <el-checkbox style="display:block;" label="从业人数"></el-checkbox>
              <el-checkbox style="display:block;" label="单位性质"></el-checkbox>
              <el-checkbox style="display:block;" label="隔离酒店名称"></el-checkbox>
              <el-checkbox style="display:block;" label="隔离人数"></el-checkbox>
            </el-checkbox-group>
            <el-divider></el-divider>
            <div class="left_title">表尾项目</div>
            <el-checkbox-group v-model="footerList" :disabled="id">
              <el-checkbox style="display:block;" checked label="评分" disabled></el-checkbox>
              <el-checkbox style="display:block;" checked label="巡查结果" disabled></el-checkbox>
              <el-checkbox style="display:block;" checked label="巡查人员签名"></el-checkbox>
              <el-checkbox style="display:block;" label="陪同人员签名"></el-checkbox>
              <el-checkbox style="display:block;" label="巡查人员签名2"></el-checkbox>
              <!--            <el-checkbox style="display:block;" label="巡查日期"></el-checkbox>-->
              <el-checkbox style="display:block;" label="巡查报告"></el-checkbox>
            </el-checkbox-group>
          </div>
        </el-col>
        <el-col :span="20">
          <avue-form :option="option"></avue-form>
          <div>食品安全巡检类目</div>
          <el-table
            :data="tableData"
            border
            style="width: 100%;margin: 10px 0">
            <el-table-column
              type="index"
              align="center"
              label="序列">
            </el-table-column>
            <el-table-column
              prop="zdmc"
              align="center"
              label="巡查项目">
              <template slot-scope="scope">
                <el-input v-model="scope.row.zdmc" size="small" style="width: 100%" placeholder="请输入巡查项目"></el-input>
              </template>
            </el-table-column>
            <el-table-column
              prop="nr"
              align="center"
              label="巡查内容">
              <template slot-scope="scope">
                <el-input v-model="scope.row.nr" size="small" style="width: 100%" placeholder="请输入巡查内容"></el-input>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="巡查结果">
              <template slot-scope="scope">
                <el-select size="small" v-model="scope.row.jg" disabled placeholder="请选择">
                  <el-option
                    v-for="item in bzList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="不符合描述">
              <template slot-scope="scope">
                <el-input v-model="scope.row.bfh" disabled size="small" style="width: 100%" placeholder="请输入"></el-input>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="备注">
              <template slot-scope="scope">
                <el-select size="small" v-model="scope.row.xjxbz" placeholder="请选择">
                  <el-option
                    v-for="item in bzList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="120">
              <template slot-scope="scope">
                <el-button
                  @click.native.prevent="deleteRow(scope.$index, tableData)"
                  type="text"
                  size="small">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="font-size: 14px;margin: 10px 0">说明：★为关键项目，◎仅为餐饮配送和中央厨房单位关键项目，⊙代表仅为小型餐饮（加工面积小于200平方）适用为一般项目，它类型为关键项目；
            关键项目在不合格处标注下横线,并注简单信息；符合项目栏内打“√”，如不符合应在相应栏内标示关键项为“★”或一般项目为“×”，若无此项目内容的，用“—”标注；
            统计检查结果，在结论处须打“√”。新提A级的单位报辖区局审定、市局公布。</div>
          <el-button @click="addTable" size="small" type="primary" icon="el-icon-plus">新增</el-button>
          <div>
            <div style="margin: 10px 0">备注</div>
            <el-input
              type="textarea"
              :rows="3"
              readonly
              placeholder="请输入备注"
              v-model="remark">
            </el-input>
          </div>
          <div style="margin: 10px 0">评分方式</div>

          <avue-crud
            style="width: 100%;margin: 20px 0"
            :data="footTable"
            :option="footOption"
            :span-method="spanMethod"
          >
            <template slot="menuLeft">
              <el-radio-group v-model="checkType" :disabled="this.id" size="small" @change="changeType">
                <el-radio-button label="1">优良中差方式</el-radio-button>
                <el-radio-button label="2">ABCD方式</el-radio-button>
              </el-radio-group>
            </template>
            <template slot-scope="{row}" slot="zhpf">
              <el-input disabled v-model="row.zhpf" size="small" style="width: 80%" placeholder="请输入分数"></el-input>
            </template>
            <template slot-scope="{row}" slot="pj">
              <el-select v-model="row.pj" placeholder="请选择" size="small" style="width: 80%" disabled>
                <el-option
                  v-for="item in pjList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </template>
          </avue-crud>

          <div style="margin: 10px 0">
            <span>巡查结果：</span>
            <el-select disabled size="small" style="width: 200px" v-model="jg" placeholder="请选择">
              <el-option label="合格" value="合格"></el-option>
              <el-option label="不合格" value="不合格"></el-option>
            </el-select>
          </div>
          <div class="sign_box">
            <div style="margin-right: 25px" v-if="footerList.indexOf('巡查人员签名')> -1">
              <div style="margin-bottom: 10px">巡查人员签名</div>
              <el-upload
                disabled
                class="upload-demo"
                action="/api/manage/put-object"
                :limit="1"
                :file-list="signImg1">
                <el-button size="small" disabled type="primary">点击上传</el-button>
              </el-upload>
            </div>
            <div style="margin-right: 25px" v-if="footerList.indexOf('陪同人员签名')> -1">
              <div style="margin-bottom: 10px">陪同人员签名</div>
              <el-upload
                disabled
                class="upload-demo"
                action="/api/manage/put-object"
                :limit="1"
                :file-list="signImg2">
                <el-button size="small" disabled type="primary">点击上传</el-button>
              </el-upload>
            </div>
            <div style="margin-right: 25px" v-if="footerList.indexOf('巡查人员签名2')> -1">
              <div style="margin-bottom: 10px">巡查人员签名2</div>
              <el-upload
                disabled
                class="upload-demo"
                action="/api/manage/put-object"
                :limit="1"
                :file-list="signImg3">
                <el-button size="small" disabled type="primary">点击上传</el-button>
              </el-upload>
            </div>
          </div>
          <div class="report_box">
            <div style="margin-right: 25px" v-if="footerList.indexOf('巡查报告')> -1">
              <div style="margin-bottom: 10px">巡查报告</div>
              <el-upload
                disabled
                class="upload-demo"
                action="/api/manage/put-object"
                :limit="1"
                :file-list="patrolReport">
                <el-button size="small" disabled type="primary">点击上传</el-button>
              </el-upload>
            </div>
          </div>
          <el-divider></el-divider>
          <div style="margin: 20px 0;text-align: center">
            <el-button @click="goBack">返回</el-button>
            <el-button type="primary" @click="save" v-if="!this.id">保存模板</el-button>
          </div>
        </el-col>
      </el-row>
    </div>
  </basic-container>
</template>

<script>

  import { add, update, getDetail} from "@/api/patrol/model";

  export default {
    name: "newTemplate",
    data(){
      return {
        checkType: 1,
        temName: '',
        headerList:[],
        headOption: [],
        footerList:[],
        option:{
          labelPosition: 'top',
          labelWidth: 120,
          submitBtn:false,
          emptyBtn:false,
          column:[]
        },
        tableData:[],
        remark: '',
        footTable:[
          {
            zhpf: '',
            pjfj: '优',
            df: '80-100分',
            pj: ''
          },
          {
            pjfj: '良',
            df: '70-80分',
            pj: ''
          },
          {
            pjfj: '中',
            df: '60-70分',
            pj: ''
          },
          {
            pjfj: '差',
            df: '60分以下',
            pj: ''
          }
        ],
        footOption:{
          border: true,
          menu: false,
          addBtn: false,
          refreshBtn: false,
          filterBtn: false,
          searchShowBtn: false,
          columnBtn: false,
          align:'center',
          column:[
            {
              label: '综合评分',
              prop: 'zhpf',
            },
            {
              label: '评价分级',
              prop: 'pjfj'
            },
            {
              label: '得分',
              prop: 'df'
            },
            {
              label: '评级',
              prop: 'pj'
            },
          ]
        },
        pjList:[
          {
            label: '优',
            value: '优'
          },
          {
            label: '良',
            value: '良'
          },
          {
            label: '中',
            value: '中'
          },
          {
            label: '差',
            value: '差'
          },
        ],
        jg:'',
        signImg1: [],
        signImg2: [],
        signImg3: [],
        patrolReport: [],
        id: '',
        bzList: [
          {
            label: '★',
            value: 1,
          },
          {
            label: '◎',
            value: 2,
          },
          {
            label: '⊙',
            value: 3,
          },
          {
            label: '-',
            value: 4,
          }
        ],

        newHeadOption: [],
        oldHeadList: []
      }
    },
    mounted() {
      this.initForm()
      this.id = this.$route.query.id
      if(this.id){
        this.getDetail()
      }
    },
    methods:{
      deleteRow(index, rows) {
        rows.splice(index, 1);
      },
      goBack(){
        this.$router.back()
      },
      getDetail(){
        getDetail(this.id).then(res=>{
          const detail = res.data.data
          this.temName = detail.model.mbmc
          this.checkType = detail.model.pflx
          detail.modelXq.filter(item=>item.mblx == 1).map(item=>{
            this.headerList.push(item.zdmc)
            this.headOption.push(item)
          })
          detail.modelXq.filter(item=>item.mblx == 3).map(item=>{
            this.footerList.push(item.zdmc)
          })
          detail.modelXq.filter(item=>item.mblx == 2).map(item=>{
            this.tableData.push(item)
          })
          this.initForm()
          this.oldHeadList = this.headerList
        })
      },
      save(){
        if(!this.temName){
          this.$message.error('请输入模板名称')
          return
        }
        let params = {
          model: {
            id: this.id,
            mbmc: this.temName,
            pflx: this.checkType
          },
          modelXq:[]
        }
        params.modelXq.push(...this.newHeadOption)
        this.tableData.map((item,index)=>{
          params.modelXq.push({
            ...item,
            "mblx": 2,
            "zdmc": item.zdmc,
            "nr": item.nr,
            "xjxbz": item.xjxbz,
            "sort": index,
          })
        })
        this.footerList.slice(2,this.footerList.length).map((item,index)=>{
          params.modelXq.push({
            ...item,
            "mblx": 3,
            "zdmc": item,
            "sort": index,
          })
        })

        if(this.id){
          update(params).then(() => {
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.goBack()
          }, error => {
            console.log(error);
          });
        }else{
          add(params).then(() => {
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.goBack()
          }, error => {
            console.log(error);
          });
        }


      },
      changeType(e){
        if(e == 1){
          this.footTable = this.footTable.map((item,index)=>{
            return {
              ...item,
              pjfj: index==0?'优':index==1?'良':index==2?'中':index==3?'差':''
            }
          })
          this.pjList = [
            {
              label: '优',
              value: '优'
            },
            {
              label: '良',
              value: '良'
            },
            {
              label: '中',
              value: '中'
            },
            {
              label: '差',
              value: '差'
            },
          ]
        }
        if(e == 2){
          this.footTable = this.footTable.map((item,index)=>{
            return {
              ...item,
              pjfj: index==0?'A':index==1?'B':index==2?'C':index==3?'D':''
            }
          })
          this.pjList = [
            {
              label: 'A',
              value: 'A'
            },
            {
              label: 'B',
              value: 'B'
            },
            {
              label: 'C',
              value: 'C'
            },
            {
              label: 'D',
              value: 'D'
            },
          ]
        }
      },
      spanMethod({ rowIndex, columnIndex }){
        if(columnIndex === 0  || columnIndex === 3){
          if (rowIndex == 0) {
            return {
              rowspan: 4,
              colspan: 1
            }
          }else{
            return {
              rowspan: 0,
              colspan: 0
            }
          }
        }
      },
      addTable(){
        this.tableData.push({
          "zdmc": '',
          "nr": ''
        })
      },
      initForm(){
        this.option.column = this.headerList.map(item=>{
          return {
            label: item,
            prop: 'input',
            disabled: true
          }
        })
      },
      changeHeader(e){
        const oldHeadOption = this.headOption
        let newHeadOption = []
        this.oldHeadList
        e.slice(6,e.length).map((item,index)=>{
          const a = oldHeadOption.find(item2=>item2.zdmc==item)
          if(a){
            newHeadOption.push(oldHeadOption.find(item2=>item2.zdmc==item))
          }else{
            newHeadOption.push({
              zdmc: item,
              sort: index,
              mblx: 1
            })
          }
          this.newHeadOption = newHeadOption
        })
        this.option.column = e.map(item=>{
          return {
            label: item,
            prop: 'input',
            disabled: true
          }
        })
      }
    }
  }
</script>

<style scoped lang="less">
  .required_span {
    color: #F56C6C;
    font-size: 14px;
    margin-right: 4px;
    font-weight: normal;
  }
  .sign_box{
    width: 100%;
    display: flex;
    font-size: 14px;
    margin: 20px 0;
  }
  .report_box{
    width: 100%;
    display: flex;
    font-size: 14px;
    margin: 20px 0;
  }
  .left_box{
    width: 100%;
    border: 1px solid #ccc;
    padding: 15px;
    overflow: auto;
    .left_title{
      color: #04A7B3;
    }
  }
  .main_box{
    min-width: 1200px;
    overflow: auto;
  }
  .title_box{
    width: 100%;
    line-height: 15px;
    text-align: center;
  }
</style>
<style lang="less">
  .left_box{
    .el-checkbox{
      margin-top: 10px;
    }
  }

</style>
