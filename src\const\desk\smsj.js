import {resolve} from "mathjs";

export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: false,
  searchMenuSpan: 6,
  labelWidth: 120,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "受检单位",
      prop: "sjdw",
      type: "tree",
      filterable: true,
      dicUrl: "/api/sh/sczt/list",
      props: {
        label: "scmc",
        value: "id",
        res: 'data.records'
      },
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      nodeClick: (data, node, nodeComp) => {
        if (window.__smsj_component__) {
          window.__smsj_component__.handleNodeClick2(data, 'scmc','sjdwmc');
        }
      },
    },
    {
      label: "受检单位名称",
      prop: "sjdwmc",
      type: "input",
      display: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "样品名称",
      prop: "ypmc",
      type: "tree",
      filterable: true,
      dicUrl: "/api/ypgl/ypxx/list?current=1&size=9999",
      props: {
        label: "ypmc",
        value: "id",
        res: 'data.records'
      },
      cascader: ['ypdl', 'ypdlmc'],
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      nodeClick: (data, node, nodeComp) => {
        if (window.__smsj_component__) {
          window.__smsj_component__.handleNodeClick(data, node);
          window.__smsj_component__.handleNodeClick2(data, 'ypmc','ypmcmc');

        }
      },
    },
    {
      label: "联系电话",
      prop: "dh",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "联系人",
      prop: "kh",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "样品大类",
      prop: "ypdl",
      type: "tree",
      dicUrl: "/api/blade-system/dict/dictionary?code=sample_big_type",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      cascader: ['ypxl', 'ypxlmc'],
      filterable: true,
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      row: false,
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      hide: true,
    },
    {
      label: "样品大类",
      prop: "ypdlmc",
      type: "tree",
      dicUrl: "/api/blade-system/dict/dictionary?code=sample_big_type",
      props: {
        label: "dictValue",
        value: "dictValue"
      },
      filterable: true,
      search: false,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: false,
      row:false

    },
    {
      label: "样品细类",
      prop: "ypxl",
      type: "select",
      dicUrl: "/api/ypgl/ypxx/getYpxl?ypdl={{ypdl}}",
      cascaderIndex: 0,
      props: {
        label: "label",
        value: "label"
      },
      filterable: true,
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "样品细类",
      prop: "ypxlmc",
      type: "tree",
      dicUrl: "/api/ypgl/ypxx/getYpxl?ypdl={{ypdl}}",
      props: {
        label: "label",
        value: "value"
      },
      cascaderIndex: 0,
      filterable: true,
      search: false,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      row:false

    },
    {
      label: "采样时间",
      prop: "cysj",
      type: "datetime",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "送检时间",
      prop: "sjrq",
      type: "datetime",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "抽样单编号",
      prop: "cydbh",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "所属城市",
      prop: "sscs",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "所属任务",
      prop: "ssrw",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      parent:false,
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      search: true,
      width: 100,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "样品编号",
      prop: "ypbh",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "样品量(g)",
      prop: "cysl",
      type: "input",
    },
    {
      label: "抽样基数",
      prop: "cyjs",
      type: "input",
    },
    {
      label: "生产企业",
      prop: "scqy",
      type: "input",
    },
    {
      label: "生产/进货日期",
      prop: "scrq",
      type: "date",
      valueFormat: "yyyy-MM-dd",
    },
    {
      label: "供货者",
      prop: "ghz",
      type: "input",
    },
    {
      label: "产品商标",
      prop: "cpsb",
      type: "input",
    },
    {
      label: "产品规格",
      prop: "cpgg",
      type: "input",
    },
    {
      label: "委托单位",
      prop: "wtdw",
      type: "input",
    },
    {
      label: "进货单号",
      prop: "jhdh",
      type: "input",
    },
    {
      label: "产地",
      prop: "cd",
      type: "input",
    },
    {
      label: "采样费",
      prop: "cyf",
      type: "input",
    },
    {
      label: "采样地址",
      prop: "dz",
      type: "input",
    },
    {
      label: "商户名称",
      prop: "shmc",
      type: "input",
    },
    {
      label: "摊位号",
      prop: "twh",
      type: "input",
    },
    {
      label: "产量",
      prop: "cl",
      type: "input",
    },
    {
      label: "检测结果",
      prop: "jcjg",
      type: "select",
      search: true,
      dicData: [
        {
          label: '阴性',
          value: '阴性',
        },
        {
          label: '阳性',
          value: '阳性',
        },
      ],
    },
    {
      label: "告知邮箱",
      prop: "email",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "采样员签名",
      prop: "cyryqm",
      type: 'upload',
      listType: 'picture-img',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: false,
      rules: [
        {
          required: true,
          message: "请上传",
          trigger: "blur"
        }
      ],
      hide: true,
      multiple: true,
    },
    {
      label: "采样图片或视频",
      prop: "cytp",
      type: 'upload',
      listType: 'picture-card',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: false,
      hide: true,
      multiple: true,
    },

    {
      label: "是否邮箱告知",
      prop: "sftz",
      type: "select",
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "检测日期",
      prop: "jcrq",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      search: true,
      searchRange:true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "备注",
      prop: "bz",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
