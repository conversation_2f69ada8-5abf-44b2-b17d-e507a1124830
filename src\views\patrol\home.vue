<template>
  <basic-container>
    <el-row :gutter="20">
      <div class="chart_title_box">
        <span>各市场检出问题统计</span>
        <el-select size="mini" v-model="day" placeholder="请选择" @change="dateChange">
          <el-option label="日" value="日"></el-option>
          <el-option label="周" value="周"></el-option>
          <el-option label="月" value="月"></el-option>
        </el-select>
      </div>
      <div id="problem_chart"></div>
    </el-row>
    <div class="chart_title_box" style="margin-top: 10px">各监测点巡查次数统计</div>
    <div id="num_chart"></div>
  </basic-container>
</template>

<script>
import { xcsl, bzcxsl } from "@/api/patrol/home";
export default {
  name: "chart",
  data() {
    return {
      day: '日',
    }
  },
  mounted() {
    this.getxcsl(this.day);
    this.getbzcxsl(this.day);
  },
  methods: {
    dateChange(type){
      this.getxcsl(type);
      this.getbzcxsl(type);
    },
    getxcsl(type){
      xcsl(type).then(({ data: resultData })=>{
        if (resultData.code == 200) {
        const enumData = [
        {
          key: "name",
          value: '市场'
        },
        {
          key: "sl",
          value: 'sl'
        }];
          let { data } = resultData;
          let formateData = enumData.map(({ key, value }) => {
            return {
              name: value,
              data: new Array(data.length).fill('').map((_, index) => {
                return data[index][key];
              }),
            }
          })
         this.initNumChart([formateData[0].data , formateData[1].data]);
        }
      })
      
    },
    initNumChart2(data) {
      const chartDom = document.getElementById('problem_chart');
      const myChart = this.echarts.init(chartDom);
      const option = {
        xAxis: {
          type: 'category',
          data: data[0]
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '快检仪',
            data: data[1],
            type: 'bar',
            stack: 'Ad',
            barWidth: 50,
            emphasis: {
              focus: 'series'
            },
          }
        ]
      };

      option && myChart.setOption(option);
    },
    initNumChart(data) {
      const chartDom = document.getElementById('num_chart');
      const myChart = this.echarts.init(chartDom);
      const option = {
        xAxis: {
          type: 'category',
          data: data[0]
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '快检仪',
            data: data[1],
            type: 'bar',
            stack: 'Ad',
            barWidth: 50,
            emphasis: {
              focus: 'series'
            },
          }
        ]
      };

      option && myChart.setOption(option);
    },
    getbzcxsl(type){
      bzcxsl(type).then(({ data: resultData })=>{
        if (resultData.code == 200) {
        const enumData = [
        {
          key: "name",
          value: '市场'
        },
        {
          key: "sl",
          value: 'sl'
        }];
          let { data } = resultData;
          let formateData = enumData.map(({ key, value }) => {
            return {
              name: value,
              data: new Array(data.length).fill('').map((_, index) => {
                return data[index][key];
              }),
            }
          })
         this.initNumChart2([formateData[0].data , formateData[1].data]);
        }
      })
      
    },
    initProblemChart() {
      const chartDom = document.getElementById('problem_chart');
      const myChart = this.echarts.init(chartDom);
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['卫生问题', '可追溯问题', '消防问题']
        },
        grid: {
          left: '2%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: ['xx市场', 'xx市场', 'xx市场', 'xx市场', 'xx市场', 'xx市场', 'xx市场']
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: '卫生问题',
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: '可追溯问题',
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: [220, 182, 191, 234, 290, 330, 310]
          },
          {
            name: '消防问题',
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: [150, 232, 201, 154, 190, 330, 410]
          }
        ]
      };
      option && myChart.setOption(option);
    },

  }

}
</script>

<style scoped>
.chart_title_box {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

#problem_chart,
#num_chart {
  width: 100%;
  height: 500px;
  border: 1px solid #04A7B3;
  padding: 10px;
  box-sizing: border-box;
}
</style>
