<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="jcsjData"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button v-if="permission.jcsj_import" type="primary"
                   size="small"
                   icon="el-icon-download"
                   plain
                   @click="downExcel">
          下载模板
        </el-button>
        <el-upload v-if="permission.jcsj_import" :headers="uploadHeaders" :on-success="handleAvatarSuccess" :show-file-list="false" action="/api/jc/jcsj/addExcel" style="display: inline-block">
          <el-button type="primary"
                     size="small"
                     icon="el-icon-upload "
                     plain>
            导入
          </el-button>
        </el-upload>
        <el-button
          type="primary"
          size="small"
          @click="handleAdd"
        >生成检测抽检单
        </el-button>
      </template>
      <template slot-scope="{row,index}" slot="menu">
        <el-button size="small" type="text" @click="showFh(row)">查看详情</el-button>
        <el-button size="small" type="text" >导出报告</el-button>
      </template>
      <!--      <template slot-scope="{row}" slot="other">-->
      <!--        <el-button type="text" size="mini" @click="showOther(row)">其他信息</el-button>-->
      <!--      </template>-->
      <!--      <template slot-scope="{row}" slot="hgz">-->
      <!--        <img :src="row.scope.hgz" style="width: 20px;" alt="">-->
      <!--      </template>-->
      <template slot-scope="{ row }" slot="zzjg">
        <div v-if="row.zzjg == 1" style="color: #606266">阴性</div>
        <div v-if="row.zzjg == 2" style="color: red">阳性</div>
      </template>
    </avue-crud>

    <el-dialog :visible.sync="yxShow" width="70%" append-to-body title="检测详情">
      <avue-crud :option="detailOption"
                 :table-loading="loading"
                 :data="fhData"
                 :permission="permissionList"
                 ref="crud"
                 @row-update="rowUpdate"
                 @row-save="rowSave"
                 @row-del="rowDel"
                 @search-change="searchChange"
                 @search-reset="searchReset"
                 @selection-change="selectionChange"
                 @current-change="currentChange"
                 @size-change="sizeChange"
                 @refresh-change="refreshChange"
                 @on-load="onLoad">
        <template slot-scope="{ row }" slot="jcjg">
          <div v-if="row.jcjg === '1'" style="color: #606266;">阴性</div>
          <div v-if="row.jcjg === '2'" style="color: red;">阳性</div>
        </template>
      </avue-crud>
    </el-dialog>

    <el-dialog
      :visible.sync="dialogFormVisible"
      append-to-body
      title="检测详情"
    >
      <div style="width: 100%">
        <avue-form v-model="checkForm" :option="checkOption"></avue-form>
      </div>
      <div slot="footer" style="display: flex; justify-content: space-between">
        <el-button @click="dialogFormVisible = false">阳性处置</el-button>
        <el-button type="primary" @click="dialogFormVisible = false"
        >确 定
        </el-button
        >
      </div>
    </el-dialog>
    <el-dialog :visible.sync="otherShow" append-to-body title="其他信息">
      <avue-form
        v-model="otherForm"
        :option="otherOption"
        @submit="saveOther"
      ></avue-form>
    </el-dialog>

    <el-dialog
      :visible.sync="quicklyShow"
      width="70%"
      append-to-body
      title="快速抽检记录单"
    >
      <!-- <el-tabs type="card" v-model="tab" @tab-click="handleClick"> -->
      <el-button type="primary" @click="print" class="print">打印</el-button>
      <!-- <el-tab-pane label="正面" name="1"> -->
      <div style="width: 100%" id="printjcsj">
        <div class="quickly_title">食品快速抽检记录单</div>
        <el-descriptions :colon="false" title="" :column="2">
          <el-descriptions-item label="快检室信息：">
            <span class="span_info">{{cyForm.kjsInfo}}</span>
          </el-descriptions-item>
          <el-descriptions-item label="受检单位：">
            <span class="span_info">{{cyForm.sjdwmc}}</span>
          </el-descriptions-item>
          <el-descriptions-item label="抽检员签名：">
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="uploadCjy">
              <img v-if="cyForm.cjySign" :src="cyForm.cjySign" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-descriptions-item>
          <el-descriptions-item label="受检单位签名：">
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="uploadCjy2">
              <img v-if="cyForm.cjdwSign" :src="cyForm.cjdwSign" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :colon="false"
          title=""
          :column="2"
          border
          labelClassName="desc_title"
        >
          <el-descriptions-item label="抽样检测日期">
            <span class="span_info">{{cyForm.input1}}</span>
          </el-descriptions-item>
          <el-descriptions-item label="抽样单编号">
            <span class="span_info">{{cyForm.input2}}</span>
          </el-descriptions-item>
          <el-descriptions-item label="位点名称">
            <span class="span_info">{{cyForm.sjdwmc}}</span>
          </el-descriptions-item>
          <el-descriptions-item label="位点地址">
            <el-input size="mini" placeholder="请输入" v-model="cyForm.input4"></el-input>
          </el-descriptions-item>
          <el-descriptions-item label="位点营业执照">
            <img @click="openPreview" style="width: 80px;cursor:pointer;" v-if="cyForm.yyzzt" :src="cyForm.yyzzt" alt="">
            <span v-else>未上传</span>
          </el-descriptions-item>
          <el-descriptions-item label="联系人">
            <span>{{cyForm.frmc}}</span>
          </el-descriptions-item>
          <el-descriptions-item label="行政区域">
            <el-input size="mini" placeholder="请输入" v-model="cyForm.input5"></el-input>
          </el-descriptions-item>
          <el-descriptions-item label="所在街道">
            <el-input size="mini" placeholder="请输入" v-model="cyForm.input6"></el-input>
          </el-descriptions-item>
          <el-descriptions-item label="位点类型">
            <el-input size="mini" placeholder="请输入" v-model="cyForm.input7"></el-input>
          </el-descriptions-item>

          <el-descriptions-item label="联系人电话">
            <el-input size="mini" placeholder="请输入" v-model="cyForm.input10"></el-input>
          </el-descriptions-item>
        </el-descriptions>
        <table style="margin-top: 10px">
          <thead>
          <tr>
            <th>采样编号</th>
            <th>样品名称</th>
            <th>抽样数量</th>
            <th>进货数量</th>
            <th>供货者/生产者信息</th>
            <th>产地</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in selectionList" :key="index">
            <td>{{item.cybh}}</td>
            <td>{{item.ypmc}}</td>
            <td>
              <el-input size="mini" placeholder="请输入" v-model="item.cyNumber"></el-input>
            </td>
            <td>
              <el-input size="mini" placeholder="请输入" v-model="item.inNumber"></el-input>
            </td>
            <td>
              <el-input size="mini" placeholder="请输入" v-model="item.supplier"></el-input>
            </td>
            <td>
              <el-input size="mini" placeholder="请输入" v-model="item.producer"></el-input>
            </td>
          </tr>
          </tbody>
        </table>
        <div class="quick_remark">
          备注：
          <el-input
            style="margin-top: 5px"
            type="textarea"
            :rows="2"
            placeholder="请输入内容"
            v-model="cyForm.remark1">
          </el-input>
        </div>
        <div style="width: 100%">
          <br>
          <br>
           <br>
          <br>
           <br>
          <br>
           <br>
          <br>
           <br>
          <div class="quickly_title">食品快速抽检记录单（B）</div>
          <el-descriptions :colon="false" title="" :column="2">
            <el-descriptions-item label="快检室信息：">
              <span class="span_info">{{cyForm.kjsInfo}}</span>
            </el-descriptions-item>
            <el-descriptions-item label="受检单位：">
              <span class="span_info">{{cyForm.sjdwmc}}</span>
            </el-descriptions-item>
            <el-descriptions-item label="抽检员签名：">
              <el-upload
                class="avatar-uploader"
                action="/api/manage/put-object"
                :show-file-list="false"
                :on-success="uploadCjy">
                <img v-if="cyForm.cjySign" :src="cyForm.cjySign" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-descriptions-item>
            <el-descriptions-item label="受检单位签名：">
              <el-upload
                class="avatar-uploader"
                action="/api/manage/put-object"
                :show-file-list="false"
                :on-success="uploadCjy2">
                <img v-if="cyForm.cjdwSign" :src="cyForm.cjdwSign" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions
            :colon="false"
            title=""
            :column="2"
            border
            labelClassName="desc_title"
          >
            <el-descriptions-item label="抽样检测日期">
              <span>{{cyForm.input1}}</span>
<!--              <el-input size="mini" placeholder="请输入" v-model="cyForm.input1"></el-input>-->
            </el-descriptions-item>
            <el-descriptions-item label="抽样单编号">
              <span class="span_info">{{cyForm.input2}}</span>
            </el-descriptions-item>
            <el-descriptions-item label="抽样地点">
              <el-input size="mini" placeholder="请输入" v-model="cyForm.inputB1"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="受检单位">
              <el-input size="mini" placeholder="请输入" v-model="cyForm.inputB2"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="证件号码">
              <el-input size="mini" placeholder="请输入" v-model="cyForm.inputB3"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="受检单位地址">
              <el-input size="mini" placeholder="请输入" v-model="cyForm.inputB4"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="联系人">
              <el-input size="mini" placeholder="请输入" v-model="cyForm.input9"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="联系人电话">
              <el-input size="mini" placeholder="请输入" v-model="cyForm.input10"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="分光空白值">
              <el-input size="mini" placeholder="请输入" v-model="cyForm.inputB5"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="可溯源设备名称">
              <el-input size="mini" placeholder="请输入" v-model="cyForm.inputB6"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="可溯源设备编号">
              <el-input size="mini" placeholder="请输入" v-model="cyForm.inputB7"></el-input>
            </el-descriptions-item>
          </el-descriptions>
          <table style="margin-top: 10px">
            <thead>
            <tr>
              <th>采样编号</th>
              <th>样品名称</th>
              <th>检测项目</th>
              <th>检测方法</th>
              <th>检测值</th>
              <th>检测结果</th>
              <th>后续处理</th>
              <th>试剂信息</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(item,index) in selectionList" :key="index">
              <td>{{item.cybh}}</td>
              <td>{{item.ypmc}}</td>
              <td>{{item.jcxm}}</td>
              <td>
                <el-input size="mini" placeholder="请输入" v-model="item.jcff"></el-input>
              </td>
              <td>
                <el-input size="mini" placeholder="请输入" v-model="item.jcz"></el-input>
              </td>
              <td :style="{ color: item.zzjg == 1 ? 'red' : 'inherit' }" >{{item.zzjg == 1?'阳性':item.zzjg == 0?'阴性':'-'}}</td>
              <td>{{item.yxcz == 1?'已处置':item.yxcz == 0?'未处置':'-'}}</td>
              <td>
                <el-input size="mini" placeholder="请输入" v-model="item.sjxx"></el-input>
              </td>
            </tr>
            </tbody>
          </table>
          <div class="quick_remark">
            备注：
            <el-input
              style="margin-top: 5px"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              v-model="cyForm.remark2">
            </el-input>
          </div>
        </div>
      </div>
      <div slot="footer" style="text-align: center">
        <el-button type="primary" @click="quicklyShow = false">确 定</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getCyList, getList, getDetail, saveOther, downLoadModel, getShopDetail} from "@/api/check/jcsj";
  import {getJcList} from '@/api/check/yxcz'
  import option from "@/const/check/jcsj";
  import Qcti7 from "@/components/print-template/qcti7";
  import Qcti8 from "@/components/print-template/qcti8";
  import Qcti12 from "@/components/print-template/qcti12";
  import Qcti13 from "@/components/print-template/qcti13";
  import {getToken} from '@/util/auth';
  import {mapGetters} from "vuex";

  export default {
    components: {
      Qcti7,
      Qcti8,
      Qcti12,
      Qcti13,
    },
    data() {
      return {
        uploadHeaders:{},
        tab: "1",
        qctiShow: true,
        quicklyShow: false,
        quickTable: [],
        jcxx: {},
        dialogFormVisible: false,
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0,
        },
        selectionList: [],
        option: option,
        jcsjData: [],
        detailShow: false,
        checkForm: {},
        checkOption: {
          submitBtn: false,
          emptyBtn: false,
          readonly: true,
          column: [
            {
              label: "样品编号",
              prop: "input",
              type: "text",
            },
            {
              label: "检测时间",
              prop: "input1",
              type: "text",
              valueFormat: "yyyy-MM-dd",
            },
            {
              label: "所属城市",
              prop: "sscs",
              type: "text",
              valueFormat: "yyyy-MM-dd",
            },
            {
              label: "所属任务",
              prop: "sbbh",
              type: "text",
              parent: false,
              dicUrl: "/api/blade-system/dept/tree",
              props: {
                label: "title",
                value: "id",
                res: "data",
              },
            },
            {
              label: "样品大类",
              prop: "ypid",
              type: "text",
              dicUrl: "/api/blade-system/dict/dictionary?code=sample_big_type",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
            },
            {
              label: "样品名称",
              prop: "input",
              type: "text",
            },
            {
              label: "检测项目",
              prop: "input",
              type: "text",
            },
            {
              label: "检测人员",
              prop: "cyry",
              type: "text",
              dicUrl: "/api/blade-user/page?current=1&size=100000&deptId=",
              props: {
                label: "name",
                value: "id",
                res: "data.records",
              },
            },
            {
              label: "检测方法",
              prop: "ypid",
              type: "text",
              dicUrl: "/api/blade-system/dict/dictionary?code=check_method",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
            },
            {
              label: "试剂信息",
              prop: "input",
              type: "text",
            },
            {
              label: "检测结果",
              prop: "input",
              type: "text",
            },
            {
              label: "检测值",
              prop: "input",
              type: "text",
            },
            {
              label: "最终结果",
              prop: "input",
              type: "text",
            },
            {
              label: "无需复核",
              prop: "ypid",
              type: "text",
              dicUrl: "/api/blade-system/dict/dictionary?code=review_status",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
            },
          ],
        },
        otherForm: {
          title1: "受检单位信息",
          title2: "溯源仪器信息",
          title3: "检测相关信息",
        },
        otherOption: {
          labelWidth: 110,
          column: [
            {
              labelWidth: 10,
              type: "title",
              prop: "title1",
              span: 24,
              styles: {
                fontSize: "14px",
                borderBottom: "1px dotted #ccc",
              },
            },
            {
              label: "受检单位",
              prop: "sjdw",
              type: "input",
            },
            {
              label: "档口号",
              prop: "dkh",
              type: "input",
            },
            {
              label: "受检单位地址",
              prop: "sjdwdz",
              type: "input",
            },
            {
              label: "经营者姓名",
              prop: "jyzxm",
              type: "input",
            },
            {
              label: "证件号码",
              prop: "zjhm",
              type: "input",
            },
            {
              label: "联系电话",
              prop: "lxdh",
              type: "input",
            },
            {
              labelWidth: 10,
              type: "title",
              prop: "title2",
              span: 24,
              styles: {
                fontSize: "14px",
                borderBottom: "1px dotted #ccc",
              },
            },
            {
              label: "溯源设备",
              prop: "sysb",
              type: "input",
            },
            {
              label: "分光空白值",
              prop: "fgkbz",
              type: "input",
            },
            // {
            //   prop: 'checkbox',
            //   type: 'checkbox',
            //   dicData:[{
            //     label:'未使用空白设备',
            //     value:0
            //   }]
            // },
            {
              labelWidth: 10,
              type: "title",
              prop: "title3",
              span: 24,
              styles: {
                fontSize: "14px",
                borderBottom: "1px dotted #ccc",
              },
            },
            {
              label: "总价",
              prop: "zj",
              type: "input",
            },
            {
              label: "大写金额",
              prop: "dxje",
              type: "input",
            },
            {
              label: "所属街道",
              prop: "scjd",
              type: "input",
            },
            {
              label: "抽样检测日期",
              prop: "cyjcrq",
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },
            {
              label: "检测计数",
              prop: "jcjs",
              type: "input",
            },
            {
              label: "收据单号",
              prop: "sjdh",
              type: "input",
            },
            {
              label: "受检单位签名",
              prop: "sjdwqm",
              type: "upload",
              fileText: "受检单位签名",
              loadText: "上传中...",
              limit: 1,
              propsHttp: {
                name: "data",
                url: "data",
              },
              action: "/api/manage/put-object",
              span: 8,
              hide: true,
            },
            {
              label: "抽检员1签名",
              prop: "cyyoqm",
              type: "upload",
              fileText: "上传抽检员1签名",
              loadText: "上传中...",
              limit: 1,
              propsHttp: {
                name: "data",
                url: "data",
              },
              action: "/api/manage/put-object",
              span: 8,
              hide: true,
            },
            {
              label: "抽检员2签名",
              prop: "cyytqm",
              type: "upload",
              fileText: "上传抽检员2签名",
              loadText: "上传中...",
              limit: 1,
              propsHttp: {
                name: "data",
                url: "data",
              },
              action: "/api/manage/put-object",
              span: 8,
              hide: true,
            },
            {
              label: "不合格问题备注",
              labelPosition: "top",
              prop: "bz",
              type: "input",
              span: 24,
            },
          ],
        },
        otherShow: false,
        cyid: "",
        cyForm: {},
        yxShow: false,
        fhData: [],
        detailOption: {
          height:'auto',
          calcHeight: 100,
          tip: false,
          border: true,
          index: true,
          addBtn: false,
          delBtn: false,
          viewBtn: false,
          searchBtn: false,
          selection: false,
          emptyBtn: false,
          columnBtn: false,
          refreshBtn: false,
          dialogClickModal: false,
          labelWidth: 140,
          align: 'center',
          menu: false,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "样品编号",
              prop: "cybh",
              type: "input",
              rules: [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur"
                }
              ],
            },
            {
              label: "样品大类",
              prop: "ypdl",
              type: "select",
              dicUrl: "/api/blade-system/dict/dictionary?code=sample_big_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              cascader: ['ypxl','cyid'],
              filterable: true,
            },
            {
              label: "样品细类",
              prop: "ypxl",
              type: "select",
              dicUrl: "/api/ypgl/ypxx/getYpxl?ypdl={{ypdl}}",
              props: {
                label: "label",
                value: "value"
              },
              filterable: true,
              cascader: ['cyid'],
            },
            {
              label: "样品名称",
              prop: "cyid",
              type: "select",
              filterable: true,
              dicUrl: "/api/ypgl/ypxx/list?current=1&size=9999&ypdl={{ypdl}}&ypxl={{ypxl}}",
              props: {
                label: "ypmc",
                value: "id",
                res: 'data.records'
              },
              hide: true,
              rules: [
                {
                  required: true,
                  message: "请选择",
                  trigger: "change"
                }
              ],
            },
            {
              label: "样品名称",
              prop: "ypmc",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              rules: [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur"
                }
              ],
            },
            {
              label:'检测项目',
              prop:'jcxm',
              type: "select",
              filterable: true,
              dicUrl: "/api/ypgl/jcxm/page?current=1&size=9999",
              props: {
                label: "xmmc",
                value: "id",
                res: 'data.records'
              },
            },
            {
              label: "检测值",
              prop: "jcz",
              type: "input",
            },
            {
              label: "检测方法",
              prop: "jcff",
              type: "select",
              dicUrl: "/api/blade-system/dict/dictionary?code=check_method",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
            },
            {
              label: "检测时间",
              prop: "jcsj",
              type: "input",
            },
            {
              label: "试剂信息",
              prop: "sjxx",
              type: "input",
            },
            {
              label: "检测结果",
              prop: "jcjg",
              type: "select",
              dicData: [
                {
                  label: '阴性',
                  value: '1',
                },
                {
                  label: '阳性',
                  value: '2',
                }
              ],
              slot: true // 启用插槽渲染
            },
            {
              label: "租户ID",
              prop: "tenantId",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "创建人",
              prop: "createUser",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "创建部门",
              prop: "createDept",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "修改人",
              prop: "updateUser",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "修改时间",
              prop: "updateTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "状态(0:关闭 1:开启)",
              prop: "status",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "是否已删除(0:正常 1:删除)",
              prop: "isDeleted",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
          ]
        }
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        // export excel, avue reconfig
        option.excelBtn = this.vaildData(this.permission.jcsj_export, false);

        return {
          addBtn: this.vaildData(this.permission.sbtj_add, false),
          viewBtn: this.vaildData(this.permission.sbtj_view, false),
          delBtn: this.vaildData(this.permission.sbtj_delete, false),
          editBtn: this.vaildData(this.permission.sbtj_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach((ele) => {
          ids.push(ele.id);
        });
        return ids.join(",");
      },
    },
    mounted() {
      this.uploadHeaders = {
        'Blade-Auth': 'bearer ' + getToken() ,
        'Authorization': 'Basic c2FiZXI6c2FiZXJfc2VjcmV0',
        'Tenant-Id': '000000'
      }
    },
    methods: {
      openPreview (index = 0) {
        const list = [
          {
            url: this.cyForm.yyzzt,
          }
        ]
        this.$ImagePreview(list, index, {
          closeOnClickModal: true,
          interval: 3000,
          click: (data, index) => {
          },
          beforeClose: () => {
          }
        });
      },
      downExcel(){
        downLoadModel().then(res=>{
          this.$exportCsv(res.data,'检测数据模板')
        })
      },
      handleAvatarSuccess(res, file) {
        if(res.code != 200){
          this.$message.error('导入信息不正确')
        }else{
          this.$message.success(res.msg)
          this.onLoad(this.page);
        }
      },
      showFh(e){
        this.yxShow = true
        this.currentId =e.id
        getJcList(1,9999,{jcid: e.id}).then(res=>{
          this.fhData =res.data.data.records;
        })
      },
      uploadCjy(res, file) {
        this.cyForm = {
          ...this.cyForm,
          cjySign: res.data
        };
      },
      uploadCjy2(res, file) {
        this.cyForm = {
          ...this.cyForm,
          cjdwSign: res.data
        };
      },
      handleClick(tab, event) {
        this.tab = tab.name;
      },
      print() {
        let printContents
        // if (this.tab == 1) {
        //    printContents = document.getElementById("printjcsj").innerHTML;
        // } else {
        //    printContents = document.getElementById("printjcsj2").innerHTML;
        // }
        printContents = document.getElementById("printjcsj").innerHTML;

        const originalContents = document.body.innerHTML;
        // const doc = new jsPDF();

        document.body.innerHTML = printContents;
        window.print();
        //   html2canvas(printContents).then(canvas => {
        //   // 添加 Canvas 对象到 PDF 文档
        //   doc.addImage(canvas.toDataURL('image/png'), 'PNG', 0, 0);

        //   // 下载 PDF 文件
        //   doc.save('example.pdf');
        // });
        // document.body.innerHTML = originalContents;
        window.location.reload();
      },
      saveOther() {
        const par = {
          ...this.otherForm,
          sjdwqm: this.otherForm.sjdwqm.join(","),
          cyyoqm: this.otherForm.cyyoqm.join(","),
          cyytqm: this.otherForm.cyytqm.join(","),
          cyid: this.cyid,
        };
        saveOther(par).then((res) => {
          this.$message({
            type: "success",
            message: res.msg,
          });
        });
      },
      showOther(e) {
        this.otherShow = true;
        this.cyid = e.id;
      },
      showPlan() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        console.log(this.selectionList);
        sessionStorage.setItem();
        this.$router.push({
          path: "/device/qjhc/plan",
        });
      },
      showDetail(e) {
        this.getDetail(e);
        this.dialogFormVisible = true;
      },
      getShopDetail(id){
        getShopDetail(id).then(res=>{
          this.cyForm = {
            ...this.cyForm,
            frmc: res.data.data.frmc,
            twh: res.data.data.twh,
            ywlx: res.data.data.ywlx,
            yyzzt: res.data.data.yyzzt,
            yyzzh: res.data.data.yyzzh,
          }
          delete this.cyForm.id
          delete this.cyForm.status
          delete this.cyForm.status
        })
      },
      getDetail(e) {
        getDetail(e.id).then((res) => {
        });
      },
      handleAdd() {
        this.getShopDetail(this.selectionList[0].sjdw)
        this.quicklyShow = true;
        this.cyForm= {
          ...this.cyForm,
          kjsInfo: this.selectionList[0].rwmc,
          sjdwmc: this.selectionList[0].sjdwmc,
          input1: this.selectionList[0].cysj,
          input2: this.selectionList[0].cybh,
        }
      },
      rowSave(row, done, loading) {
        add(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            window.console.log(error);
          }
        );
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            console.log(error);
          }
        );
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then((res) => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        if(params.cysj){
          this.query = {
            ...params,
            startDate: params.cysj[0],
            endDate: params.cysj[1],
          };
        }else{
          this.query = params
        }
        this.page.currentPage = 1;
        this.onLoad(this.page, this.query);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(
          page.currentPage,
          page.pageSize,
          Object.assign(params, this.query)
        ).then((res) => {
          const e = res.data.data;
          this.page.total = e.total;
          console.log(1, e.records);
          this.jcsjData = e.records;
          this.loading = false;
          // this.selectionClear();
        });
      },
    },
  };
</script>


<style lang="less" scoped>
  .span_info{
    font-size: 14px;
  }
  .quick_remark {
    width: 100%;
    height: 100px;
    margin-top: 10px;
  }

  .quickly_title {
    width: 100%;
    text-align: center;
    line-height: 50px;
    font-weight: bold;
  }

  .title_box {
    width: 100%;
    padding: 0 15px 10px 15px;
    border-bottom: 1px dotted #ccc;
    margin-bottom: 10px;
    font-weight: bold;
  }

  table {
    width: 100%;
    table-layout: fixed;
    font-size: 12px !important;
  }

  table,
  td,
  th {
    text-align: center;
    border: 1px solid #ccc;
    border-collapse: collapse;
    font-weight: normal;
    font-size: 12px !important;
  }

  table tr {
    height: 30px;
    padding: 0 5px !important;
    font-size: 12px !important;
    word-wrap: break-word !important;
  }

  table td {
    padding: 5px;
    font-size: 12px !important;
    word-wrap: break-word !important;
  }
</style>
<style>
  .desc_title {
    width: 120px !important;
    background: rgba(255, 255, 255, 0) !important;
  }

  .el-dialog__body {
    padding: 10px 20px !important;
  }

  .avatar-uploader .el-upload {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 24px;
    color: #8c939d;
    width: 128px;
    height: 64px;
    line-height: 64px;
    text-align: center;
  }

  .avatar {
    width: 128px;
    height: 64px;
    display: block;
  }
  @media print {
  /* 隐藏页眉 */
  @page {
    margin-top: 0;
  }

  /* 隐藏浏览器的页眉和页脚 */
  @page {
    size: auto;
    margin: 0;
  }
}
</style>
