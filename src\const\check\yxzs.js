export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  addBtn: false,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  align: 'center',
  menu: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "所属任务",
      prop: "rwid",
      type: "tree",
      parent:false,
      dicUrl: "/api/blade-system/dept/tree",
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      search: true,
      searchSpan: 4,
    },
    {
      label: "受检单位",
      prop: "sjdw",
      type: "input",
    },
    {
      label: "采样编号",
      prop: "cybh",
      type: "date",
    },
    {
      label: "采样日期",
      prop: "cysj",
      type: "date",
      valueFormat: "yyyy-MM-dd"
    },
    {
      label: "检测项目",
      prop: "jcxm",
      type: "input",
      search: true,
      searchSpan: 4,
      filterable: true,
      dicUrl: "/api/ypgl/jcxm/page?current=1&size=9999",
      props: {
        label: "xmmc",
        value: "xmbh",
        res: 'data.records'
      },
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "检测时间",
      prop: "createTime",
      type: "input",
      viewDisplay: true,
    },
    {
      label: "样品名称",
      prop: "ypmc",
      type: "input",
      search: true,
      searchSpan: 4,
    },
    // {
    //   label: "阳性确认状态",
    //   prop: "yxqr",
    //   search: true,
    //   searchSpan: 4,
    //   type: "select",
    //   dicData: [
    //     {
    //       label: '未确认',
    //       value: '0',
    //     },
    //     {
    //       label: '已确认',
    //       value: '1',
    //     }
    //   ],
    // },
    // {
    //   label: "转定量状态",
    //   prop: "zdl",
    //   search: true,
    //   searchSpan: 4,
    //   type: "select",
    //   dicData: [
    //     {
    //       label: '未转',
    //       value: 0,
    //     },
    //     {
    //       label: '已转',
    //       value: 1,
    //     }
    //   ],
    // },
    // {
    //   label: "阳性处置",
    //   prop: "yxcz",
    //   search: true,
    //   searchSpan: 4,
    //   type: "select",
    //   dicData: [
    //     {
    //       label: '未处置',
    //       value: '0',
    //     },
    //     {
    //       label: '已处置',
    //       value: '1',
    //     }
    //   ],
    // },

    {
      label: "最终结果",
      prop: "zzjg",
      search: true,
      searchSpan: 4,
      type: "select",
      dicData: [
        {
          label: '无效',
          value: null,
        },
        {
          label: '阴性',
          value: 1,
        },
        {
          label: '阳性',
          value: 2,
        }
      ],
    },
    {
      label: "追溯状态",
      prop: "zszt",
      search: true,
      searchSpan: 4,
      type: "select",
      dicData: [
        {
          label: '追溯中',
          value: '0',
        },
        {
          label: '追溯完成',
          value: '1',
        }
      ],
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    // {
    //   label: "创建时间",
    //   prop: "createTime",
    //   type: "input",
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   hide: true,
    // },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
