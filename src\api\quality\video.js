import request from '@/router/axios';
//
// export const getList = () => {
//   return request({
//     url: '/api/blade-system/dict/dictionary?code=video_list',
//     method: 'get',
//     params: {
//     }
//   })
// }

export const getList = (current, size, params) => {
  return request({
    url: '/api/splb/lb/list?descs=update_time',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


