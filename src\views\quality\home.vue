<template>
  <basic-container>
    <el-row :gutter="20">
      <el-col :span="14">
        <div class="chart_title_box">
          <span>质量控制记录</span>
        </div>
        <el-table
          :data="tableData"
          height="600"
          border
          style="width: 100%;border: 1px solid #04A7B3">
          <el-table-column
            prop="date"
            label="排名">
          </el-table-column>
          <el-table-column
            prop="name"
            label="检测室">
          </el-table-column>
          <el-table-column
            prop="address"
            label="检测次数">
          </el-table-column>
          <el-table-column
            prop="address"
            label="合格率">
          </el-table-column>
          <el-table-column
            prop="address"
            label="阳性率">
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="10">
        <div class="chart_title_box">
          <span>盲样考核占比统计</span>
        </div>
        <div id="pie_box1"></div>
        <div class="chart_title_box" style="margin-top: 10px">
          <span>不同市场类型占比分析</span>
        </div>
        <div id="pie_box2"></div>
      </el-col>
    </el-row>
    <div>
      <div class="chart_title_box">
        <span>人员考核统计分析</span>
      </div>
      <div id="people_bar"></div>
    </div>
    <el-row :gutter="20" style="margin-bottom: 20px">
      <el-col :span="14">
        <div class="chart_title_box">
          <span>检测师考核分析</span>
        </div>
        <div id="line_chart"></div>
      </el-col>
      <el-col :span="10">
        <div class="chart_title_box">
          <span>盲样考核占比统计</span>
        </div>
        <div id="pie_box3"></div>
        <div class="chart_title_box" style="margin-top: 10px">
          <span>不同市场类型占比分析</span>
        </div>
        <div id="pie_box4"></div>
      </el-col>
    </el-row>
  </basic-container>
</template>

<script>
  export default {
    name: "chart",
    data(){
      return {
        day: 1,
        tableData:[]
      }
    },
    mounted() {
      this.initPie1()
      this.initPie2()
      this.initPeopleBar()
      this.initLine()
      this.initPie3()
      this.initPie4()
    },
    methods:{
      initPie1(){
        const chartDom = document.getElementById('pie_box1');
        const myChart = this.echarts.init(chartDom);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: 'center',
            right: '5%',
            align: 'right',
            orient: 'vertical'
          },
          series: [
            {
              name: '盲样考核占比统计',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 40,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 1048, name: '农药残留' },
                { value: 735, name: '违禁药品' },
                { value: 580, name: '重金属' },
                { value: 484, name: '瘦肉精' },
                { value: 300, name: '非法添加剂' }
              ]
            }
          ]
        };
        option && myChart.setOption(option);
      },
      initPie2(){
        const chartDom = document.getElementById('pie_box2');
        const myChart = this.echarts.init(chartDom);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: 'center',
            right: '5%',
            align: 'right',
            orient: 'vertical'
          },
          series: [
            {
              name: '不同市场类型占比统计',
              type: 'pie',
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 40,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 1048, name: '超市' },
                { value: 735, name: '农贸市场' },
                { value: 580, name: '购物中心' },
                { value: 484, name: '海鲜市场' },
                { value: 300, name: '添加剂市场' }
              ]
            }
          ]
        };
        option && myChart.setOption(option);
      },
      initPeopleBar(){
        const chartDom = document.getElementById('people_bar');
        const myChart = this.echarts.init(chartDom);
        const option = {
          legend: {
            top: '5%'
          },
          grid: {
            containLabel: true,
            top: '15%',
            bottom: '5%'
          },
          tooltip: {},
          dataset: {
            source: [
              ['product', '到岗率', '任务完成率'],
              ['1月', 85,35],
              ['2月', 81,33],
              ['3月', 83,31],
              ['4月', 82,32],
              ['5月', 84,35],
            ]
          },
          xAxis: { type: 'category' },
          yAxis: {},
          series: [{ type: 'bar',barWidth: '20%' }, { type: 'bar',barWidth: '20%' }]
        };
        option && myChart.setOption(option);
      },
      initLine(){
        const chartDom = document.getElementById('line_chart');
        const myChart = this.echarts.init(chartDom);
        const option = {
          legend: {
            top: '5%',
            data: ['到岗率', '合格率', '巡查率'],
          },
          grid: {
            top: '15%',
            left: '5%',
            right: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['XX市场', 'XX市场', 'XX市场', 'XX市场', 'XX市场', 'XX市场', 'XX市场']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '到岗率',
              type: 'line',
              stack: 'Total',
              label: {
                show: true,
                position: 'top'
              },
              data: [120, 132, 101, 134, 90, 230, 210]
            },
            {
              name: '合格率',
              type: 'line',
              stack: 'Total',
              label: {
                show: true,
                position: 'top'
              },
              data: [220, 182, 191, 234, 290, 330, 310]
            },
            {
              name: '巡查率',
              type: 'line',
              stack: 'Total',
              label: {
                show: true,
                position: 'top'
              },
              data: [150, 232, 201, 154, 190, 330, 410]
            },
          ]
        };
        option && myChart.setOption(option);
      },

      initPie3(){
        const chartDom = document.getElementById('pie_box3');
        const myChart = this.echarts.init(chartDom);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: 'center',
            right: '5%',
            align: 'right',
            orient: 'vertical'
          },
          series: [
            {
              name: '到岗率统计',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 1048, name: '到岗' },
                { value: 735, name: '请假' },
                { value: 580, name: '迟到' },
              ]
            }
          ]
        };
        option && myChart.setOption(option);
      },
      initPie4(){
        const chartDom = document.getElementById('pie_box4');
        const myChart = this.echarts.init(chartDom);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: 'center',
            right: '5%',
            align: 'right',
            orient: 'vertical'
          },
          series: [
            {
              name: '合格率',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 1048, name: '合格数' },
                { value: 735, name: '不合格数' },
              ]
            }
          ]
        };
        option && myChart.setOption(option);
      },

    }

  }
</script>

<style scoped lang="less">
  #line_chart{
    width: 100%;
    height: 600px;
    border: 1px solid #04A7B3;
  }

  #people_bar{
    width: 100%;
    height: 300px;
    margin: 10px 0;
    border: 1px solid #04A7B3;
  }
  #pie_box1, #pie_box2,#pie_box3,#pie_box4{
    width: 100%;
    height: 275px;
    padding: 10px;
    box-sizing: border-box;
    border: 1px solid #04A7B3;
  }
  .chart_box{
    width: 100%;
    height: 200px;
    padding: 10px;
    box-sizing: border-box;
  }

  .chart_title_box{
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

</style>
