export default {
  height: 'auto',
  calcHeight: 30,
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  align: 'center',
  labelWidth: 120,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "来源类型",
      prop: "lylx",
      type: "select",
      dicData: [
        {
          label: '新购',
          value: '新购',
        },
        {
          label: '调转',
          value: '调转',
        },
        {
          label: '修复',
          value: '修复'
        },
      ],
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "blur"
        }
      ],
    },
    {
      label: "归属任务",
      prop: "rwid",
      width: 150,
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      searchSpan:4,
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "blur"
        }
      ],
    },
    {
      label: "设备编号",
      prop: "sbbh",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "设备名称",
      prop: "sbmc",
      type: "input",
      searchSpan:4,
      search: true,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "设备厂家",
      prop: "sbcj",
      width: 150,
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "所属区域",
      prop: "ssqy",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "出厂编号",
      prop: "ccbh",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "生产日期",
      prop: "scrq",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "blur"
        }
      ],
    },
    {
      label: "入库日期",
      prop: "rkrq",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      searchSpan: 6,
      search: true,
      searchRange:true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "blur"
        }
      ],
    },
    {
      label: "固定资产编号",
      prop: "gdzcbh",
      type: "input",
      width: 100,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "系统管理",
      prop: "xtgl",
      type: "select",
      dicData: [
        {
          label: '线上',
          value: '线上',
        },
        {
          label: '线下',
          value: '线下',
        }
      ],
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "blur"
        }
      ],
    },

    {
      label: "标准仪器名称",
      prop: "bzyqmc",
      type: "select",
      dicUrl: "/api/blade-system/dict/dictionary?code=standard_device",
      filterable: true,
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      width: 100,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "blur"
        }
      ],
    },
    // {
    //   label: "设备管理员",
    //   prop: "sbgly",
    //   type: "select",
    //   dicUrl: "/api/blade-user/page?current=1&size=100000&deptId=",
    //   props: {
    //     label: "name",
    //     value: "id",
    //     res: 'data.records'
    //   },
    //   width: 100,
    // },
    {
      label: "设备管理员",
      prop: "sbgly",
      type: "input",
      width: 100,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "设备类型",
      prop: "sblx",
      type: "select",
      dicData: [
        {
          label: '设备',
          value: '设备',
        },
        {
          label: '设施',
          value: '设施',
        }
      ],
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "blur"
        }
      ],
    },
    {
      label: "设备状态",
      prop: "sbzt",
      type: "select",
      dicData: [
        {
          label: '已校准',
          value: '已校准',
        },
        {
          label: '待校准',
          value: '待校准',
        },
        {
          label: '无需校准',
          value: '无需校准'
        },
      ],
      searchSpan:4,
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "blur"
        }
      ],
    },
    {
      label: "规格型号",
      prop: "ggxh",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "备注",
      prop: "bz",
      type: "input",
      hide: true,
    },

    {
      label: "设备照片",
      prop: "shd",
      type: 'upload',
      listType: 'picture-card',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: true,
      hide: true,
      multiple: true,
      rules: [
        {
          required: true,
          message: "请上传",
          trigger: "blur"
        }
      ],

    },
    {
      label: "溯源照片",
      prop: "syzp",
      type: 'upload',
      listType: 'picture-card',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: true,
      hide: true,
      multiple: true,
      rules: [
        {
          required: true,
          message: "请上传",
          trigger: "blur"
        }
      ],

    },

    {
      label: "下次校准时间",
      prop: "xcxzsj",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: 100,
    },

    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
