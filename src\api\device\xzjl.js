import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/sb/sbtj/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getRecordList = (sbid) => {
  return request({
    url: `/api/sb/xzjl/page?current=1&size=999`,
    method: 'get',
    params: {
      sbid
    }
  })
}

export const getUserList = (current, size, params, deptId) => {
  return request({
    url: '/api/blade-user/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      deptId,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/sb/xzjl/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/sb/xzjl/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/sb/xzjl/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/sb/xzjl/submit',
    method: 'post',
    data: row
  })
}

// 修改设备统计
export const updateSb = (row) => {
  return request({
    url: '/api/sb/sbtj/submit',
    method: 'post',
    data: row
  })
}


