<template>
  <basic-container>
    <div class="chart_title_box">宣传活动数量统计</div>
    <div id="num_chart"></div>
    <div class="chart_title_box">宣传活动投入产出比统计</div>
    <div id="ratio_chart"></div>
  </basic-container>
</template>

<script>
  export default {
    name: "chart",
    data(){
      return {}
    },
    mounted() {
      this.initNumChart()
      this.initRatioChart()
    },
    methods:{
      initNumChart(){
        const chartDom = document.getElementById('num_chart');
        const myChart = this.echarts.init(chartDom);
        const option = {
          legend: {
            data: ['宣传活动'],
          },
          xAxis: {
            type: 'category',
            data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '宣传活动',
              data: [120, 200, 150, 80, 70, 110, 130,125,95,120,138,146],
              type: 'bar',
              barWidth : 50,
            }
          ]
        };

        option && myChart.setOption(option);
      },
      initRatioChart(){
        const chartDom = document.getElementById('ratio_chart');
        const myChart = this.echarts.init(chartDom);
        const option = {
          legend: {
            data: ['投入','产出'],
          },
          xAxis: {
            type: 'category',
            data: ['xx市场', 'xx市场', 'xx市场', 'xx市场', 'xx市场', 'xx市场', 'xx市场', 'xx市场', 'xx市场', 'xx市场', 'xx市场', 'xx市场']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '投入',
              data: [30, 40, 55, 35, 21, 45, 30,38,29,25,38,46],
              type: 'bar',
              barWidth : 30,
            },
            {
              name: '产出',
              data: [120, 200, 150, 80, 70, 110, 130,125,95,120,138,146],
              type: 'bar',
              barWidth : 30,
            }
          ]
        };

        option && myChart.setOption(option);
      }
    }

  }
</script>

<style scoped>
  #num_chart, #ratio_chart{
    width: 100%;
    height: 500px;
  }

</style>
