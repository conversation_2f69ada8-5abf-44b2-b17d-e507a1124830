<template>
  <div style="height: 100%">
    <div class="home_index">
      <div class="top_logo">
        <img src="../../assets/img/logo.jpg" alt="" style="width: 34%">
        <div style="font-size: 10px;">
          请搜索小程序:<br>华测快检服务在线下单<br>服务热线:<br>0532-58515871
        </div>
      </div>
      <div class="title_box">华测检测最新检测记录</div>
      <div class="item_title">商户信息</div>
      <div class="card_item">
        <div>企业名称：{{jyhmc}}</div>
        <div>联系人：{{frmc}}</div>
        <div>受检单位：{{scmc}}</div>
      </div>
      <div class="item_title">营业执照</div>
      <div class="card_item">
        <!-- 使用 v-for 循环显示每张图片 -->
        <img v-for="(src, index) in yyzztArray" :key="index" :src="src" alt="" style="width: 100%">

      </div>
      <div class="item_title">检测记录</div>
      <div class="card_item">
        <el-form class="search-box">
          <el-form-item label="开始时间">
            <el-date-picker
              v-model="searchParams.startTime"
              type="date"
              value-format="yyyy-MM-dd"        style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="结束时间">
            <el-date-picker
              v-model="searchParams.endTime"
              type="date"
              value-format="yyyy-MM-dd"        style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="任务名称">
            <el-select
              v-model="searchParams.taskName"
              filterable
              remote
              reserve-keyword
              placeholder="请输入任务名称"
              :remote-method="fetchTasks"
              :loading="loading"    style="width: 100%"
            >
              <el-option
                v-for="item in taskOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :level="item.level"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handleSearch"
              icon="el-icon-search"        style="width: 100%"
            >搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="item_title">抽检信息</div>
      <div class="card_item">
        <div class="check_item">
          <div>任务名称</div>
          <div>样品编号: TJMYK230831016</div>
          <div>样品名称: 猪肉</div>
          <div>采样时间: 2023-08-31 00:00:00</div>
          <div>检测项目: 沙丁胺醇（组织）</div>
          <div>检测结果: 阴性</div>
          <div>检测时间: 2023-08-31 00:00:00</div>
          <div>进货量: 510kg</div>
          <div>进货时间: 2023-08-29</div>
          <div>供应商: 唐山双汇食品有限责任公司</div>
          <div>供应商地址: /</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import {getDetail} from '@/api/desk/jyh'
  import {getDeptTree, getOpenTree, getTree} from '@/api/system/dept' // 注意实际路径可能不同
  export default {
    name: "operatorDetail",
    data(){
      return {
        searchParams: {
          taskName: '',
          startTime: '',
          endTime: ''
        },
        taskOptions: [],
        loading: false,
        info:{}
        ,id:''
        ,jyhmc:''
        ,frmc:''
        ,scmc:''
        ,yyzzt:''
        ,yyzztArray: [] // 新增数组来存储拆分后的图片 URL
      }
    },
    mounted() {
      this.id = this.$route.query.id
      this.jyhmc = this.$route.query.jyhmc
      this.frmc = this.$route.query.frmc
      this.scmc = this.$route.query.scmc
      this.yyzzt = this.$route.query.yyzzt
      if (this.yyzzt) {
        this.yyzztArray = this.yyzzt.split(',');
      }
      this.fetchTasks(); // 添加初始化加载
    },

    methods:{
        async fetchTasks(query) {
          this.loading = true;
          try {
            const params = query ? { title: query } : {};
            const response = await getOpenTree(params);
            const data = response.data.data;
            const flattenData = this.flattenTree(data);
            this.taskOptions = flattenData.map(item => ({
              value: item.id,
              label: this.generateLabel(item),
              level: item.level // 添加层级数据用于样式
            }));
          } catch (e) {
            console.error('获取数据失败', e);
          } finally {
            this.loading = false;
          }
        },
        // 递归扁平化树结构
      flattenTree(treeData) {
        // 空数据保护
        if (!treeData) return [];

        // 类型校验
        if (!Array.isArray(treeData)) {
          console.error('树形数据必须是数组，当前类型:', typeof treeData);
          return [];
        }

        const result = [];

        const flatten = (nodes, level = 0) => {
          // 二次校验
          if (!Array.isArray(nodes)) {
            console.warn('非数组节点，层级:', level, '数据:', nodes);
            return;
          }

          nodes.forEach(node => {
            if (!node) return; // 空节点保护

            // 克隆对象避免污染原始数据
            const newNode = { ...node };
            newNode.level = level;
            result.push(newNode);

            // 处理子节点
            if (newNode.children) {
              if (Array.isArray(newNode.children)) {
                flatten(newNode.children, level + 1);
              } else {
                console.warn('非数组子节点:', newNode.children);
              }
            }
          });
        };

        try {
          flatten(treeData);
        } catch (e) {
          console.error('树形数据处理异常:', e);
        }

        return result;
      },
      // 生成带层级缩进的标签
        generateLabel(item) {
          return '　'.repeat(item.level) + '├ ' + item.title;
        }


    }
  }
</script>

<style scoped lang="less">
  .card_item div{
    margin: 10px 0;
  }
  .card_item{
    width: 100%;
    border: 1px solid #eee;
    border-radius: 15px;
    padding: 10px;
  }
  .item_title {
    width: 100px;
    height: 40px;
    border-radius: 30px;
    background: #00a680;
    color: #fff;
    text-align: center;
    line-height: 40px;
    margin: 20px auto;
  }

  .title_box {
    width: 100%;
    text-align: center;
    font-size: 24px;
  }
  .top_logo{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  .home_index {
    padding: 20px;
    height: 100%;
    box-sizing: border-box;
    overflow: auto;
  }
  .search-box {
    .el-form-item {
      width: 100%;
      margin-bottom: 15px;

      &__content {
        width: 100%;
      }

      .el-date-editor,
      .el-input {
        width: 100% !important;
      }
    }

    .el-button {
      margin-top: 10px;
    }
  }

  .el-select-dropdown__item {
    &[level="1"] { padding-left: 30px; }
    &[level="2"] { padding-left: 40px; }
    &[level="3"] { padding-left: 50px; }
    // 可根据需要继续添加更多层级样式
  }

</style>
