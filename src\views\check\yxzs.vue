<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
      </template>
      <template slot-scope="{row}" slot="yxqr">
        <el-button v-if="row.yxqr==0" size="small" type="text" @click="showFh(row)">阳性复核</el-button>
        <span style="cursor:pointer;" v-if="row.yxqr==1" size="small" @click="showFh(row)">已复核</span>
      </template>
      <template slot-scope="{row}" slot="yxcz">
        <el-button size="small" v-if="row.yxcz==0" type="text" @click="showCz(row)">未处置</el-button>
        <span style="cursor:pointer;" v-if="row.yxcz==1" @click="showCz(row)">已处置</span>
      </template>
      <template slot-scope="{row}" slot="zdl">
        <span>{{row.zdl == 1?'已转':row.zdl == 0?'未转':''}}</span>
        <br>
        <el-button v-if="row.zdl == 0" type="text" size="small" @click="goRation(row)">去转定量</el-button>
      </template>
      <template slot-scope="{row}" slot="yxzs">
        <el-button size="small" type="text">追溯中</el-button>
      </template>
      <template slot-scope="{row}" slot="zzjg">
        <span v-if="row.zzjg==1" >阴性</span>
        <div v-if="row.zzjg==2" style="color: red">
          阳性
          <div>
            <el-button size="small" type="text" @click="showZS(row)">阳性追溯</el-button>
          </div>
        </div>

      </template>
    </avue-crud>

    <el-dialog :visible.sync="yxShow" append-to-body title="阳性结果确认">
      <avue-crud :option="fhOption"
                 :data="fhData"
                 ref="crud"
      >
      </avue-crud>
      <div style="margin: 10px 0">最终结果</div>
      <el-select size="small" v-model="finalResult" placeholder="请选择">
        <el-option label="阴性" :value="1"></el-option>
        <el-option label="阳性" :value="2"></el-option>
      </el-select>
      <div slot="footer" style="display: flex;justify-content: space-between">
        <div>
        </div>
        <el-button type="primary" @click="fhOk">复核确认</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="czShow" append-to-body title="阳性处置记录">
      <avue-form :option="czOption" v-model="czForm" @submit="czSave"></avue-form>
    </el-dialog>

    <el-dialog :visible.sync="retraceShow" width="75%" append-to-body title="阳性追溯">
      <avue-crud :option="retraceOption"
                 :data="retraceData"
                 ref="crud"
                 :table-loading="retraceLoading"
                 :page.sync="retracePage"
                 v-model="retraceForm"
                 @row-update="retraceUpdate"
                 @row-save="retraceSave"
                 @row-del="retraceDel"
                 @size-change="sizeRetraceChange"
                 @current-change="currentRetraceChange"
                 @on-load="onRetraceLoad"
      >
        <template #menu-form="{row,index,type}">
          <el-button type="primary"
                     icon="el-icon-check"
                     v-if="type=='add'"
                     @click="$refs.crud.rowSave()">自定义新增</el-button>
          <el-button type="primary"
                     icon="el-icon-check"
                     v-if="type=='edit'"
                     @click="$refs.crud.rowUpdate()">自定义修改</el-button>
          <el-button type="primary"
                     icon="el-icon-check"
                     @click="finishRetrace()">完成追溯</el-button>
        </template>
      </avue-crud>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getDetail as getYpxxDetail} from "@/api/sample/ypxx";
import {getFhList, getJcList, jgFh, getYxczDetail, getReviewList, getList, getDetail, add, update, remove, czSumbit, updateCysj} from "@/api/check/yxcz";
import {getRetraceList, getRetraceDetail, addRetrace, updateRetrace, removeRetrace} from "@/api/check/yxzs";
import option from "@/const/check/newyxzs";
import {mapGetters} from "vuex";

export default {
  data() {
    return {
      czShow: false,
      finalResult: 1,
      fhData: [],
      yxShow: false,
      filterDate:[],
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: option,
      data: [{}],
      czOption:{
        labelWidth: '140',
        column: [
          {
            label:'采样编号',
            prop:'cybh',
            type:'input',
            readonly: true
          },
          {
            label:'检测ID',
            prop:'cyid',
            type:'input',
            readonly: true
          },
          {
            label:'处置人',
            prop:'czr',
            type:'input',
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur"
              }
            ],
          },
          {
            label:'处置时间',
            prop:'czsj',
            type:'date',
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur"
              }
            ],
          },
          {
            label:'销毁重量（kg）',
            prop:'xhzl',
            type:'input',
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur"
              }
            ],
          },
          {
            label:'备注',
            prop:'bz',
            type:'input'
          },
          {
            label:'图片上传',
            prop:'tp',
            type: 'upload',
            listType: 'picture-card',
            dataType: 'string',
            propsHttp: {
              name: 'data',
              url: 'data'
            },
            multiple: true,
            action: '/api/manage/put-object',
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur"
              }
            ],
          },
          {
            label:'视频上传',
            prop:'sp',
            type: 'upload',
            listType: 'picture-card',
            dataType: 'string',
            multiple: true,
            propsHttp: {
              name: 'data',
              url: 'data'
            },
            action: '/api/manage/put-object',
            span: 24,
          },
        ]
      },
      czForm:{},
      currentId: '',
      fhOption: {
        labelWidth: '140',
        menu: false,
        maxHeight: '350',
        searchShowBtn: false,   // 栏目折叠显隐
        columnBtn: false,       // 操作列显隐
        refreshBtn: false,
        addBtn: false,
        column: [
          {
            label:'采样编号',
            prop:'cybh',
            type:'input',
            readonly: true
          },
          {
            label:'样品名称',
            prop:'ypmc',
            type:'input',
            readonly: true
          },
          {
            label:'检测项目',
            prop:'jcxm',
            type: "select",
            filterable: true,
            dicUrl: "/api/ypgl/jcxm/page?code=check_method?current=1&size=9999",
            props: {
              label: "xmmc",
              value: "id",
              res: 'data.records'
            },
          },
          {
            label:'检测时间',
            prop:'jcsj',
            type:'input',
            readonly: true
          },
          {
            label:'检测人员',
            prop:'jcry',
            type:'input',
            readonly: true
          },
          {
            label:'检测结果',
            prop:'jcjg',
            type:'select',
            dicData:[
              {
                label: '无效',
                value: null,
              },
              {
                label: '阴性',
                value: '1',
              },
              {
                label: '阳性',
                value: '2',
              },
            ]
          },
        ]
      },
      retraceShow: false,
      sampleId: null,
      retraceOption: {
        height:'auto',
        calcHeight: 30,
        dialogWidth: '70%',
        tip: false,
        searchMenuSpan: 4,
        border: true,
        index: true,
        addBtn: true,
        viewBtn: true,
        selection: true,
        searchShow: false,
        searchShowBtn: false,   // 栏目折叠显隐
        columnBtn: false,       // 操作列显隐
        excelBtn: false,       // 导出Excel
        refreshBtn: false,      // 刷新
        printBtn: false,       // 表格打印导出
        filterBtn: false,
        dialogClickModal: false,
        align: 'center',
        labelWidth: 120,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "供应商名称",
            prop: "gysmc",
            type: "input",
            search: true,
            rules: [{
              required: true,
              message: "请输入",
              trigger: "blur"
            }],
          },
          {
            label: "供应商联系人",
            prop: "gyslxr",
            type: "input",
            rules: [{
              required: true,
              message: "请输入",
              trigger: "blur"
            }],
          },
          {
            label: "联系电话",
            prop: "lxdh",
            type: "input",
            rules: [{
              required: true,
              message: "请输入",
              trigger: "blur"
            }],
          },
          {
            label: "填报人员",
            prop: "tbry",
            type: "input",
            rules: [{
              required: true,
              message: "请输入",
              trigger: "blur"
            }],
          },
          {
            label: "填报单位",
            prop: "tbdw",
            type: "input",
            rules: [{
              required: true,
              message: "请输入",
              trigger: "blur"
            }],
          },
          {
            label: "关联样品",
            prop: "glyp",
            type: "input",
            rules: [{
              required: true,
              message: "请输入",
              trigger: "blur"
            }],
          },
          {
            label: '追溯完成',
            prop: 'isFinish',
            type: 'checkbox',
            dicData: [{
              label: '完成',
              value: 1
            }]
          },
          {
            label: "转至任务",
            prop: "rwid",
            type: "tree",
            dicUrl: "/api/blade-system/dept/tree",
            props: {
              label: "title",
              value: "id",
              res: 'data',
            },
          },
          {
            label: "上传凭证",
            prop: "tp",
            type: 'upload',
            listType: 'picture-img',
            propsHttp: {
              url: 'data',
              name: 'data'
            },
            action: '/api/manage/put-object',
          },
          {
            label: "租户ID",
            prop: "tenantId",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建人",
            prop: "createUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建部门",
            prop: "createDept",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建时间",
            prop: "createTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改人",
            prop: "updateUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改时间",
            prop: "updateTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "状态(0:关闭 1:开启)",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "是否已删除(0:正常 1:删除)",
            prop: "isDeleted",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
        ]
      },
      retraceData: [],
      retraceForm:{},
      retraceLoading: true,
      retracePage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      retraceQuery: {},

    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      // export excel, avue reconfig
      option.excelBtn = this.vaildData(this.permission.yxcz_export, false);
      return {
        addBtn: this.vaildData(this.permission.yxcz_add, false),
        viewBtn: this.vaildData(this.permission.yxcz_view, false),
        delBtn: this.vaildData(this.permission.yxcz_delete, false),
        editBtn: this.vaildData(this.permission.yxcz_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  watch:{
    'form.glyp': {
      handler(newVal) {
        if(newVal>0)
        {
          getYpxxDetail(newVal).then(result=>{
            this.form.ypmc = result.data.data.ypmc;
            this.form.glypmc = result.data.data.ypmc;
          });
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    finishRetrace(){
      const pramas = {
        id: this.sampleId,
        zszt: 1
      }
      updateCysj(pramas).then(res=>{
        console.log(2121,res)
      })
    },
    goRation(e){
      this.$router.push({
        name: '阳性转定量首页',
        params: {
          item: e
        }
      })
    },
    showZS(e){
      this.sampleId =e.cyid
      this.retraceShow = true
      this.onRetraceLoad(this.retracePage)
    },
    onRetraceLoad(page, params = {}) {
      this.retraceLoading = true;
      this.retraceQuery.cyid = this.sampleId
      getRetraceList(page.currentPage, page.pageSize, Object.assign(params, this.retraceQuery)).then(res => {
        const e = res.data.data;
        this.retracePage.total = e.total;
        this.retraceData = e.records;
        this.retraceLoading = false;
      });
    },
    retraceUpdate(row, done, loading){
      delete row.isFinish
      if(this.retraceForm.isFinish == 1){
        this.finishRetrace()
      }
      addRetrace(row).then(() => {
        this.onRetraceLoad(this.retracePage);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    retraceSave(row, done, loading){
      const par = {
        ...row,
        cyid: this.sampleId
      }
      delete par.isFinish
      if(this.retraceForm.isFinish == 1){
        this.finishRetrace()
      }
      addRetrace(par).then(() => {
        this.onRetraceLoad(this.retracePage);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    retraceDel(row, done, loading){
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onRetraceLoad(this.retracePage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    currentRetraceChange(currentPage){
      this.retracePage.currentPage = currentPage;
    },
    sizeRetraceChange(pageSize){
      this.retracePage.pageSize = pageSize;
    },
    czSave(e,done){
      // const imgList = this.czForm.tp.map(item=>{
      //   return item.value
      // })
      // const videoList = this.czForm.sp.map(item=>{
      //   return item.value
      // })
      // const par = {
      //   ...this.czForm,
      //   tp: imgList,
      //   sp: videoList,
      // };
      czSumbit(this.czForm).then(res=>{
        done()
      })
      jgFh({
        yxcz: 1,
        id: this.currentId
      }).then(res=>{
        if(res.data.code==200){
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.czShow = false
          this.onLoad(this.page);
        }
      })
    },
    fhOk(){
      jgFh({
        yxqr: 1,
        zzjg: this.finalResult,
        id: this.currentId
      }).then(res=>{
        if(res.data.code==200){
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.yxShow = false
          this.onLoad(this.page);
        }
      })
    },
    showCz(e){
      this.czShow = true
      this.currentId =e.id
      this.getYxczDetail(e)
    },

    getYxczDetail(e){
      this.czForm = {}
      this.czForm.cyid = e.cyid
      this.czForm.cybh = e.cybh
      this.czForm.jcid = e.id
      getYxczDetail({
        jcid: e.id
      }).then(res=>{
        this.czForm = {
          ...res.data.data
        }
      })
    },
    showFh(e){
      this.yxShow = true
      this.currentId =e.id
      getJcList(1,99999,{jcid: e.id}).then(res=>{
        this.fhData =res.data.data.records;
      })
    },
    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchList(){
      this.page = 1
      this.query = {
        ...this.query,
      }
      this.onLoad(this.page, this.query)
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getReviewList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const e = res.data.data;
        this.page.total = e.total;
        this.data = e.records;
        this.loading = false;
      });
    }
  }
};
</script>

<style>
</style>
