<template>
    <div class="card_right_middle_charts" id="card_right_bottom_charts"></div>
</template>
<script>
import * as echarts from "echarts";
export default {
    name: "lineArea",
    props: {
        title: {
            type: String,
            default: "",
        },
        xData: {
            type: Array,
            default: [],
        },
        series: {
            type: Array,
            default: [],
        },
    },
    mounted() {

    },
  watch:{
    xData:{
      handler(val){
        this.$nextTick(() => {
          this.getCardRightMiddleCharts();
        })
      },
      deep: true,
      immediate: true
    }
  },
    methods: {
        getCardRightMiddleCharts() {
            var chartDom = document.getElementById("card_right_bottom_charts");
            var myChart = echarts.init(chartDom);
            var option;
            debugger;

            option = {
                title: {
                    text: this.title,
                    top: "5%",
                    left: "center", //居中 相当于Y
                    textStyle: {
                        color: "#16CBD9", //文字颜色
                        fontSize: "16", //文字大小
                    },
                },
              tooltip: {
                trigger: "axis",
                axisPointer: {
                  type: "shadow",
                },
              },
                grid: {
                    left: "5%",
                    top: "20%",
                    right: "4%",
                    bottom: "5%",
                    containLabel: true,
                },
                xAxis: {
                    type: "category",
                    boundaryGap: false,
                    data: this.xData,
                    axisLabel: {
                        //y轴文字的配置
                        textStyle: {
                            color: "RGBA(22, 203, 217, 0.5)", //Y轴内容文字颜色
                        },
                    },
                  splitLine: { // 添加网格线配置
                    lineStyle: {
                      color: ['#666'], // 浅灰色
                    }
                  }
                },
                yAxis: {
                    type: "value",
                    axisLabel: {
                        //y轴文字的配置
                        textStyle: {
                            color: "RGBA(22, 203, 217, 0.5)", //Y轴内容文字颜色
                        },
                    },
                  splitLine: { // 添加网格线配置
                    lineStyle: {
                      color: ['#666'], // 浅灰色
                    }
                  }
                },
                series: this.series,
            };

            option && myChart.setOption(option);
        },
    },
};
</script>
