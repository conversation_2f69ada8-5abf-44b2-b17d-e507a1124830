<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <div style="display: flex;float: right">
          <el-button v-if="permission.smsj_import" type="primary"
                     size="small"
                     icon="el-icon-download"
                     plain
                     style="margin-left: 10px"
                     @click="downExcel">
            下载模板
          </el-button>
          <el-upload v-if="permission.smsj_import" :headers="uploadHeaders" :on-success="handleAvatarSuccess" :show-file-list="false" action="/api/jc/smsj/addExcel">
            <el-button type="primary"
                       size="small"
                       icon="el-icon-upload "
                       plain
                       style="margin-left: 10px">
              导入
            </el-button>
          </el-upload>
          <el-button type="danger"
                     size="small"
                     icon="el-icon-delete"
                     plain
                     style="margin-left: 10px"
                     v-if="permission.smsj_delete"
                     @click="handleDelete">删 除
          </el-button>
        </div>
      </template>
      <template slot-scope="{row}" slot="menu">
        <el-button type="text" size="mini" >二维码</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, downLoadModel, getDetail, getList, remove, sendEmail, update} from "@/api/desk/smsj";
import option from "@/const/desk/smsj";
import {mapGetters} from "vuex";
import {getToken} from '@/util/auth';
import {getDept} from "@/api/system/dept";
// import nodemailer from 'nodemailer';


  export default {
    data() {
      return {
        form: {
          sjdwmc: '',
          ypbh:''
        },
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        uploadHeaders:{},
        deptDetail: {}

      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        // export excel, avue reconfig
        option.excelBtn = this.vaildData(this.permission.smsj_export, false);

        return {
          addBtn: this.vaildData(this.permission.smsj_add, false),
          viewBtn: this.vaildData(this.permission.smsj_view, false),
          delBtn: this.vaildData(this.permission.smsj_delete, false),
          editBtn: this.vaildData(this.permission.smsj_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    mounted() {
      this.getDeptDetail()
      this.uploadHeaders = {
        'Blade-Auth': 'bearer ' + getToken() ,
        'Authorization': 'Basic c2FiZXI6c2FiZXJfc2VjcmV0',
        'Tenant-Id': '000000'
      }
      window.__smsj_component__ = this;
    },
    beforeDestroy() {
      delete window.__smsj_component__;
    },
    beforeOpen(done, type) {
      if (["add", "edit"].includes(type)) {
        this.form.sjdwmc = ''; // 重置值
      }
      done();
    },
    watch:{
      'form.ypdl': {
        handler(newVal) {
          let type = null
          if(newVal == 1){
            type = 'A'
          }
          if(newVal == 2){
            type = 'B'
          }
          if(newVal == 3){
            type = 'C'
          }
          if(newVal == 4){
            type = 'D'
          }
          if(newVal == 5){
            type = 'E'
          }
          if(newVal == 6){
            type = 'F'
          }
          if(newVal == 7){
            type = 'G'
          }
          this.form.ypbh = this.deptDetail.remark + type + new Date().getTime()
          this.form.cydbh = this.deptDetail.remark + type + new Date().getTime()
        },
        deep: true,
        immediate: true
      }
    },
    methods: {
      getDeptDetail(){
        getDept(this.userInfo.dept_id).then(res=>{
          this.deptDetail = res.data.data
        })
      },
      handleNodeClick(data,node) {
        //将data.ypdl 转成数字
        data.ypdl = parseInt(data.ypdl)
        const ypdlColumn = this.option.column.find(item => item.prop === 'ypdl');
        const ypdlMcColumn = this.option.column.find(item => item.prop === 'ypdlmc');

        this.$set(ypdlColumn, 'cascaderIndex',  data.ypdl-1); // 示例使用节点层级
        this.$set(ypdlMcColumn, 'cascaderIndex',  data.ypdl-1); // 示例使用节点层级
      },
      handleNodeClick2(data,val,prop) {
        console.log(data)
        this.form[prop] = data[val];
        // 如果需要强制更新视图
        this.$set(this.form, prop, data[val]);
      },
      setColumnValue(prop, value) {
        const column = this.option.column.find(item => item.prop === prop);
        if (column) {
          this.$set(column, 'value', value);
        } else {
          console.warn(`未找到 ${prop} 列配置`);
        }
      },
      downExcel(){
        downLoadModel().then(res=>{
          this.$exportCsv(res.data,'市民送检模板')
        })
      },
      handleAvatarSuccess(res, file) {
        if(res.code != 200){
          this.$message.error(res.msg)
        }else{
          this.$message.success(res.msg)
          this.onLoad(this.page);
        }
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          if(row.jcjg){
            this.toSendEmail(row)
          }
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          if(row.jcjg){
            this.toSendEmail(row, index, done, loading)
          }
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      toSendEmail(row, index, done, loading){
        sendEmail(row).then(res=>{
          if(res.data.code == 200){
            this.$message({
              type: "success",
              message: "发送成功"
            });
            const par = {
              ...row,
              sftz: 1
            }
            update(par).then(() => {
              this.onLoad(this.page);
              done();
            }, error => {
              loading();
            });
          }else{
            this.$message.error(res.message);
          }

        })
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        if(params.jcrq){
          this.query = {
            ...params,
            startDate: params.jcrq[0],
            endDate: params.jcrq[1]
          };
        }else{
          this.query = params
        }

        this.page.currentPage = 1;
        this.onLoad(this.page, this.query);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
