export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  labelWidth: 130,
  align: 'center',
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "质控方式",
      prop: "zkfs",
      type: "select",
      dicData: [
        {
          label: '盲样考核原始记录',
          value: '盲样考核原始记录',
        },
        {
          label: '留样再测原始记录',
          value: '留样再测原始记录',
        },
        {
          label: '人员比对原始记录',
          value: '人员比对原始记录',
        },
        {
          label: '方法对比原始记录',
          value: '方法对比原始记录',
        },
        {
          label: '仪器对比原始记录',
          value: '仪器对比原始记录',
        },
        {
          label: '试剂比对实验结果评价表',
          value: '试剂比对实验结果评价表',
        },
        {
          label: '人员监督原始记录',
          value: '人员监督原始记录',
        }
      ],
      searchSpan:4,
      width: 150,
      rules: [{
        required: true,
        message: "请选择",
        trigger: "blur"
      }],
    },
    {
      label: "组织者",
      prop: "zzz",
      type: "input",
    },
    {
      label: "实施时间",
      prop: "sssj",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      searchSpan:8,
      search: true,
      searchRange:true,
      rules: [{
        required: true,
        message: "请选择",
        trigger: "blur"
      }],
    },
    {
      label: "任务名称",
      prop: "rwid",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      parent:false,
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      search: true,
      width: 100,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "样品种类",
      prop: "ypzl",
      type: "input",
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "检测项目",
      prop: "jcxm",
      type: "select",
      searchSpan:4,
      search: true,
      filterable: true,
      dicUrl: "/api/ypgl/jcxm/page?code=check_method?current=1&size=9999",
      props: {
        label: "xmmc",
        value: "id",
        res: 'data.records'
      },
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "检测员",
      prop: "jcy",
      type: "input",
    },

    {
      label: "样品编号",
      prop: "cybh",
      type: "input",

    },
    {
      label: "样品名称",
      prop: "ypmc",
      type: "input",
      searchSpan:4,
      search: true,

    },
    {
      label: "检测方法",
      prop: "jcff",
      type: "select",
      filterable: true,
      dicUrl: "/api/blade-system/dict/dictionary?code=check_method",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      rules: [{
        required: true,
        message: "请选择",
        trigger: "blur"
      }],
    },
    {
      label: "输出材料",
      prop: "sccl",
      type: "input",
    },
    {
      label: "评测标准",
      prop: "pcbz",
      type: "input",
    },
    {
      label: "上传原始记录单",
      prop: "ysjl",
      type: 'upload',
      dataType: 'string',
      listType: 'picture-card',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: true,
      hide: true,
      multiple: true,
      tip: '可上传照片、视频等',
      rules: [
        {
          required: true,
          message: "请上传",
          trigger: "blur"
        }
      ],
      change(file, fileList) {
        if (file.value !==""){
          if (window.__zkjl_component__) {
            window.__zkjl_component__.setColumnValue('完成','wczt');

          }
        }else {
          if (window.__zkjl_component__) {
            window.__zkjl_component__.setColumnValue('','wczt');

          }
        }

      },

    },
    {
      label: "记录单文件",
      prop: "ysjlwj",
      type: 'upload',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: true,
      hide: true,
      multiple: true,
      tip: '可上传文件',
      change(file, fileList) {
        if (file.value !==""){
          if (window.__zkjl_component__) {
            window.__zkjl_component__.setColumnValue('完成','wczt');

          }
        }else {
          if (window.__zkjl_component__) {
            window.__zkjl_component__.setColumnValue('','wczt');

          }
        }

      },

    },

    {
      label: "完成状态",
      prop: "wczt",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      row: true,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "上传时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      width: 150,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
