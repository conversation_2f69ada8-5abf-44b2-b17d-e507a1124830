import request from '@/router/axios';
export const downLoadModel = () => {
  return request({
    url: '/api/xchd/hdjl/downLoadModel',
    method: 'get',
    responseType: 'blob',
    params: {
    }
  })
}
export const getList = (current, size, params) => {
  return request({
    url: '/api/xchd/hdjl/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/xchd/hdjl/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/xchd/hdjl/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/xchd/hdjl/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/xchd/hdjl/submit',
    method: 'post',
    data: row
  })
}

