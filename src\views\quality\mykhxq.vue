<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.mykhxq_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template slot-scope="{row}" slot="yd">
        <el-button type="primary" size="mini" @click="showDetail(row)">填写应答</el-button>
      </template>
    </avue-crud>

    <el-dialog title="应答表" :visible.sync="diaShow" append-to-body>
      <avue-form :option="diaOption" v-model="diaForm" @submit="handleSubmit"></avue-form>
    </el-dialog>
     <!-- <div class="box">
      <div class="dn">下载</div>
      <div class="sx">刷新</div>
      <div class="cx">查询</div>
    </div> -->
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/quality/mykhxq";
  import {addYd} from "@/api/quality/mykhyd";
  import option from "@/const/quality/mykhxq";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        diaShow: false,
        diaOption: {
          labelWidth: 120,
          column: [
            {
              label: "需求id",
              prop: "xqid",
              type: "input",
              disabled: true
            },
            {
              label: "参加考核确认",
              prop: "cjkhqr",
              type: "select",
              dicData: [
                {
                  label: '是',
                  value: '是'
                },
                {
                  label: '否',
                  value: '否'
                }
              ]
            },
            {
              label: "单个样品重量",
              prop: "dgypzl",
              type: "input",
            },
            {
              label: "检测项目检出限",
              prop: "jcxmjcx",
              type: "input",
            },
            {
              label: "考核样品数量",
              prop: "khypsl",
              type: "input",
            },
            {
              label: "是否需要邮寄",
              prop: "sfxyyj",
              type: "select",
              dicData: [
                {
                  label: '是',
                  value: '是'
                },
                {
                  label: '否',
                  value: '否'
                }
              ]
            },
            {
              label: "收件人地址",
              prop: "sjrdz",
              type: "input",
            },
            {
              label: "收件人电话",
              prop: "sjrdh",
              type: "input",
            },
            {
              label: "收件人姓名",
              prop: "sjrxm",
              type: "input",
            },
          ]
        },
        diaForm: {}
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        // export excel, avue reconfig
        option.excelBtn = this.vaildData(this.permission.mykhxq_export, false);

        return {
          addBtn: this.vaildData(this.permission.mykhxq_add, false),
          viewBtn: this.vaildData(this.permission.mykhxq_view, false),
          delBtn: this.vaildData(this.permission.mykhxq_delete, false),
          editBtn: this.vaildData(this.permission.mykhxq_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      handleSubmit(){
        addYd(this.diaForm).then(() => {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.diaShow = false
        }, error => {
          window.console.log(error);
        });
      },
      showDetail(e) {
        this.diaShow = true
        this.diaForm.xqid = e.id
      },
      rowSave(row, done, loading) {
        let params = {
          ...row,
          kjsids: row.kjsids.join(',')
        }
        add(params).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
