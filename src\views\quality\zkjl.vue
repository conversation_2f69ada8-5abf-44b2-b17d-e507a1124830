<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot-scope="{row}" slot="ysjl">
        <el-upload
          v-if="!row.ysjl"
          class="upload-demo"
          action="/api/manage/put-object"
          :on-success="uploadSuccess"
          >
          <el-button size="small" type="primary" @click="changeFile(row)">点击上传</el-button>
        </el-upload>
        <span v-else>
          <a class="down_html" v-for="(item,index) in row.ysjl.split(',')" :href="item" :key="index">下载文件{{index+1}}</a>
        </span>
      </template>
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.zkjl_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
      <!-- <div class="box">
      <div class="dn">下载</div>
      <div class="sx">刷新</div>
      <div class="cx">查询</div>
    </div> -->
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/quality/zkjl";
  import option from "@/const/quality/zkjl";
  import {mapGetters} from "vuex";
  import {getToken} from "@/util/auth";

  export default {
    data() {
      return {
        form: {
          wczt:''
        },
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        currentId: null,
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        // export excel, avue reconfig
        option.excelBtn = this.vaildData(this.permission.zkjl_export, false);

        return {
          addBtn: this.vaildData(this.permission.zkjl_add, false),
          viewBtn: this.vaildData(this.permission.zkjl_view, false),
          delBtn: this.vaildData(this.permission.zkjl_delete, false),
          editBtn: this.vaildData(this.permission.zkjl_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    mounted() {

      window.__zkjl_component__ = this;
    },
    beforeDestroy() {
      delete window.__zkjl_component__;
    },
    methods: {
      setColumnValue(val,prop) {
        this.form[prop] = val;
        // // 如果需要强制更新视图
        this.$set(this.form, prop, val);
      },
      changeFile(e){
        this.currentId = e.id
      },
      uploadSuccess(res, file, fileList){
        const row = {
          id: this.currentId,
          ysjl: res.data
        }
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          console.log(error);
        });
      },
      rowSave(row, done, loading) {

        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {

        update(row).then(() => {
          debugger
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
  .down_html{
    display: block;
  }
  .down_html:hover{
    color: #04A7B3;
  }
</style>
