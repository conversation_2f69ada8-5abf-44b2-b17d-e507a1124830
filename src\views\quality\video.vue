<template>
  <basic-container>
    <el-row style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入视频名称"
          clearable
          @keyup.enter.native="handleSearch"
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="handleSearch"
          ></el-button>
        </el-input>
      </el-col>
    </el-row>
    <el-row :gutter="20" style="width: 100%;" v-loading="loading">
      <el-col :span="8" style="height: 240px" v-for="(item,index) in videoList" :key="index">
        <div style="position:relative;">
          <img class="video_img" :src="`//images.weserv.nl/?url=http://27.223.62.98:9180/${item.videoid}.jpg`" onerror="" alt="">
          <div style="text-align: center">{{item.videoname}}</div>
          <div class="play_box" @click="clickVideo(item)" >
            <i class="el-icon-caret-right"></i>
          </div>
        </div>

      </el-col>
    </el-row >
    <div style="text-align: center;margin: 20px auto">
      <el-pagination background :total="page.total" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[15, 50, 100, 200]"
                     :page-size="page.pageSize"
                     @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page.currentPage"></el-pagination>
    </div>
    <div>
      <el-dialog :title="videoname" :visible.sync="videoShow" v-if="videoShow" append-to-body>
        <div style="width: 100%">
          <VideoBox :id="'video'+ new Date().getDay()" style="height: 500px" :srcUrl="videourl" ></VideoBox>
        </div>
      </el-dialog>
    </div>
  </basic-container>
</template>

<script>
  import VideoBox from "../../components/VideoBox";
  import { getList } from "@/api/quality/video";
  export default {
    name: '',
    components: {
      VideoBox
    },
    data() {
      return {
        loading: false,
        videoShow: false,
        videoname: '视频名称',
        videourl: '',
        videoList:[],
        searchKeyword: '', // 新增搜索关键词
        page: {
          pageSize: 15,
          currentPage: 1,
          total: 0
        },
      }
    },
    mounted() {
      this.getVideoList()
    },
    watch: {
      searchKeyword(newVal) {
        if(newVal === '') {
          this.handleSearch()
        }
      }
    },

    methods: {
      handleSizeChange(val) {
        this.page.pageSize = val
        this.getVideoList()
      },
      handleCurrentChange(val) {
        this.page.currentPage = val
        this.getVideoList()
      },
      clickVideo(e){
        this.videoname = e.videoname
        this.videourl = e.videourl
        setTimeout(()=>{
          this.videoShow = true
        },100)
      },
      getVideoList() {
        this.loading = true
        getList(this.page.currentPage, this.page.pageSize, {
          videoname: this.searchKeyword // 添加搜索参数
        }).then(res => {
          this.loading = false
          this.videoList = res.data.data.records
          this.page.total = res.data.data.total
        })
      },
      // 新增搜索方法
      handleSearch() {
        this.page.currentPage = 1 // 重置到第一页
        this.getVideoList()
      },
    },
  }
</script>

<style scoped lang="less">
  .video_img{
    width: 100%;
    height: 200px;
    border: 1px solid #eee;
  }
  .play_box{
    width: 40px;
    height: 40px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -20px;
    margin-left: -20px;
    background: #fff;
    border-radius: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer !important;
    i{
      font-size: 32px;
    }
  }
  .people_item{
    width: 100%;
    line-height: 35px;
    display: flex;
    justify-content: space-between;
  }
  .people_title{
    width: 200px;
    line-height: 40px;
    border: 1px solid #ccc;
    text-align: center;
    margin: 0 auto;
  }
  // 可添加以下样式优化搜索框位置
  .el-col-6 {
    padding-left: 0 !important;
  }
</style>
