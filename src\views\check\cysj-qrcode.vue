<template>
  <div style="height: 100%">
    <div class="home_index">
      <div class="item_title">华测快检采样信息</div>
      <div class="card_item">
        <div class="check_item">
          <div>采样编号: {{ cybh }}</div>
          <div>所属任务: {{ssrwmc}}</div>
          <div>样品名称: {{ypmc}}</div>
          <div>采样人员: {{ cyrymc}}</div>
          <div>采样时间: {{ cysj }}</div>
          <div>样品信息: {{ ypmc}}</div>
          <div>大类: {{ ypxl}}</div>
          <div>受检信息: {{ shidmc}}</div>
          <div>单位: {{ sjdwmc}}</div>

        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {getDetail} from '@/api/desk/jyh'
import {getDeptTree, getOpenTree, getTree} from '@/api/system/dept' // 注意实际路径可能不同
export default {
  name: "qrcode",
  data(){
    return {
      cybh: '',
      ssrwmc: '',
      cyrymc:''
      ,cysj:''
      ,ypmc:''
      ,ypxl:''
      ,shidmc:''
      ,sjdwmc:''
    }
  },
  mounted() {
    this.cybh = this.$route.query.cybh
    this.ssrwmc = this.$route.query.ssrwmc
    this.cyrymc = this.$route.query.cyrymc
    this.cysj = this.$route.query.cysj
    this.ypmc = this.$route.query.ypmc
    this.ypxl = this.$route.query.ypxl
    this.shidmc = this.$route.query.shidmc
    this.sjdwmc = this.$route.query.sjdwmc

  },

  methods:{


  }
}
</script>

<style scoped lang="less">
.card_item div{
  margin: 10px 0;
}
.card_item{
  width: 100%;
  border: 1px solid #eee;
  border-radius: 15px;
  padding: 10px;
}
.item_title {
  width: 100px;
  height: 40px;
  border-radius: 30px;
  background: #00a680;
  color: #fff;
  text-align: center;
  line-height: 40px;
  margin: 20px auto;
}

.title_box {
  width: 100%;
  text-align: center;
  font-size: 24px;
}
.top_logo{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.home_index {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
}
.search-box {
  .el-form-item {
    width: 100%;
    margin-bottom: 15px;

    &__content {
      width: 100%;
    }

    .el-date-editor,
    .el-input {
      width: 100% !important;
    }
  }

  .el-button {
    margin-top: 10px;
  }
}

.el-select-dropdown__item {
  &[level="1"] { padding-left: 30px; }
  &[level="2"] { padding-left: 40px; }
  &[level="3"] { padding-left: 50px; }
  // 可根据需要继续添加更多层级样式
}

</style>
