<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <div style="display: flex">
          <el-button type="text"
                     size="small"
                     icon="el-icon-download"
                     plain
                     @click="downExcel">
            下载模板
          </el-button>
          <el-upload :auto-upload="false" :show-file-list="false" action="action" :on-change="handleChange">
            <el-button type="text"
                       size="small"
                       icon="el-icon-upload "
                       plain
                       style="margin-left: 20px">
              导入
            </el-button>
          </el-upload>
          <el-button type="primary"
                     size="small"
                     style="margin-left: 20px"
                     @click="madeTag"
                     plain>
            生成制样标签
          </el-button>
        </div>
      </template>
    </avue-crud>


    <el-dialog title="制样标签" :visible.sync="tagShow" append-to-body>
      <div>

        <el-row>
<!--          盲样制作标签，修改下面span的值（一行总数为24，若一行4个即为6，一行3个即为8）-->
          <el-col :span="6" v-for="(item,index) in data" :key="index">
            <div class="tag_main">
              <div class="tag_title">盲样制作标签</div>
              <el-descriptions class="margin-top" :contentStyle="contentClass" title="" :column="1" size="small" border>
                <el-descriptions-item>
                  <template slot="label">
                    样品编号
                  </template>
                  {{item[['样品编号']]}}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">
                    样品名称
                  </template>
                  {{item[['样品名称']]}}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">
                    待测项目
                  </template>
                  {{item[['待测项目']]}}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">
                    复溶加水量
                  </template>
                  {{item[['复溶加水量']]}}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">
                    备注
                  </template>
                  {{item[['备注']]}}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
        </el-row>
      </div>
      <div style="text-align: center">
        <el-button type="primary"
                   size="small"
                   style="margin-left: 20px"
                   @click="madeTag"
                   plain>
          打印
        </el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {downLoadModel, getDetail, add, update, remove} from "@/api/quality/myzz";
  import option from "@/const/quality/myzz";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        tagShow: false,
        contentClass:{
          'text-align': 'center',  //文本居中
          'min-width': '150px',   //最小宽度
          'word-break': 'break-all'  //过长时自动换行
        }
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.mykhxq_add, false),
          viewBtn: this.vaildData(this.permission.mykhxq_view, false),
          delBtn: this.vaildData(this.permission.mykhxq_delete, false),
          editBtn: this.vaildData(this.permission.mykhxq_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      madeTag(){
        this.tagShow = true
      },
      handleChange(file, fileLis){
        this.$Export.xlsx(file.raw)
        .then(data => {
          this.data=data.results;
        })
      },
      downExcel(){
        downLoadModel().then(res=>{
          this.$exportCsv(res.data,'盲样模板')
        })
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = false;
      }
    }
  };
</script>

<style lang="less" scoped>
  .tag_title{
    width: 100%;
    text-align: center;
    line-height: 30px;
  }
</style>
