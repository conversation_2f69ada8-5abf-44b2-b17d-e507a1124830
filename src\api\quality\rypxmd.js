import request from '@/router/axios';

export const getPeoList = (current, size, params) => {
  return request({
    url: '/api/zlgl/rypxmd/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/zlgl/rypxmd/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/zlgl/rypxmd/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/zlgl/rypxmd/submit',
    method: 'post',
    data: row
  })
}

export const upPeoDate = (row) => {
  return request({
    url: '/api/zlgl/rypxmd/submit',
    method: 'post',
    data: row
  })
}

