import {getToken} from '@/util/auth';

export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchShow: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  columnBtn: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "申请人",
      prop: "sqr",
      type: "input",
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "省",
      prop: "xs",
      type: "input",
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "市",
      prop: "sq",
      type: "input",
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "区",
      prop: "xzq",
      type: "input",
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "任务名称",
      prop: "rwmc",
      type: "input",
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "收费/免费",
      prop: "smf",
      type: "select",
      dicData: [
        {
          label: '收费',
          value: '收费',
        },
        {
          label: '免费',
          value: '免费',
        },
      ],
      rules: [{
        required: true,
        message: "请选择",
        trigger: "change"
      }],
    },
    {
      label: "活动主题",
      prop: "hdtm",
      type: "input",
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "活动地点",
      prop: "hddd",
      type: "input",
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "活动状态",
      prop: "hdzt",
      type: "select",
      dicData: [
        {
          label: '未开始',
          value: '未开始',
        },
        {
          label: '已完成',
          value: '已完成',
        },
      ],
      rules: [{
        required: true,
        message: "请选择",
        trigger: "change"
      }],
    },
    {
      label: "活动时间",
      prop: "hdsj",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      rules: [{
        required: true,
        message: "请选择",
        trigger: "change"
      }],
    },
    {
      label: "参与人数",
      prop: "cyrs",
      type: "input",
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "活动形式",
      prop: "hdxs",
      type: "input",
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "活动记录",
      prop: "hdjl",
      type: 'upload',
      listType: 'picture-card',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: false,
      hide: true,
      multiple: true,
      tip: '可上传照片、视频等',
      rules: [{
        required: true,
        message: "请上传",
        trigger: "change"
      }],
    },
    {
      label: "活动记录",
      prop: "hdjlwj",
      type: 'upload',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: false,
      hide: true,
      multiple: true,
      tip: '可上传文件',
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

  ]
}
