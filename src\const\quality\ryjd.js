export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "样品名称",
      prop: "ypid",
      type: "tree",
      filterable: true,
      dicUrl: "/api/ypgl/ypxx/list?current=1&size=9999",
      props: {
        label: "ypmc",
        value: "id",
        res: 'data.records'
      },
      search: true,

      nodeClick: (data, node, nodeComp) => {
        if (window.__ryjd_component__) {
          window.__ryjd_component__.handleNodeClick2(data, 'ypmc','ypmc');
        }
      },
      addDisplay: true,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "样品名称",
      prop: "ypmc",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "样品编号",
      prop: "ypbh",
      width: 150,
      type: "input",
    },
    {
      label: "样品大类",
      prop: "ypdlmc",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: false,
      row:false
    },
    {
      label: "样品大类",
      prop: "ypdl",
      type: "tree",
      dicUrl: "/api/blade-system/dict/dictionary?code=sample_big_type",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      cascader: ['ypxl'],
      filterable: true,
      search: true,
      nodeClick: (data, node, nodeComp) => {
        if (window.__ryjd_component__) {
          window.__ryjd_component__.handleNodeClick2(data, 'dictValue','ypdlmc');
        }
      },
      row: false,
      addDisplay: true,
      editDisplay: true,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "样品细类",
      prop: "ypxl",
      type: "select",
      dicUrl: "/api/ypgl/ypxx/getYpxl?ypdl={{ypdl}}",
      props: {
        label: "label",
        value: "value"
      },
      filterable: true,
      cascaderIndex: 0,
      search: true,
    },
    {
      label: "任务名称",
      prop: "rwid",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      parent:false,
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      search: true,
      width: 100,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label:'检测项目',
      prop:'jcxm',
      type: "select",
      filterable: true,
      dicUrl: "/api/ypgl/jcxm/page?code=check_method?current=1&size=9999",
      props: {
        label: "xmmc",
        value: "id",
        res: 'data.records'
      },
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "被监督对象",
      prop: "bjddx",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "监督类型",
      prop: "jdlx",
      type: "select",
      dicData: [
        {
          label: '人员监督记录',
          value: '人员监督记录',
        },
        {
          label: '微生物实验室实操考核',
          value: '微生物实验室实操考核',
        },
        {
          label: '实验室实操考核',
          value: '实验室实操考核'
        },
        {
          label: '采样考核',
          value: '采样考核'
        },
      ],
      searchSpan:4,
      search: true,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "实施时间",
      prop: "sssj",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "考核人",
      prop: "khr",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "组织者",
      prop: "zzz",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "输出材料",
      prop: "sccl",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "评价标准",
      prop: "pjbz",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },


    {
      label: "上传时间",
      prop: "scsj",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "检测方法",
      prop: "jcff",
      type: "select",
      dicUrl: "/api/blade-system/dict/dictionary?code=check_method",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      rules: [{
        required: true,
        message: "请选择",
        trigger: "blur"
      }],
    },
    {
      label: "完成状态",
      prop: "wczt",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "监督结果",
      prop: "jg",
      type: "select",
      dicData: [
        {
          label: '优秀',
          value: '优秀',
        },
        {
          label: '良好',
          value: '良好',
        },
        {
          label: '合格',
          value: '合格',
        },
        {
          label: '不合格',
          value: '不合格',
        },
      ],
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "监督详情",
      prop: "jdxq",
      type: 'upload',
      listType: 'picture-card',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: true,
      hide: true,
      multiple: true,
      tip: '可上传照片、视频',
      rules: [
        {
          required: true,
          message: "请上传",
          trigger: "blur"
        }
      ],
      change(file, fileList) {
        if (file.value !==""){
          if (window.__ryjd_component__) {
            window.__ryjd_component__.setColumnValue('完成','wczt');

          }
        }else {
          if (window.__ryjd_component__) {
            window.__ryjd_component__.setColumnValue('','wczt');

          }
        }

      },

    },
    {
      label: "详情文件",
      prop: "jdxqwj",
      type: 'upload',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: true,
      hide: true,
      multiple: true,
      tip: '可上传文件',
      change(file, fileList) {
        if (file.value !==""){
          if (window.__ryjd_component__) {
            window.__ryjd_component__.setColumnValue('完成','wczt');

          }
        }else {
          if (window.__ryjd_component__) {
            window.__ryjd_component__.setColumnValue('','wczt');

          }
        }

      },

    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
