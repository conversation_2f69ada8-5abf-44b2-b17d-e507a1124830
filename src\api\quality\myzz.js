import request from '@/router/axios';

export const downLoadModel = () => {
  return request({
    url: '/api/zlgl/myzydy/downLoadModel',
    method: 'get',
    responseType: 'blob',
    params: {
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/zlgl_/mykhxq/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/zlgl_/mykhxq/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/zlgl_/mykhxq/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/zlgl_/mykhxq/submit',
    method: 'post',
    data: row
  })
}

