<template>
  <div>
    <div @click="print" class="print">打印</div>
    <div id="print">
      <h3 class="title">移液器期间核查原始记录表</h3>
      <table>
        <tr>
          <td class="w10">仪器编号</td>
          <td class="w5">{{ yyqhc.sbbh }}</td>
          <td class="w5">规格型号</td>
          <td class="w10 fl">
            □(20-200)uL<br />
            □(100-1000)uL<br />
            □(1000-5000)uL
          </td>
          <td class="w10">期间核查依据</td>
          <td class="w10" colspan="2">{{ yyqhc.zcfg }}</td>
          <td class="w10" colspan="2">期间核查日期</td>
          <td class="w10">{{ yyqhc.qjhcrq }}</td>
        </tr>

        <tr>
          <td class="w10">期间核查所用砝码设备编号</td>
          <td class="w5"></td>
          <td class="w5">电子天平检定日期</td>
          <td class="w10">{{ yyqhc.dztpxzsj }}</td>
          <td class="w10">天平精密度</td>
          <td class="w10">{{ yyqhc.dztpjd }}</td>
          <td class="w10">环境湿度</td>
          <td class="w10">{{ yyqhc.hjwd }}</td>
          <td class="w10">环境温度</td>
          <td class="w10">{{ yyqhc.hjsd }}</td>
        </tr>
      </table>

      <table>
        <tr>
          <td class="tf w10" colspan="12">移液器期间核查记录</td>
        </tr>
        <tr>
          <td class="tl w10">一、外观检查记录</td>
          <td colspan="12" class="tl">
            经目测和触摸观察，表面平整、无废边裂纹、活塞上下移动灵活等，设备标识清晰可见，外观检查合格。
          </td>
        </tr>
        <tr>
          <td class="tl w10" colspan="12">二、称量记录</td>
        </tr>
        <tr>
          <td class="w5">序号</td>
          <td class="w5">核查点/uL</td>
          <td class="w5">质量值/mg</td>
          <td class="w5">温度/℃</td>
          <td class="w5">K(t)值/cm3/g</td>
          <td class="w5">V20实际容积值/μL</td>
          <td class="w5">平均值/uL</td>
          <td class="w5">测试相对误差/%/</td>
          <td class="w5">允许误差/%</td>
          <td class="w5">测试重复性/%</td>
          <td class="w5">要求重复性/%</td>
          <td class="w5">单项核查结论</td>
        </tr>

        <tr v-for="(item, index) in infoOne">
          <td class="w5">{{ index + 1 }}</td>
          <td class="w5" rowspan="6" v-if="index == 0">{{ item.hcd }}</td>
          <td class="w5">{{ item.zlz }}</td>
          <td class="w5">{{ item.wd }}</td>
          <td class="w5">{{ item.ktz }}</td>
          <td class="w5">{{ item.vsj }}</td>
          <td class="w5" rowspan="6" v-if="index == 0">{{ item.pjz }}</td>
          <td class="w5" rowspan="6" v-if="index == 0">{{ item.csxdwc }}</td>
          <td class="w5" rowspan="6" v-if="index == 0">{{ item.yxwc }}</td>
          <td class="w5" rowspan="6" v-if="index == 0">{{ item.jsbzpc }}</td>
          <td class="w5" rowspan="6" v-if="index == 0">{{ item.cscfx }}</td>
          <td class="w5" rowspan="6" v-if="index == 0">{{ item.dxhcjl }}</td>
        </tr>
      </table>
      <table>
        <tr>
          <td class="tf w10" colspan="9">核查结论:</td>
        </tr>

        <tr>
          <td class="tl w10" colspan="10">
            {{ yyqhc.hcjl }}
          </td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">
            <div class="flex">
              核查人：
              <img v-if="yyqhc.tjrqm" :src="yyqhc.tjrqm" class="avatar" />
              &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;
              <div class="flex">
                审核人 ：
                <img v-if="yyqhc.tjrqm" :src="yyqhc.tjrqm" class="avatar" />
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td class="tf w10" colspan="10">备注：</td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">
            1、每把移液器内部校准三个点：最小量程、1/2量程、最大量程
          </td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">
            2、允许误差：20uL：±4.0%；100uL：±2.0%；200uL：±1.5%；500uL：±1.0%；1000uL：±1.0%；2500uL：±0.5%；5000uL：±0.6%
          </td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">
            3、重复性要求：20uL：≤2.0%；100uL：≤1.0%；200uL：≤1.0%；500uL：≤0.5%；1000uL：≤0.5%；2500uL：≤0.2%；5000uL：≤0.2%
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import table from "../../views/util/table.vue";
export default {
  components: { table },
  name: "qctif07",
  data() {
    return {
      urlPath: this.getUrlPath(), //iframe src 路径
    };
  },
  props: {
    yyqhc: null,
    infoOne: null,
    infoTwo: null,
    infoThree: null,
  },
  created() {},
  methods: {
    print() {
      const printContents = document.getElementById("print").innerHTML;
      const originalContents = document.body.innerHTML;

      document.body.innerHTML = printContents;
      window.print();

     window.location.reload()
    },
  },
};
</script>

<style  scoped>
.title {
  text-align: center;
  position: relative;
}
table {
  border-collapse: collapse;
  width: 100%;
}
table th,
table td {
  padding: 5px;
  text-align: center;
  border: 1px solid #ddd;
}
table th {
  background-color: #f2f2f2;
  font-weight: bold;
  font-size: 1em;
}
.w15 {
  width: 15%;
}
.tf {
  text-align: left;
  font-weight: bold;
}
.tl {
  text-align: left;
}
.w20 {
  width: 20%;
}
.w10 {
  width: 10%;
}
.w5 {
  width: 5%;
}
.flex{
  display: flex;
}
img {
  width: 60px;
}
.print {
  width: 100px;
  background: #04a7b3;
  color: #fff;
  text-align: center;
  padding: 10px;
}
</style>
