export const option = {
  height: 'auto',
  calcHeight: 30,
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  addBtn: false,
  editBtn: false,
  delBtn: true,
  viewBtn: false,
  dialogClickModal: false,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  labelWidth: 110,
  align: 'center',
  menu: true,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "所属任务",
      prop: "rwid",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      search: true,
      searchSpan: 4,
    },
    // {
    //   label: "街道",
    //   prop: "cybh",
    //   type: "input",
    // },
    {
      label: "单位类型",
      prop: "dwlx",
      type: "select",
      dicUrl: "/api/blade-system/dict/dictionary?code=shop_type",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      search: true,
      searchSpan: 4,
    },
    {
      label: "商铺名",
      prop: "bjdwmc",
      type: "input",
    },
    {
      label: "食安负责人",
      prop: "safzr",
      type: "input",
      width: 100,
    },
    {
      label: "巡查日期",
      prop: "xcrq",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      search: true,
      searchSpan: 4,
    },
    {
      label: "食安联系电话",
      prop: "safzrdh",
      type: "input",
      width: 100,
    },
    {
      label: "不合格项",
      prop: "bhgx",
      type: "input",
      search: true,
      searchSpan: 4,
    },
    {
      label: "评分",
      prop: "pj",
      type: "input",
    },

    {
      label: "巡查结果",
      prop: "xcjg",
      type: "input",
      search: true,
      searchSpan: 4,
    },
    {
      label: "综合评分",
      prop: "zhpf",
      type: "input",
    },
    {
      label: "整改结果",
      prop: "zgjg",
      type: "input",
      search: true,
      searchSpan: 4,
    },
    // {
    //   label: "回执单",
    //   prop: "hzd",
    //   type: "input",
    // },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}

export const closeOption = {
  height: 'auto',
  calcHeight: 30,
  tip: false,
  searchShow: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  viewBtn: false,
  selection: true,
  dialogClickModal: false,
  labelWidth: 110,
  align: 'center',
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "行政区",
      prop: "cybh",
      type: "input",
    },
    {
      label: "街道",
      prop: "cybh",
      type: "input",
    },
    {
      label: "单位类型",
      prop: "cybh",
      type: "input",
    },
    {
      label: "商铺号",
      prop: "cybh",
      type: "input",
    },
    {
      label: "食安弗负责人",
      prop: "cybh",
      type: "input",
    },
    {
      label: "巡查日期",
      prop: "cysj",
      type: "date",
      valueFormat: "yyyy-MM-dd"
    },
    {
      label: "联系电话",
      prop: "cybh",
      type: "input",
    },
    {
      label: "不合格项",
      prop: "cybh",
      type: "input",
    },
    {
      label: "评分",
      prop: "cybh",
      type: "input",
    },

    {
      label: "巡查结果",
      prop: "sscs",
      type: "input",
    },
    {
      label: "整改结果",
      prop: "ypmc",
      type: "input",
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}

