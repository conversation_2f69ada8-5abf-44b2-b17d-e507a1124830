import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/jc/yxzs/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getRetraceList = (current, size, params) => {
  return request({
    url: '/api/jc/yxzsjl/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getRetraceDetail = (id) => {
  return request({
    url: '/api/jc/yxzsjl/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const removeRetrace = (ids) => {
  return request({
    url: '/api/jc/yxzsjl/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const addRetrace = (row) => {
  return request({
    url: '/api/jc/yxzsjl/submit',
    method: 'post',
    data: row
  })
}

export const updateRetrace = (row) => {
  return request({
    url: '/api/jc/yxzsjl/submit',
    method: 'post',
    data: row
  })
}

