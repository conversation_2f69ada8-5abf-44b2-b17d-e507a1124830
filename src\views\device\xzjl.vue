<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
      </template>
      <template slot-scope="{row}" slot="xcxzsj">
        <el-date-picker
          @change="changeRq($event,row)"
          v-model="row.xcxzsj"
          type="date"
          value-format="yyyy-MM-dd"
          size="mini"
          placeholder="选择日期">
        </el-date-picker>
      </template>
      <template slot-scope="{row}" slot="xzjl">
        <el-button type="primary" size="mini" @click="showDetail(row)">查看</el-button>
      </template>
    </avue-crud>

    <el-dialog :visible.sync="detailShow" append-to-body custom-class="dialog_box">
      <template slot="title">
        <div class="detail_title">设备校准记录</div>
      </template>
      <div id="detail_main">
        <div class="main_info">
          设备名称：{{detail.sbmc}}
          <span style="margin-left: 15px">设备编号：{{detail.sbbh}}</span>
        </div>
        <avue-crud :option="addOption"
                   :data="jlList"
                   v-model="addForm"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowJlDel">
        </avue-crud>
      </div>
      <template slot="footer">
        <div class="detail_foot">
          <div>
<!--            <el-button size="small" icon="el-icon-search">导出</el-button>-->
<!--            <el-button size="small" icon="el-icon-search">打印</el-button>-->
          </div>
          <el-button size="small" type="primary" @click="detailShow=false">确认</el-button>
        </div>
      </template>
    </el-dialog>
 <!-- <div class="box">
      <div class="dn">下载</div>
      <div class="sx">刷新</div>
      <div class="cx">查询</div>
    </div> -->
  </basic-container>
</template>

<script>
  import {getList, getRecordList, add, update, remove, updateSb} from "@/api/device/xzjl";
  import option from "@/const/device/xzjl";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        detailShow: false,
        detail: {},

        jlList: [],
        addShow: false,
        addOption: {
          align: 'center',
          excelBtn: true,
          refreshBtn: false,
          columnBtn: false,
          column: [
            {
              label: '设备管理员',
              prop: 'sbgly',
              type: 'input'
            },
            {
              label: '校准时间',
              prop: 'xzsj',
              type: "date",
              valueFormat: "yyyy-MM-dd",
              rules:[
                {required: true, message: '请选择校准时间', trigger: 'blur'},
              ]
            },
            {
              label: '校准结果',
              prop: 'xzjg',
              type: "select",
              dicData: [
                {
                  label: '合格',
                  value: '合格'
                }, {
                  label: '不合格',
                  value: '不合格'
                }
              ],
              rules:[
                {required: true, message: '请选择校准结果', trigger: 'blur'},
              ]
            },
            {
              label: '校准证书',
              prop: 'xzzs',
              type: "upload",
              propsHttp: {
                url: 'data',
                name: 'data'
              },
              action: '/api/manage/put-object'
            }
          ]
        },
        addForm:{},
        jlPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        // export excel, avue reconfig
        option.excelBtn = this.vaildData(this.permission.xzjl_export, false);

        return {
          addBtn: this.vaildData(this.permission.xzjl_add, false),
          viewBtn: this.vaildData(this.permission.xzjl_view, false),
          delBtn: this.vaildData(this.permission.xzjl_delete, false),
          editBtn: this.vaildData(this.permission.xzjl_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      changeRq(e, row) {
        updateSb({
          id: row.id,
          xcxzsj: e
        }).then(res => {
          this.$message({
            type: "success",
            message: res.data.msg
          });
          this.onLoad(this.page);
        })
      },
      getXzDetail() {
        getRecordList(this.detail.id).then(res => {
          this.jlList = res.data.data.records
        })
      },
      showDetail(e) {
        this.detail = e
        this.detailShow = true
        this.getXzDetail()
      },
      rowSave(row, done, loading) {
        const par = {
          ...row,
          sbid: this.detail.id
        }
        add(par).then(() => {
          this.getXzDetail()
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.getXzDetail()
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowJlDel(row){
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          return remove(row.id);
        })
        .then((e) => {
          this.getXzDetail()
          this.$message({
            type: "success",
            message: e.data.msg
          });
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style lang="less">
  .dialog_box {
    .el-dialog__body {
      padding: 10px;
    }
  }
</style>

<style scoped lang="less">

  .pdf_img {
    width: 30px;
    height: auto;
    cursor: pointer;
  }

  #detail_main {
    width: 100%;
    height: 500px;
    overflow: auto;

    .main_title {
      width: 100%;
      text-align: center;
      font-size: 16px;
    }

    .main_info {
      width: 100%;
      text-align: center;
      margin: 15px 0;
    }
  }

  .detail_foot {
    width: 100%;
    display: flex;
    justify-content: space-between;
    border-top: 1px solid #ccc;
    padding-top: 20px;
  }

  .detail_title {
    width: 100%;
    font-size: 14px;
    text-align: center;
    color: #04A7B3;
    padding-bottom: 20px;
    border-bottom: 1px solid #ccc;
  }
</style>
