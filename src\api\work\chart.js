import request from '@/router/axios';

//设备-各快检室设备类型
export const sblxtj = () => {
  return request({
    url: '/api/tb/sblxtj',
    method: 'post'
  })
}

//设备-校准设备类型统计
export const xzsblx = (type) => {
  return request({
    url: '/api/tb/xzsblx',
    method: 'post',
    params:{
      type
    }
  })
}

//设备-核查统计
export const hctj = () => {
  return request({
    url: '/api/tb/hctj',
    method: 'get',
  })
}
