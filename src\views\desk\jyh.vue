<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.jyh_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template slot-scope="{row}" slot="cxgl">
        <avue-rate  :value="row.cxgl" disabled></avue-rate>
      </template>
      <template slot-scope="{ row }" slot="qrcode">
        <el-button type="text" size="small" @click="showQr(row)"> 查看 </el-button>
      </template>
      <template slot-scope="{row}" slot="menu">
        <el-button v-if="row.hmd=='否'" type="text" size="mini" @click="inHmd(row,true)">列入黑名单</el-button>
        <el-button v-if="row.hmd=='是'" type="text" size="mini" @click="inHmd(row,false)">移出黑名单</el-button>
        <el-button type="text" size="mini" @click="goRouter(1,row)">进货信息</el-button>
        <el-button type="text" size="mini" @click="goRouter(2,row)">售出信息</el-button>
      </template>
    </avue-crud>

    <el-dialog
      append-to-body
      :title="detailInfo.jyhmc"
      :visible.sync="dialogVisible"
      width="30%"
      center="true"
    >
      <div
        style="
          display: flex;
          justify-content: center;
          align-items: center;
          padding-bottom: 20px;
        "
      >
        <Qrcode1
          id="qrcode"
          :url="qrcodeUrl"
          :key="qrcodeUrl"
        ></Qrcode1>

      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/desk/jyh";
  import {getDept} from "@/api/system/dept";
  import option from "@/const/desk/jyh";
  import Qrcode1 from "@/components/Qrcode";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        keyword: null,
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        dialogVisible: false,
        qrcodeUrl: null,
        detailInfo: {}
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        // export excel, avue reconfig
        option.excelBtn = this.vaildData(this.permission.jyh_export, false);

        return {
          addBtn: this.vaildData(this.permission.jyh_add, false),
          viewBtn: this.vaildData(this.permission.jyh_view, false),
          delBtn: this.vaildData(this.permission.jyh_delete, false),
          editBtn: this.vaildData(this.permission.jyh_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    components: {
      Qrcode1,
    },
    watch:{
        'form.rwid': {
          handler(newVal) {
              if(newVal>0)
              {
                  getDept(newVal).then(result=>{
                        this.form.rwmc = result.data.data.deptName;
                  });
              }
          },
          deep: true,
          immediate: true
        }
    },
    methods: {
      goRouter(type,item){
        this.$router.push({
          path: type == 1?'/desk/jhsy':'/desk/scsy',
          query: {
            id: item.id
          }
        })
      },
      showQr(e) {
        // 使用深拷贝创建新对象，确保响应式更新
        this.detailInfo = Object.assign({}, e)

        const href = window.location.href.split('#');
        const url = `${href[0]}#/operator/detail?id=${e.id}&jyhmc=${e.jyhmc}&frmc=${e.frmc}&scmc=${e.scmc}&yyzzt=${e.yyzzt}`

        // 强制二维码组件重新渲染
        this.qrcodeUrl = null
        this.$nextTick(() => {
          this.qrcodeUrl = url
          this.dialogVisible = true;
        })
      },
      inHmd(e,v){
        update({
          id: e.id,
          hmd: v?'是':'否'
        }).then(res=>{
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.onLoad(this.page);
        })
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {

        if(params.clrq){
          this.query = {
            ...params,
            startDate: params.clrq[0],
            endDate: params.clrq[1]
          };
        }else{
          this.query = params
        }
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  .top_box{
    width: 100%;
    height: 80px;
    background: #ccc;
  }
</style>
