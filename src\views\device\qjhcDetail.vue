<template>
  <basic-container back>
    <div id="detail_box">
      <!-- <div v-if="type == 1">
        <div class="title_box">移液器期间核查结果详情</div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="3"
          size="small "
          border
        >
          <el-descriptions-item label="设备名称">
            {{ yyqhc.sbmc }}
          </el-descriptions-item>
          <el-descriptions-item label="设备编号">
            {{ yyqhc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="期间核查日期">
            {{ yyqhc.qjhcrq }}
          </el-descriptions-item>
          <el-descriptions-item label="核查所用电子天平编号">
            {{ yyqhc.dztpbh }}
          </el-descriptions-item>
          <el-descriptions-item label="电子天平校准日期">
            {{ yyqhc.dztpxzsj }}
          </el-descriptions-item>
          <el-descriptions-item label="天平精密度">
            {{ yyqhc.dztpjd }}
          </el-descriptions-item>
          <el-descriptions-item label="环境温度">
            {{ yyqhc.hjwd }}
          </el-descriptions-item>
          <el-descriptions-item label="环境湿度">
            {{ yyqhc.hjsd }}
          </el-descriptions-item>
          <el-descriptions-item label="设备校准日期">
            {{ yyqhc.sbxzsj }}
          </el-descriptions-item>
          <el-descriptions-item label="街道负责人">
            {{ yyqhc.jdfzr }}
          </el-descriptions-item>
          <el-descriptions-item label="设备管理人">
            {{ yyqhc.sbgly }}
          </el-descriptions-item>
          <el-descriptions-item label="期间核查依据">
            {{ yyqhc.zcfg }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="card_title">一、外观检测记录</div>
        <div class="out_box"></div>
        <div class="card_title">二、称量记录（每个核查点记录6次）</div>
        <div class="card_title">称量记录（1）</div>
        <table>
          <thead>
            <tr>
              <th>序列</th>
              <th>核查点/ul</th>
              <th>质量值/mg</th>
              <th>温度/℃</th>
              <th>K(T)值/cm3/g</th>
              <th>V20实际容积值/ul</th>
              <th>平均值/ul</th>
              <th>测试相对误差/%</th>
              <th>允许误差</th>
              <th>标准偏差</th>
              <th>测试重复性/%</th>
              <th>要求重复性/%</th>
              <th>单项核查结论</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in infoOne" :key="index">
              <td>{{ index + 1 }}</td>
              <td rowspan="6" v-if="index == 0">
                {{ item.hcd }}
              </td>
              <td>
                {{ item.zlz }}
              </td>
              <td>
                {{ item.wd }}
              </td>
              <td>
                {{ item.ktz }}
              </td>
              <td>
                {{ item.vsj }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.pjz }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.csxdwc }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.yxwc }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.jsbzpc }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.cscfx }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.yqcfx }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.dxhcjl }}
              </td>
            </tr>
          </tbody>
        </table>
        <div class="card_title">称量记录（2）</div>
        <table>
          <thead>
            <tr>
              <th>序列</th>
              <th>核查点/ul</th>
              <th>质量值/mg</th>
              <th>温度/℃</th>
              <th>K(T)值/cm3/g</th>
              <th>V20实际容积值/ul</th>
              <th>平均值/ul</th>
              <th>测试相对误差/%</th>
              <th>允许误差</th>
              <th>标准偏差</th>
              <th>测试重复性/%</th>
              <th>要求重复性/%</th>
              <th>单项核查结论</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in infoTwo" :key="index">
              <td>{{ index + 1 }}</td>
              <td rowspan="6" v-if="index == 0">
                {{ item.hcd }}
              </td>
              <td>
                {{ item.zlz }}
              </td>
              <td>
                {{ item.wd }}
              </td>
              <td>
                {{ item.ktz }}
              </td>
              <td>
                {{ item.vsj }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.pjz }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.csxdwc }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.yxwc }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.jsbzpc }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.cscfx }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.yqcfx }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.dxhcjl }}
              </td>
            </tr>
          </tbody>
        </table>
        <div class="card_title">称量记录（3）</div>
        <table>
          <thead>
            <tr>
              <th>序列</th>
              <th>核查点/ul</th>
              <th>质量值/mg</th>
              <th>温度/℃</th>
              <th>K(T)值/cm3/g</th>
              <th>V20实际容积值/ul</th>
              <th>平均值/ul</th>
              <th>测试相对误差/%</th>
              <th>允许误差</th>
              <th>计算标准偏差</th>
              <th>测试重复性/%</th>
              <th>要求重复性/%</th>
              <th>单项核查结论</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in infoThree" :key="index">
              <td>{{ index + 1 }}</td>
              <td rowspan="6" v-if="index == 0">
                {{ item.hcd }}
              </td>
              <td>
                {{ item.zlz }}
              </td>
              <td>
                {{ item.wd }}
              </td>
              <td>
                {{ item.ktz }}
              </td>
              <td>
                {{ item.vsj }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.pjz }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.csxdwc }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.yxwc }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.jsbzpc }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.cscfx }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.yqcfx }}
              </td>
              <td rowspan="6" v-if="index == 0">
                {{ item.dxhcjl }}
              </td>
            </tr>
          </tbody>
        </table>
        <div class="card_title">
          核查结论：
          <span style="font-weight: bolder">{{ yyqhc.hcjl }}</span>
          <span style="margin-left: 15px"
            >确认日期：{{ yyqhc.createTime }}</span
          >
        </div>
        <el-row :gutter="20" style="font-size: 14px">
          <el-col :span="6">
            <div style="margin: 10px 0">确认人签名</div>
            <img v-if="yyqhc.tjrqm" :src="yyqhc.tjrqm" class="avatar" />
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">核查人签名</div>
            <img v-if="yyqhc.tjrqm" :src="yyqhc.tjrqm" class="avatar" />
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">审核人签名</div>
            <img v-if="yyqhc.tjrqm" :src="yyqhc.tjrqm" class="avatar" />
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <div class="btn_box">
          <div>
            <el-button size="small">导出</el-button>
            <el-button size="small">打印</el-button>
          </div>
          <el-button size="small" type="primary" @click="goBack"
            >确认</el-button
          >
        </div>
      </div> -->
      <!-- <div v-if="type == 2">
        <div class="title_box">电子天平期间核查结果详情</div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="3"
          size="small "
          border
        >
          <el-descriptions-item label="设备名称">
            {{ dztphc.sbmc }}
          </el-descriptions-item>
          <el-descriptions-item label="设备编号">
            {{ dztphc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="期间核查日期">
            {{ dztphc.qjhcrq }}
          </el-descriptions-item>
          <el-descriptions-item label="核查所用砝码编号">
            {{ dztphc.fmbh }}
          </el-descriptions-item>
          <el-descriptions-item label="砝码检定证书编号">
            {{ dztphc.fmjdzsbh }}
          </el-descriptions-item>
          <el-descriptions-item label="砝码检定日期">
            {{ dztphc.fmjdrq }}
          </el-descriptions-item>
          <el-descriptions-item label="环境温度">
            {{ dztphc.hjwd }}
          </el-descriptions-item>
          <el-descriptions-item label="环境湿度">
            {{ dztphc.hjsd }}
          </el-descriptions-item>
          <el-descriptions-item label="设备管理员">
            {{ dztphc.sbgly }}
          </el-descriptions-item>
          <el-descriptions-item label="期间核查依据">
            {{ dztphc.zcfg }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="card_title">一、外观检测记录</div>
        <div class="out_box">{{ dztphc.wgjcjl }}</div>
        <div class="card_title">二、称量记录</div>
        <div class="card_title">称量核查</div>
        <table>
          <thead>
            <tr>
              <th>序号</th>
              <th>核查点/g</th>
              <th>天平读数（第1次）/g</th>
              <th>天平读数（第2次）/g</th>
              <th>天平读数（第3次）/g</th>
              <th>天平读数（第4次）/g</th>
              <th>平均值/g</th>
              <th>误差/g</th>
              <th>允许误差/g</th>
              <th>内部校准结果</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in clhc" :key="index">
              <td>{{ index + 1 }}</td>
              <td>
                {{ item.hcd }}
              </td>
              <td>
                {{ item.tpds1 }}
              </td>
              <td>
                {{ item.tpds2 }}
              </td>
              <td>
                {{ item.tpds3 }}
              </td>
              <td>
                {{ item.tpds4 }}
              </td>
              <td>
                {{ item.pjz }}
              </td>
              <td>
                {{ item.wc }}
              </td>
              <td>
                {{ item.yxwc }}
              </td>
              <td>
                {{ item.nbxzjg }}
              </td>
            </tr>
          </tbody>
        </table>
        <div class="card_title">偏载核验</div>
        <table>
          <thead>
            <tr>
              <th>序号</th>
              <th>偏载校准点/g</th>
              <th>天平读数（第1次）/g</th>
              <th>天平读数（第2次）/g</th>
              <th>天平读数（第3次）/g</th>
              <th>天平读数（第4次）/g</th>
              <th>平均值/g</th>
              <th>相对误差/g</th>
              <th>允差/g</th>
              <th>内部校准结果</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in pzhy" :key="index">
              <td>{{ index + 1 }}</td>
              <td>
                {{ item.hcd }}
              </td>
              <td>
                {{ item.tpds1 }}
              </td>
              <td>
                {{ item.tpds2 }}
              </td>
              <td>
                {{ item.tpds3 }}
              </td>
              <td>
                {{ item.tpds4 }}
              </td>
              <td>
                {{ item.pjz }}
              </td>
              <td>
                {{ item.wc }}
              </td>
              <td>
                {{ item.yxwc }}
              </td>
              <td>
                {{ item.nbxzjg }}
              </td>
            </tr>
          </tbody>
        </table>
        <div class="card_title">重复性核查</div>
        <table>
          <thead>
            <tr>
              <th>序列</th>
              <th>重复性校准点/g</th>
              <th>天平读数（第1次）/g</th>
              <th>天平读数（第2次）/g</th>
              <th>天平读数（第3次）/g</th>
              <th>天平读数（第4次）/g</th>
              <th>平均值/g</th>
              <th>相对误差/g</th>
              <th>允差/g</th>
              <th>内部校准结果</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in cfxhc" :key="index">
              <td>{{ index + 1 }}</td>
              <td>
                {{ item.hcd }}
              </td>
              <td>
                {{ item.tpds1 }}
              </td>
              <td>
                {{ item.tpds2 }}
              </td>
              <td>
                {{ item.tpds3 }}
              </td>
              <td>
                {{ item.tpds4 }}
              </td>
              <td>
                {{ item.pjz }}
              </td>
              <td>
                {{ item.wc }}
              </td>
              <td>
                {{ item.yxwc }}
              </td>
              <td>
                {{ item.nbxzjg }}
              </td>
            </tr>
          </tbody>
        </table>
        <div class="card_title">
          核查结论：{{ dztphc.hcjl }}
          <span style="margin-left: 15px"
            >确认日期：{{ dztphc.createTime }}</span
          >
        </div>
        <el-row :gutter="20" style="font-size: 14px">
          <el-col :span="6">
            <div style="margin: 10px 0">确认人签名</div>
            <img v-if="dztphc.tjrqm" :src="dztphc.tjrqm" class="avatar" />
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">核查人签名</div>
            <img v-if="dztphc.hcrqm" :src="dztphc.hcrqm" class="avatar" />
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">审核人签名</div>
            <img v-if="dztphc.shrqm" :src="dztphc.shrqm" class="avatar" />
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <div class="btn_box">
          <div>
            <el-button size="small">导出</el-button>
          </div>
          <el-button size="small" type="primary" @click="goBack"
            >确认</el-button
          >
        </div>
      </div> -->
      <!-- <div v-if="type == 3">
        <div class="title_box">水浴锅期间核查结果详情</div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="3"
          size="small "
          border
        >
          <el-descriptions-item label="设备名称">
            {{ syghc.sbmc }}
          </el-descriptions-item>
          <el-descriptions-item label="设备编号">
            {{ syghc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="设备管理员">
            {{ syghc.sbgly }}
          </el-descriptions-item>
          <el-descriptions-item label="规格型号">
            {{ syghc.ggxh }}
          </el-descriptions-item>
          <el-descriptions-item label="期间核查依据">
            {{ syghc.zcfg }}
          </el-descriptions-item>
          <el-descriptions-item label="期间核查日期">
            {{ syghc.qjhcrq }}
          </el-descriptions-item>
          <el-descriptions-item label="环境温度">
            {{ syghc.hjwd }}
          </el-descriptions-item>
          <el-descriptions-item label="环境湿度">
            {{ syghc.hjsd }}
          </el-descriptions-item>
          <el-descriptions-item label="核查用温度计编号">
            {{ syghc.wdjbh }}
          </el-descriptions-item>
          <el-descriptions-item label="校准机构">
            {{ syghc.xzjg }}
          </el-descriptions-item>
          <el-descriptions-item label="校准证书编号">
            {{ syghc.xzzsbh }}
          </el-descriptions-item>
          <el-descriptions-item label="校准日期">
            {{ syghc.wdjxzrq }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="card_title">一、外观检测记录</div>
        <div class="out_box">{{ syghc.wgjcjl }}</div>
        <div class="card_title">二、上层期间核查记录</div>
        <table>
          <thead>
            <tr>
              <th>序列</th>
              <th>第1次实测温度值℃</th>
              <th>第2次实测温度值℃</th>
              <th>第3次实测温度值℃</th>
              <th>温度波动度℃</th>
              <th>温度均匀度℃</th>
              <th>温度波动度允差℃</th>
              <th>温度均匀度允差℃</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in schc" :key="index">
              <td>{{ index + 1 }}</td>
              <td>
                {{ item.wd1 }}
              </td>
              <td>
                {{ item.wd2 }}
              </td>
              <td>
                {{ item.wd3 }}
              </td>
              <td>
                {{ Number(item.wdbdd).toFixed(2) }}
              </td>
              <td>
                {{ Number(item.wdjyd).toFixed(2) }}
              </td>
              <td>±2℃</td>
              <td>±2℃</td>
            </tr>
          </tbody>
        </table>
        <div class="card_title">三、下层期间核查记录</div>
        <table>
          <thead>
            <tr>
              <th>序列</th>
              <th>第1次实测温度值℃</th>
              <th>第2次实测温度值℃</th>
              <th>第3次实测温度值℃</th>
              <th>温度波动度℃</th>
              <th>温度均匀度℃</th>
              <th>温度波动度允差℃</th>
              <th>温度均匀度允差℃</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in xchc" :key="index">
              <td>{{ index + 1 }}</td>
              <td>
                {{ item.wd1 }}
              </td>
              <td>
                {{ item.wd2 }}
              </td>
              <td>
                {{ item.wd3 }}
              </td>
              <td>
                {{ Number(item.wdbdd).toFixed(2) }}
              </td>
              <td>
                {{ Number(item.wdjyd).toFixed(2) }}
              </td>
              <td>±2℃</td>
              <td>±2℃</td>
            </tr>
          </tbody>
        </table>
        <div class="card_title">
          核查结论：{{ syghc.hcjl }}
          <span style="margin-left: 15px"
            >确认日期：{{ syghc.createTime }}</span
          >
        </div>
        <el-row :gutter="20" style="font-size: 14px">
          <el-col :span="6">
            <div style="margin: 10px 0">确认人签名</div>
            <img v-if="syghc.tjrqm" :src="syghc.tjrqm" class="avatar" />
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">核查人签名</div>
            <img v-if="syghc.tjrqm" :src="syghc.tjrqm" class="avatar" />
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">审核人签名</div>
            <img v-if="syghc.tjrqm" :src="syghc.tjrqm" class="avatar" />
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <div class="btn_box">
          <div>
            <el-button size="small">导出</el-button>
          </div>
          <el-button size="small" type="primary" @click="goBack"
            >确认</el-button
          >
        </div>
      </div> -->
      <!-- <div v-if="type == 4">
        <div class="title_box">分光光度计期间核查结果详情</div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="3"
          size="small "
          border
        >
          <el-descriptions-item label="设备名称">
            {{ fggdjhc.sbmc }}
          </el-descriptions-item>
          <el-descriptions-item label="设备编号">
            {{ fggdjhc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="期间核查日期">
            {{ fggdjhc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="工作液申请日期">
            {{ fggdjhc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="检测项目">
            {{ fggdjhc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="标准储备液浓度（mg/L）">
            {{ fggdjhc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="标准储备液编号">
            {{ fggdjhc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="标准物质CAS号">
            {{ fggdjhc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="工作液浓度（mg/L）">
            {{ fggdjhc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="环境温度">
            {{ fggdjhc.sbbh }}
          </el-descriptions-item>
          <el-descriptions-item label="环境湿度">
            kooriookami
          </el-descriptions-item>
          <el-descriptions-item label="设备校准日期">
            kooriookami
          </el-descriptions-item>
          <el-descriptions-item label="街道负责人">
            kooriookami
          </el-descriptions-item>
          <el-descriptions-item label="设备管理人">
            kooriookami
          </el-descriptions-item>
          <el-descriptions-item label="期间核查依据">
            kooriookami
          </el-descriptions-item>
        </el-descriptions>
        <div class="card_title">一、外观检测记录</div>
        <div class="out_box">可以正常使用，外观符合要求。</div>
        <div class="card_title">二、分光光度计核查记录</div>
        <table>
          <thead>
            <tr>
              <th>序列</th>
              <th>加标倍数</th>
              <th>加标项目</th>
              <th>0.5倍加标量</th>
              <th>1倍加标量</th>
              <th>加标量/ul</th>
              <th>检测结果（抑制率）</th>
              <th>允差范围</th>
              <th>允差校验/C</th>
              <th>核查结果判定</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in hcjl" :key="index">
              <td>{{ index + 1 }}</td>
              <td>
                {{ item.jbbs }}
              </td>
              <td>
                {{ item.jbxm }}
              </td>
              <td>
                {{ item.hjbl }}
              </td>
              <td>
                {{ item.djbl }}
              </td>
              <td>
                {{ item.jbl }}
              </td>
              <td>
                {{ item.jcjg }}
              </td>
              <td>
                {{ item.ycfw }}
              </td>
              <td>
                {{ item.ycxy }}
              </td>
              <td>
                {{ item.hcjgpd }}
              </td>
            </tr>
          </tbody>
        </table>
        <div class="card_title">
          核查结论：{{ yyqhc.hcjl }}
          <span style="margin-left: 15px"
            >确认日期：{{ yyqhc.createTime }}</span
          >
        </div>
        <el-row :gutter="20" style="font-size: 14px">
          <el-col :span="6">
            <div style="margin: 10px 0">确认人签名</div>
            <img v-if="yyqhc.tjrqm" :src="yyqhc.tjrqm" class="avatar" />
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">核查人签名</div>
            <img v-if="yyqhc.tjrqm" :src="yyqhc.tjrqm" class="avatar" />
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">审核人签名</div>
            <img v-if="yyqhc.tjrqm" :src="yyqhc.tjrqm" class="avatar" />
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <div class="btn_box">
          <div>
            <el-button size="small">导出</el-button>
            <el-button size="small">打印</el-button>
          </div>
          <el-button size="small" type="primary" @click="goBack"
            >确认</el-button
          >
        </div>
      </div> -->
    </div>
    <Qcti7
      v-if="type == 2"
      :dztphc="dztphc"
      :cfxhc="cfxhc"
      :clhc="clhc"
      :pzhy="pzhy"
    ></Qcti7>
    <Qcti8
     v-if="type == 4"
     :hcjl="hcjl"
     :fggdjhc="fggdjhc">
     </Qcti8>
    <Qcti12
      v-if="type == 1"
      :yyqhc="yyqhc"
      :infoOne="infoOne"
      :infoTwo="infoTwo"
      :infoThree="infoThree"
    ></Qcti12>
    <Qcti13 v-if="type == 3"
     :syghc="syghc"
      :allList="allList">
    </Qcti13>
  </basic-container>
</template>

<script>
import {
  getYyqDetail,
  getDztpDetail,
  getSygDetail,
  getGdjDetail,
  update,
  remove,
} from "@/api/device/newQjhc";
import Qcti7 from "@/components/print-template/qcti7";
import Qcti8 from "@/components/print-template/qcti8";
import Qcti12 from "@/components/print-template/qcti12";
import Qcti13 from "@/components/print-template/qcti13";

export default {
  name: "newQjhc",
  data() {
    return {
      allList: [],
      type: null,
      yyqhc: {},
      infoOne: [{}, {}, {}, {}, {}, {}],
      infoTwo: [{}, {}, {}, {}, {}, {}],
      infoThree: [{}, {}, {}, {}, {}, {}],

      dztphc: {},
      clhc: [{}, {}, {}, {}, {}, {}, {}],
      pzhy: [{}, {}, {}, {}, {}],
      cfxhc: [{}],
      scqjhcjl: [{}, {}, {}, {}, {}, {}],
      xcqjhcjl: [{}, {}, {}, {}, {}, {}],

      syghc: {},
      schc: [{}, {}, {}, {}, {}, {}],
      xchc: [{}, {}, {}, {}, {}, {}],

      fggdjhc: {},
      hcjl: [{}, {}, {}, {}, {}, {}],
    };
  },
  components: {
    Qcti7,
    Qcti8,
    Qcti12,
    Qcti13,
  },
  mounted() {
    this.type = this.$route.query.type;
    if (this.type == 1) {
      this.getDetail1();
    }
    if (this.type == 2) {
      this.getDetail2();
    }
    if (this.type == 3) {
      this.getDetail3();
    }
    if (this.type == 4) {
      this.getDetail4();
    }
  },
  methods: {
    getDetail1() {
      getYyqDetail(this.$route.query.hcid).then((res) => {
        this.yyqhc = res.data.data.yyqhc;
        this.infoOne = res.data.data.infoOne;
        this.infoTwo = res.data.data.infoTwo;
        this.infoThree = res.data.data.infoThree;
      });
    },
    getDetail2() {
      getDztpDetail(this.$route.query.hcid).then((res) => {
        this.dztphc = res.data.data.dztphc;
        this.cfxhc = res.data.data.cfxhc;
        this.clhc = res.data.data.clhc;
        this.pzhy = res.data.data.pzhy;
      });
    },
    getDetail3() {
      getSygDetail(this.$route.query.hcid).then((res) => {
        this.syghc = res.data.data.syghc;
        this.schc = res.data.data.schc;
        this.xchc = res.data.data.xchc;
        this.allList = this.schc.concat(this.xchc);
      });
    },
    getDetail4() {
      getGdjDetail(this.$route.query.hcid).then((res) => {
        console.log(1212,res)
        this.fggdjhc = res.data.data.fggdjhc
        this.hcjl = res.data.data.hcjl
      });
    },
    goBack() {
      this.$router.back();
    },
  },
};
</script>

<style >
.avatar-uploader .el-upload {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 24px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  line-height: 128px;
  text-align: center;
}

.avatar {
  width: 128px;
  height: 128px;
  display: block;
}
</style>

<style scoped lang="less">
.title_box {
  width: 100%;
  padding-bottom: 15px;
  text-align: center;
}

.btn_box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 50px;
}

.info_title {
  padding: 15px 0;
}

.card_title {
  margin-top: 15px;
  padding-bottom: 15px;
  font-size: 14px;
}
table {
  width: 100%;
  table-layout: fixed;
  font-size: 12px !important;
}

table,
td,
th {
  text-align: center;
  border: 1px solid #ccc;
  border-collapse: collapse;
  font-weight: normal;
  font-size: 12px !important;
}

table tr {
  height: 30px;
  padding: 0 5px !important;
  font-size: 12px !important;
  word-wrap: break-word !important;
}

table td {
  padding: 5px;
  font-size: 12px !important;
  word-wrap: break-word !important;
}
</style>
