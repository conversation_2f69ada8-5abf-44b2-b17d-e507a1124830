export default {
  height: 'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchShow: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  labelWidth: 120,
  excelBtn: true,
  printBtn: false,
  align: 'center',
  menu: true,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "培训课程",
      prop: "pxkc",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
      type: "input",
    },
    {
      label: "培训学时",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
      prop: "pxxs",
      type: "input",
    },
    {
      label: "培训师",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
      prop: "pxs",
      type: "input",
    },
    {
      label: "培训地点",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
      prop: "pxdd",
      type: "input",
    },
    {
      label: "计划培训日期",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
      prop: "jhpxrq",
      type: "date",
      valueFormat: "yyyy-MM-dd"
    },
    {
      label: "培训单位",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
      prop: "pxdw",
      type: "input",
    },
    {
      label: "归属任务",
      prop: "rwid",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      parent:false,
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      search: true,
      width: 100,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },

    {
      label: "参培人员",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
      prop: "cxry",
      type: "input",
    },
    {
      label: "完成状态",
      prop: "wczt",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    // {
    //   label: "参培人员",
    //   prop: "ryid",
    //   type: "select",
    //   hide: true,
    //   multiple:true,
    //   dicUrl: "/api/blade-user/page?current=1&size=100000&deptId={{rwid}}",
    //   props: {
    //     label: "name",
    //     value: "id",
    //     res: 'data.records'
    //   },
    // },
    {
      label: "考核方式",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
      prop: "khfs",
      type: "input",
    },
    {
      label: "备注",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
      prop: "bz",
      type: "input",
    },
    {
      label: "培训记录",
      prop: "pxzp",
      listType: 'picture-card',
      type: 'upload',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: true,
      hide: true,
      multiple: true,
      tip: '可上传照片、视频等',

      change(file, fileList) {
        if (file.value !== "") {
          if (window.__rypx_component__) {
            window.__rypx_component__.setColumnValue('完成', 'wczt');

          }
        } else {
          if (window.__rypx_component__) {
            window.__rypx_component__.setColumnValue('', 'wczt');

          }
        }

      },
    },
    {
      label: "记录文件",
      prop: "pxzpwj",
      type: 'upload',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: true,
      hide: true,
      multiple: true,
      tip: '可上传文件',

      change(file, fileList) {
        if (file.value !== "") {
          if (window.__rypx_component__) {
            window.__rypx_component__.setColumnValue('完成', 'wczt');

          }
        } else {
          if (window.__rypx_component__) {
            window.__rypx_component__.setColumnValue('', 'wczt');

          }
        }

      },
    },
    {
      label: "参培名单",
      prop: "cpmd",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
