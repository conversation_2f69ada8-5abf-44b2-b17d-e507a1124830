<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <div style="display: flex">
          <el-button v-if="permission.myzyjg_import" type="primary"
                     size="small"
                     icon="el-icon-download"
                     plain
                     @click="downExcel">
            下载模板
          </el-button>
          <el-upload v-if="permission.myzyjg_import" :headers="uploadHeaders" :on-success="handleAvatarSuccess" :show-file-list="false" action="/api/zlgl/myzyjg/addExcel">
            <el-button type="primary"
                       size="small"
                       icon="el-icon-upload "
                       plain
                       style="margin-left: 10px">
              导入
            </el-button>
          </el-upload>
          <el-button type="primary"
                     size="small"
                     style="margin-left: 10px"
                     @click="madeTag"
                     plain>
            生成制样标签
          </el-button>
          <el-button type="danger"
                     size="small"
                     style="margin-left: 10px"
                     icon="el-icon-delete"
                     plain
                     v-if="permission.myzyjg_delete"
                     @click="handleDelete">删 除
          </el-button>
        </div>
      </template>
    </avue-crud>
    <el-dialog title="制样标签" width="700px" :visible.sync="tagShow" append-to-body>
      <div id="print">
        <div class="print">
          <table v-for="(item, index) in selectionList" :key="index">
            <tr><td colspan="2" >盲样制样标签</td></tr>
            <tr>
              <td style="width: 50%">样品编号</td><td style="width: 50%">{{item.ypbh}}</td>
            </tr>
            <tr>
              <td class="w10">样品名称</td><td class="w15">{{item.ypmc}}</td>
            </tr>
            <tr>
              <td class="w10">检测项目</td><td class="w15">{{item.jcxm}}</td>
            </tr>
            <tr>
              <td class="w10">复溶加水量</td><td class="w15">{{item.frjsl}}</td>
            </tr>
            <tr><td colspan="2" style="text-align: left">备注：复溶后，需按说明书要求进行称样后实验</td></tr>
          </table>
        </div>
      </div>
      <div style="text-align: center">
        <el-button type="primary"
                   size="small"
                   style="margin-left: 20px"
                   @click="print"
                   plain>
          打印
        </el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, downLoadModel} from "@/api/quality/myzyjg";
  import option from "@/const/quality/myzyjg";
  import {mapGetters} from "vuex";
  import {getToken} from '@/util/auth';

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        test: [{},{},{},{},{},{},{}],

        tagShow: false,
        contentClass:{
          'text-align': 'center',  //文本居中
          'min-width': '150px',   //最小宽度
          'word-break': 'break-all'  //过长时自动换行
        },
        uploadHeaders:{}
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        // export excel, avue reconfig
        option.excelBtn = this.vaildData(this.permission.myzyjg_export, false);

        return {
          addBtn: this.vaildData(this.permission.myzyjg_add, false),
          viewBtn: this.vaildData(this.permission.myzyjg_view, false),
          delBtn: this.vaildData(this.permission.myzyjg_delete, false),
          editBtn: this.vaildData(this.permission.myzyjg_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    mounted() {
      this.uploadHeaders = {
        'Blade-Auth': 'bearer ' + getToken() ,
        'Authorization': 'Basic c2FiZXI6c2FiZXJfc2VjcmV0',
        'Tenant-Id': '000000'
      }
    },
    methods: {
      handleAvatarSuccess(res, file) {
        if(res.code != 200){
          this.$message.error('导入信息不正确')
        }else{
          this.$message.success(res.msg)
          this.onLoad(this.page);
        }
      },
      print() {
        const printContents = document.getElementById("print").innerHTML;
        const originalContents = document.body.innerHTML;
        // const doc = new jsPDF();

        document.body.innerHTML = printContents;
        window.print();
        window.location.reload();
      },

      madeTag(){
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.tagShow = true
      },
      handleChange(file, fileLis){
        this.$Export.xlsx(file.raw)
          .then(data => {
            this.data=data.results;
          })
      },
      downExcel(){
        downLoadModel().then(res=>{
          this.$exportCsv(res.data,'盲样模板')
        })
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style scoped>
  .print{
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
  }
  table {
    border-collapse: collapse;
    width: 210px;
    margin: 2px;
  }
  table th,
  table td {
    padding: 5px;
    text-align: center;
    border: 1px solid #ddd;
  }
  .flex {
    display: flex;
  }
  table th {
    background-color: #f2f2f2;
    font-weight: bold;
    font-size: 1em;
  }
</style>
