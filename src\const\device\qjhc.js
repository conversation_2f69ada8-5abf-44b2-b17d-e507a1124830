export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  addBtn: false,
  editBtn: false,
  viewBtn: false,
  selection: true,
  dialogClickModal: false,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  searchLabelWidth:100,
  menu: false,
  align: 'center',
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "标准仪器名称",
      prop: "bzyqmc",
      type: "input",
      dicUrl: "/api/blade-system/dict/dictionary?code=standard_device",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      width: 100,
    },
    {
      label: "设备名称",
      prop: "sbmc",
      type: "input",
      searchSpan:4,
      search: true,
    },
    {
      label: "型号规格",
      prop: "sbbh",
      type: "input",
    },
    {
      label: "所属任务",
      prop: "rwmc",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      searchSpan:4,
      search: true,
      width: 100,
    },
    // {
    //   label: "设备校准日期",
    //   prop: "sbxzsj",
    //   type: "date",
    //   valueFormat: "yyyy-MM-dd",
    //   searchSpan:4,
    //   search: true,
    //   width: 100,
    // },
    {
      label: "期间核查日期",
      prop: "qjhcrq",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      width: 100,
    },
    {
      label: "期间核查依据",
      prop: "zcfg",
      type: "input",
      width: 100,
    },
    {
      label: "核查结果",
      prop: "hcjl",
      type: "input",
    },
    {
      label: "核查详情",
      prop: "hcxq",
      type: "input",
    },

    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
