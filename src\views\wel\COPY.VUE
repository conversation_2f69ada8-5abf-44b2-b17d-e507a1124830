<template>
    <div class="main" :class='[light == "moon" ? "moon" : "light"]' ref='myImg'>
      <div>
        <div class="time">{{ date }}</div>
        <div class="title2">北京市市场管理局视频快检追朔数据大屏</div>
        <div class="action" :class='[light == "moon" ? "moon" : "light"]'>
          <el-form :inline="true">
            <el-form-item label="统计周期：">
              <el-select v-model="type" placeholder="统计周期：" size="mini" style="width:100px;margin-top:20px">
                <el-option label="年" value="year"></el-option>
                <el-option label="月" value="month"></el-option>
                <el-option label="日" value="day"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <img src="../../assets/img/sun.png" alt="" class="img1" v-if="light == 'moon'" @click="setLigt('sun')">
          <img src="../../assets/img/sun1.png" alt="" class="img1" v-if="light == 'sun'" @click="setLigt('moon')">
          <img src="../../assets/img/moon.png" alt="" class="img2" v-if="light == 'sun'" @click="setLigt('moon')">
          <img src="../../assets/img/moon1.png" alt="" class="img2" v-if="light == 'moon'" @click="setLigt('sun')">
          <img src="../../assets/img/quanping.png" alt="" class="img3" @click="full">
        </div>
      </div>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="grid-content">
            <div>
              <el-row :gutter="10">
                <el-col :span="12">
                  <div class="total_item">
                    <img src="../../assets/img/home/<USER>" alt="">
                    <span>设备总数</span>
                    <span>{{ deviceTotal.sbzs }}</span>
                  </div>
  
  
                </el-col>
                <el-col :span="12">
                  <div class="total_item">
                    <img src="../../assets/img/home/<USER>" alt="">
                    <span>检测数量</span>
                    <span>{{ deviceTotal.jcsl }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="12">
                  <div class="total_item">
                    <img src="../../assets/img/home/<USER>" alt="">
                    <span>阳性数量</span>
                    <span style="color:#F38E28"> {{ deviceTotal.yxsl }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="total_item">
                    <img src="../../assets/img/home/<USER>" alt="">
                    <span>已处置量</span>
                    <span>{{ deviceTotal.yczs }}</span>
                  </div>
                </el-col>
              </el-row>
  
              <div class="left2">
                <autoScroll :list="list" :speed="0.9" :waitTime="0" :singleHeight="100"
                  style="height:100px;margin-top:10px;margin-bottom:10px">
                  <div v-for="(item, index) in list" :key="index">
                    <div class="leftTitle">
                      <span>{{ item.name }}</span>
                      <span style="margin-left:10px">{{ item.title }}</span>
                    </div>
                  </div>
                </autoScroll>
              </div>
  
              <div>
                <div class="left3">
                  <div ref="chart1" style="width:100%;height:240px"></div>
                  <div class="left3T">目标检测完成情况统计</div>
                </div>
              </div>
  
  
              <div>
                <div class="left4">
                  <div class="left4T">各检测时任务排名</div>
                  <div ref="chart2" style="width:100%;height:400px"></div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div>
            <div class="ditu">
              <div ref="chart7" style="width: 100%; height:400px;"></div>
            </div>
          </div>
  
          <div style="margin-top:5px">
            <el-row :gutter="20">
              <el-col :span="8">
                <video width="100%" height="190" controls>
                  <source src="./video.mp4" type="video/mp4">
                  <source src="movie.ogg" type="video/ogg">
                  您的浏览器不支持Video标签。
                </video>
              </el-col>
              <el-col :span="8">
                <video width="100%" height="190" controls>
                  <source src="./video.mp4" type="video/mp4">
                  <source src="movie.ogg" type="video/ogg">
                  您的浏览器不支持Video标签。
                </video>
              </el-col>
              <el-col :span="8">
                <video width="100%" height="190" controls>
                  <source src="./video.mp4" type="video/mp4">
                  <source src="movie.ogg" type="video/ogg">
                  您的浏览器不支持Video标签。
                </video>
              </el-col>
            </el-row>
          </div>
  
          <div class="center">
            <div class="left4T">食品安全指数分析</div>
            <div ref="chart8" style="width:100%;height:300px"></div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="right1">
                  <div class="left4T">抽检不合格/总量对比</div>
                  <div ref="chart3" style="width:100%;height:160px;margin-top:10px"></div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="right2">
                  <div class="left4T">不同类型市场占比</div>
                  <div ref="chart4" style="width:100%;height:180px"></div>
                </div>
              </el-col>
  
            </el-row>
          </div>
  
  
          <div>
  
            <div class="right2">
              <div class="rightT2" :class='[light == "moon" ? "rightT21" : "rightT22"]'>阳性预警</div>
              <!-- <autoScroll :list="list2" :speed="0.9" :waitTime="0" :singleHeight="100"
                style="height:150px;margin-top:10px">
                <div class="t-item" v-for="(item, index) in list2" :key="index">
                  <div>
                    <div class="text1">{{ item.title }}</div>
                    <div class="text2">{{ item.name }}</div>
  
                  </div>
                </div>
              </autoScroll> -->
  
              <autoScroll :list="list" :speed="0.9" :waitTime="0" :singleHeight="100"
                style="height:160px;margin-top:10px">
                <div v-for="(item, index) in list2" :key="index">
                  <span class="text1">{{ item.title }}</span>
                  <span class="text2">{{ item.name }}</span>
                </div>
              </autoScroll>
  
  
            </div>
          </div>
  
  
          <div class="grid-content">
  
            <div class="right4">
              <div class="left4T">不同类型任务占比</div>
              <div ref="chart5" style="width:100%;height:220px;margin-top:10px"></div>
  
              <div class="left4T">到岗率统计</div>
              <div ref="chart6" style="width:100%;height:220px"></div>
              <div class="tips">
                <div>到岗：567人</div>
                <div style="margin-top:10px">请假：15人</div>
                <div style="margin-top:10px">迟到：32人</div>
              </div>
            </div>
  
          </div>
  
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <third-register></third-register>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :span="24">
          <basic-container :top="false">
            <div style="font-size: 14px;">
              <span>{{ realName }}</span>
              <span style="margin-left: 10px;color:#999">{{ today }}</span>
            </div>
            <el-row :gutter="15" style="margin-top: 10px;font-size: 14px;">
              <el-col :span="6">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <div class="total_item">
                      <img src="../../assets/img/home/<USER>" alt="">
                      <span>设备总数</span>
                      <span>{{ deviceTotal.sbzs }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="total_item">
                      <img src="../../assets/img/home/<USER>" alt="">
                      <span>检测数量</span>
                      <span>{{ deviceTotal.jcsl }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="12">
                    <div class="total_item">
                      <img src="../../assets/img/home/<USER>" alt="">
                      <span>阳性数量</span>
                      <span>{{ deviceTotal.yxsl }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="total_item">
                      <img src="../../assets/img/home/<USER>" alt="">
                      <span>已处置量</span>
                      <span>{{ deviceTotal.yczs }}</span>
                    </div>
                  </el-col>
                </el-row>
                <div class="check_box">
                  <div class="check_title">本月不同类别检测量和阳性率</div>
                  <el-empty v-if="chartList.length == 0" description="暂无数据"></el-empty>
                  <div class="pie_box" ref="pp_box_pie" >
                    <div class="chart_box" id="chart1" v-for="item in chartList"></div>
                    <div class="chart_box" id="chart2"></div>
                    <div class="chart_box" id="chart3"></div>
                    <div class="chart_box" id="chart4"></div>
                    <div class="chart_box" id="chart5"></div>
                    <div class="chart_box" id="chart6"></div>
                    <div class="chart_box" id="chart7"></div>
                    <div class="chart_box" id="chart8"></div>
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="check_title" style="margin-bottom: 10px">类别检测量</div>
  
                <div class="center_center" id="check_chart"></div>
                <div class="center_bottom">
                  <div class="check_title" style="margin-bottom: 10px">本月本市检测执行排行榜</div>
                  <el-table size="mini" :data="checkRank" border style="width: 100%;height: 460px">
                    <el-table-column align="center" label="排名">
                      <template slot-scope="scope">
                        <img class="rate_img" v-if="scope.$index== 0" src="../../assets/img/home/<USER>" alt="">
                        <img class="rate_img" v-if="scope.$index== 1" src="../../assets/img/home/<USER>" alt="">
                        <img class="rate_img" v-if="scope.$index== 2" src="../../assets/img/home/<USER>" alt="">
                        <span v-if="scope.$index> 2">{{ scope.$index}}</span>
                      </template>
  </el-table-column>
  <el-table-column prop="deptName" align="center" label="名称">
  </el-table-column>
  <el-table-column prop="jcNum" label="检测数量（次）" align="center">
  </el-table-column>
  <el-table-column prop="scNum" align="center" label="市场数量（个）">
  </el-table-column>
  <el-table-column prop="avgNum" align="center" label="平均检测量（次/个）">
  </el-table-column>
  </el-table>
  </div>
  </el-col>
  <el-col :span="6">
    <div class="right_top">
      <div class="right_pie" id="pie_box1"></div>
      <div class="right_pie" id="pie_box2"></div>
    </div>
    <div class="right_line" id="line_box"></div>
    <div class="right_center">
      <div class="title_div">阳性预警</div>
      <div class="item_div" v-for="(item, index) in warnList" :key="index">
        <div class="item_left">{{ item.deptName }}</div>
        <div class="item_right">{{ item.rq }}</div>
      </div>
    </div>
    <div class="right_center">
      <div class="title_div title_div2">设备校准到期提醒</div>
      <div class="item_div item_div2" v-for="(item, index) in remindList" :key="index">
        <div class="item_left">{{ item.deptName }}</div>
        <div class="item_right">{{ item.rq }}</div>
      </div>
    </div>
  </el-col>
  </el-row>
  </basic-container>
  </el-col>
  </el-row> -->
    </div>
  </template>
  
  <script>
  import { mapGetters } from "vuex";
  import autoScroll from "./scroll.vue";
  import { getUserInfo, updateInfo, updatePassword } from "@/api/system/user";
  import { getSYgl, getSYlbYxl, getSYlbJcl, getSySph, getSYyxYj, getSYsbXz, getSYjcSjb, getSYjcJlb } from "@/api/wei";
  import * as echarts from 'echarts';
  import screenfull from "screenfull";
  import qingdaoMap from "./map.json";
  
  export default {
    components: { autoScroll },
    name: "wel",
    data() {
      return {
        date: '',
        isFullscreen: false,// 默认不全屏
        light: 'moon',
        type: 'year',
        chartList: [],
        list: [
          { name: '会议提醒 9/03', title: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
          { name: '9/04', title: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
          { name: '9/05', title: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
          { name: '9/06', title: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
          { name: '9/07', title: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
          { name: '9/08', title: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
          { name: '9/09', title: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
          { name: '9/10', title: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
          { name: '9/11', title: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
          { name: '9/12', title: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
          { name: '9/13', title: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
        ],
        list2: [
          { name: '09:31 5月15日', title: '1北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性' },
          { name: '09:31 5月15日', title: '2北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性' },
          { name: '09:31 5月15日', title: '3北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性' },
          { name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性' },
          { name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性' },
          { name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性' },
          { name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性' },
          { name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性' },
          { name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性' },
          { name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性' },
          { name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性' },
        ],
        today: '',
        currentDay: '',
        deviceTotal: {},
        checkTotal: 254,
        yxTotal: 2,
        finishTotal: 251,
        noticeList: [
          {
            title: '会议提醒',
            desc: '09/03 10:00',
          },
          {
            title: '会议提醒',
            desc: '09/03 10:00',
          },
          {
            title: '会议提醒',
            desc: '09/03 10:00',
          },
        ],
        checkRank: [
  
        ],
        warnList: [
  
        ],
        remindList: [
  
        ],
        realName: ''
      };
    },
    computed: {
      ...mapGetters(["userInfo"]),
    },
    mounted() {
      this.getDate()
      this.init1()
      this.init2()
      this.init3()
      this.init4()
      this.init5()
      this.init6()
      this.init7()
      this.init8()
      this.resize()
      this.fetchSYjcJlb();
      this.fetchSYjcSjb();
      this.fetchSYsbXz();
      this.fetchSYyxYj();
      this.fetchSySph();
      this.fetchSYlbJcl();
      // --------------------------------
      this.fetchSYlbYxl();
      this.getUser()
      this.getTime()
      this.fetchSYgl();
      this.initLine()
      this.fetchSYgl()
      // this.initNumChart2()
      // this.initNumChart3()
      // this.initNumChart4()
    },
    methods: {
      resize(){
        const myChart1 = echarts.init(this.$refs.chart1);
        const myChart2 = echarts.init(this.$refs.chart2);
        const myChart3 = echarts.init(this.$refs.chart3);
        const myChart4 = echarts.init(this.$refs.chart4);
        const myChart5 = echarts.init(this.$refs.chart5);
        const myChart6 = echarts.init(this.$refs.chart6);
        const myChart7 = echarts.init(this.$refs.chart7);
        const myChart8 = echarts.init(this.$refs.chart8);
  
        window.addEventListener("resize", () => {
          myChart1.resize();
        myChart2.resize();
        myChart3.resize();
        myChart4.resize();
        myChart5.resize();
        myChart6.resize();
        myChart7.resize();
        myChart8.resize();
    });
      },
      setLigt(val) {
        this.light = val
        console.log(this.light)
      },
      full() {
        if (screenfull.isEnabled) {
          screenfull.toggle(this.$refs.myImg);
          if (screenfull.isFullscreen) {
            document.documentElement.style.overflow = 'scroll';
          }
        }
      },
      getDate() {
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth() + 1; // 月份从0开始，需要加1
        const day = now.getDate();
        this.date = year + '-' + month + '-' + day
        console.log(year, month, day, "aaaaa");
      },
      init1() {
        const myChart = echarts.init(this.$refs.chart1);
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            }
          },
          grid: {
            top: '40',
            left: '40',
            bottom: '20',
            right: '20'
          },
          legend: {
            data: ['项目一', '项目二'],
            textStyle: {
              color: '#ccc' // 这里设置图例文字的颜色为红色
            }
          },
          xAxis: [
            {
              type: 'category',
              data: ['检测室1', '检测室2', '检测室3', '检测室4', '检测室5', '检测室6'],
              axisPointer: {
                type: 'shadow'
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              splitLine: {
                lineStyle: {
                  // 使用深浅的间隔色
                  color: ['#085875']
                }
              }
            },
          ],
          series: [
            {
              name: '项目一',
              type: 'bar',
              itemStyle: {
                // 设置柱状图颜色
                color: '#04A7B3'
              },
              data: [12, 56, 25, 41, 89, 151]
            },
            {
              name: '项目二',
              type: 'bar',
              itemStyle: {
                // 设置柱状图颜色
                color: 'rgb(233,137,12)'
              },
              data: [6, 85, 14, 25, 36, 14, 88]
            },
  
          ]
        };
        myChart.setOption(option);
      },
      init2() {
        const myChart2 = echarts.init(this.$refs.chart2);
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              // Use axis to trigger tooltip
              type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
            }
          },
          grid: {
            top: '60',
            left: '40',
            bottom: '60',
            right: '20'
          },
          legend: {
            textStyle: {
              color: '#ccc' // 这里设置图例文字的颜色为红色
            }
          },
          xAxis: {
            type: 'value'
          },
          yAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Mon']
          },
          series: [
            {
              name: 'Direct',
              type: 'bar',
              stack: 'total',
              label: {
                show: true
              },
              emphasis: {
                focus: 'series'
              },
              data: [20, 31, 21, 13, 43, 20, 31, 21, 13, 43]
            },
            {
              name: 'Mail Ad',
              type: 'bar',
              stack: 'total',
              label: {
                show: true
              },
              emphasis: {
                focus: 'series'
              },
              data: [32, 45.67, 34, 34, 32, 45.67, 34, 34]
            },
            {
              name: 'Affiliate Ad',
              type: 'bar',
              stack: 'total',
              label: {
                show: true
              },
              emphasis: {
                focus: 'series'
              },
              data: [32, 42, 12, 67, 78, 32, 42, 12, 67, 78]
            },
            {
              name: 'Video Ad',
              type: 'bar',
              stack: 'total',
              label: {
                show: true
              },
              emphasis: {
                focus: 'series'
              },
              data: [32, 48, 35, 34, 34, 32, 48, 35, 34, 34]
            },
            {
              name: 'Search Engine',
              type: 'bar',
              stack: 'total',
              label: {
                show: true
              },
              emphasis: {
                focus: 'series'
              },
              data: [32, 65, 45, 34, 85, 32, 65, 45, 34, 85]
            },
  
          ]
        }
        myChart2.setOption(option)
      },
  
      init3() {
        const myChart3 = echarts.init(this.$refs.chart3);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          series: [
            {
              name: '数量',
              type: 'pie',
              data: [
                { value: 1048, name: '抽检不合格' },
                { value: 735, name: '总量对比' },
              ],
            }
          ]
        }
        myChart3.setOption(option)
      },
  
      init4() {
        const myChart4 = echarts.init(this.$refs.chart4);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          series: [
            {
              name: 'Access From',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 40,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 1048, name: 'Search Engine' },
                { value: 735, name: 'Direct' },
                { value: 580, name: 'Email' },
                { value: 484, name: 'Union Ads' },
                { value: 300, name: 'Video Ads' }
              ]
            }
          ]
        }
        myChart4.setOption(option)
      },
  
      init5() {
        const myChart5 = echarts.init(this.$refs.chart5);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            left: 'right',
            orient: "vertical",
            top:'20%',
            textStyle: {
              color: '#ccc' // 这里设置图例文字的颜色为红色
            }
          },
          series: [
            {
              name: '数量',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 40,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 1048, name: '农药残留' },
                { value: 735, name: '违禁药品' },
                { value: 580, name: '重金属' },
                { value: 484, name: '瘦肉精' },
                { value: 300, name: '非法添加剂' }
              ]
            }
          ]
        }
        myChart5.setOption(option)
      },
  
      init6() {
        const myChart6 = echarts.init(this.$refs.chart6);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          series: [
            {
              name: 'Access From',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 40,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 1048, name: 'Search Engine' },
                { value: 735, name: 'Direct' },
              ]
            }
          ]
        }
        myChart6.setOption(option)
      },
  
      init7() {
        echarts.registerMap("qingdaoMap", qingdaoMap);
        const myChart7 = echarts.init(this.$refs.chart7);
  
        var linesData = [
          { coords: [120.395966,36.070892], name: '市南区' },
        ];
        var linesSeries = linesData.map((item, index) => {
          var prevCoord = index > 0 ? linesData[index - 1].coords : item.coords;
           return {
              type: 'lines',
              coords: [prevCoord, item.coords],
              lineStyle: {
              width: 2,
              color: '#ff0000',
              type: 'dashed'
          }
      };
    });
        const option = {  
          geo: {
            map: "qingdaoMap",
            aspectScale: 1,
            silent: false,
            label: {
              show: true,
              emphasis: {
                show: true, // 高亮状态下显示区域名称
                areaColor: "rgba(35, 99, 150,0.1)",
              },
            },
            emphasis: {
                label: {
                  show: true,
                  color: "#00D3FD",
                  fontSize: "12",
                },
                itemStyle: {
                  borderColor: "#0F5A69",
                  borderWidth: 2,
                  areaColor: "#DEF0F3",
                },
              },
            top: "center",
            left: "center",
            roam: true, //禁止拖拽
            selectedMode: "single", //单选
            animationDurationUpdate: 0,
            zoom: 1.4,
            regions: [
              //对不同的区块进行着色
              {
                name: "平度市",
                itemStyle: {
                  normal: {
                    areaColor: "#3F8EC9",
                  },
                  emphasis: {
                    areaColor: "#3F8EC9",
                  },
                },
                lines: [{
                  coords: [
                      [119.959012,36.788828], // 引导线起点经纬度
                      [124.31, 48.91]  // 引导线终点经纬度
                  ],
                  lineStyle: {
                      color: 'red',
                      width: 2
                  }
              }]
              },
              {
                name: "莱西市",
                itemStyle: {
                  normal: {
                    areaColor: "#274889",
                  },
                  emphasis: {
                    areaColor: "#274889",
                  },
                },
              },
              {
                name: "胶州市",
                itemStyle: {
                  normal: {
                    areaColor: "#274889",
                  },
                  emphasis: {
                    areaColor: "#274889",
                  },
                },
              },
              {
                name: "黄岛区",
                itemStyle: {
                  normal: {
                    areaColor: "#274889",
                  },
                  emphasis: {
                    areaColor: "#274889",
                  },
                },
              },
              {
                name: "市北区",
                itemStyle: {
                  normal: {
                    areaColor: "#274889",
                  },
                  emphasis: {
                    areaColor: "#274889",
                  },
                },
              },
              {
                name: "市南区",
                itemStyle: {
                  normal: {
                    areaColor: "#274889",
                  },
                  emphasis: {
                    areaColor: "#274889",
                  },
                },
              },
              {
                name: "李沧区",
                itemStyle: {
                  normal: {
                    areaColor: "#274889",
                  },
                  emphasis: {
                    areaColor: "#274889",
                  },
                },
              },
              {
                name: "城阳区",
                itemStyle: {
                  normal: {
                    areaColor: "#A18D66",
                  },
                  emphasis: {
                    areaColor: "#A18D66",
                  },
                },
              },
              {
                name: "崂山区",
                itemStyle: {
                  normal: {
                    areaColor: "#274889",
                  },
                  emphasis: {
                    areaColor: "#274889",
                  },
                },
              },
              {
                name: "即墨区",
                itemStyle: {
                  normal: {
                    areaColor: "#274889",
                  },
                  emphasis: {
                    areaColor: "#274889",
                  },
                },
              },
            ],
          },
          series: [
          {
              type: 'map',
              map: 'qingdaoMap', // 使用中国地图，你可以根据需要更改为其他地图
              // 其他地图设置...
              lines: [
                  {
                      coords: [
                          [116.46, 39.92], // 起点经纬度
                          [143.47, 21.23]  // 终点经纬度
                      ],
                      lineStyle: {
                          color: 'red',
                          width: 2,
                          type: 'solid'
                      }
                  }
                  // 可以添加更多的引导线...
              ]
          }
      ]
  
  
        }
        myChart7.setOption(option)
      },
  
      init8() {
        const myChart8 = echarts.init(this.$refs.chart8);
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            top: '5%',
            textStyle: {
              color: '#ccc' // 这里设置图例文字的颜色为红色
            }
          },
          grid: {
            left: '20',
            right: '4%',
            bottom: '6%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01]
          },
          yAxis: {
            type: 'category',
            data: ['22日', '21日', '20日']
          },
          series: [
            {
              name: '果蔬',
              type: 'bar',
              data: [132, 132, 32, 132, 124, 214]
            },
            {
              name: '水产',
              type: 'bar',
              data: [32, 41, 321, 123, 231, 68]
            },
            {
              name: '生鲜',
              type: 'bar',
              data: [23, 211, 134, 211, 79, 124]
            },
            {
              name: '禽蛋',
              type: 'bar',
              data: [23, 211, 134, 211, 79, 124]
            },
            {
              name: '加工',
              type: 'bar',
              data: [23, 211, 134, 211, 79, 124]
            }
          ]
        }
        myChart8.setOption(option)
      },
      fetchSYjcJlb() {
        getSYjcJlb().then(res => {
          let data = res.data.data || [];
          this.initPie2(data)
        })
      },
      fetchSYjcSjb() {
        getSYjcSjb().then(res => {
          let data = res.data.data || [];
          this.initPie1(data)
        })
      },
      fetchSYsbXz() {
        getSYsbXz().then(res => {
          let data = res.data.data || [];
          this.remindList = data;
        })
      },
      fetchSYyxYj() {
        getSYyxYj().then(res => {
          let data = res.data.data || [];
          this.warnList = data;
        })
      },
      fetchSySph() {
        getSySph().then(res => {
          let data = res.data.data || [];
          this.checkRank = data;
        })
      },
      fetchSYlbJcl() {
        getSYlbJcl().then(res => {
          let data = res.data.data || [];
          const enumData = [
            {
              key: "name",
              value: '市场'
            },
            {
              key: "sl",
              value: 'sl'
            }];
          let formateData = enumData.map(({ key, value }) => {
            return {
              name: value,
              data: new Array(data.length).fill('').map((_, index) => {
                return data[index][key];
              }),
            }
          })
  
          this.initCheckChart([formateData[0].data, formateData[1].data]);
        })
      },
  
      fetchSYlbYxl() {
        getSYlbYxl().then(res => {
          let data = res.data.data || [];
          this.chartList = data;
          console.log(555, this.chartList)
          this.$nextTick(() => {
            this.initNumChart1(data);
          })
        })
      },
  
      fetchSYgl() {
        getSYgl().then(res => {
          let data = res.data.data || {};
  
          this.deviceTotal = data
  
        })
      },
  
      getUser() {
        getUserInfo().then(res => {
          const user = res.data.data;
          this.realName = user.realName
          // this.form = {
          //   id: user.id,
          //   avatar: user.avatar,
          //   name: user.name,
          //   realName: user.realName,
          //   phone: user.phone,
          //   email: user.email,
          // }
        });
      },
      initLine() {
        const chartDom = document.getElementById('line_box');
        const myChart = this.echarts.init(chartDom);
        const option = {
          title: {
            text: '任务量月度统计',
            left: 'center',
            top: '2%',
            textStyle: {
              color: '#04A7B3',
              fontWeight: 'normal',
              fontSize: '14px'
            },
          },
          grid: {
            containLabel: true,
            top: '25%',
            bottom: '3%',
          },
          xAxis: {
            type: 'category',
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
          },
          yAxis: {
            type: 'value'
          },
          color: ['#04A7B3'],
          series: [
            {
              data: [150, 230, 224, 218, 135, 147, 260],
              type: 'line',
              areaStyle: {
                opacity: 0.4,
                color: '#04A7B3'
              }
            }
          ]
        };
        option && myChart.setOption(option);
        window.addEventListener('resize', () => {
          myChart.resize();
        }, false);
      },
      initPie1(data) {
        const chartDom = document.getElementById('pie_box1');
        const myChart = this.echarts.init(chartDom);
        const option = {
          title: {
            text: '样品阳性数/总量',
            left: 'center',
            top: '2%',
            textStyle: {
              color: '#04A7B3',
              fontWeight: 'normal',
              fontSize: '14px'
            },
          },
          grid: {
            containLabel: true,
            top: '25%',
            bottom: '3%',
          },
          tooltip: {
            trigger: 'item'
          },
          color: ['#ef6567', '#04A7B3'],
          series: [
            {
              name: '样品阳性数/总量',
              type: 'pie',
              radius: '45%',
              center: ['50%', '55%'],
              data: [
                { value: 12, name: '阳性数' },
                { value: 735, name: '总量' },
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        };
        option && myChart.setOption(option);
  
        window.addEventListener('resize', () => {
          myChart.resize();
        }, false);
  
      },
      initPie2(data) {
        const chartDom = document.getElementById('pie_box2');
        const myChart = this.echarts.init(chartDom);
        const option = {
          title: {
            text: '批次阳性数/总量',
            left: 'center',
            top: '2%',
            textStyle: {
              color: '#04A7B3',
              fontWeight: 'normal',
              fontSize: '14px'
            },
          },
          grid: {
            containLabel: true,
            top: '25%',
            bottom: '3%',
          },
          tooltip: {
            trigger: 'item'
          },
          color: ["#179f84", "#9AB958", "#EE7C00", "#BE382B", "#F4C40C", "#00AAFF", "#D9037C"],
          series: [
            {
              name: '样品阳性数/总量',
              type: 'pie',
              radius: ['30%', '50%'],
              center: ['50%', '55%'],
              data: [
                { value: 12, name: '阳性数' },
                { value: 50, name: '总量' },
              ],
            }
          ]
        };
        option && myChart.setOption(option);
        window.addEventListener('resize', () => {
          myChart.resize();
        }, false);
      },
      initCheckChart(data) {
        const chartDom = document.getElementById('check_chart');
        const myChart = this.echarts.init(chartDom);
        const option = {
          xAxis: {
            type: 'category',
            data: data[0]
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '快检仪',
              data: data[1],
              type: 'bar',
              stack: 'Ad',
              barWidth: 50,
              emphasis: {
                focus: 'series'
              },
            }
          ]
        };
        // const myChart = this.echarts.init(chartDom);
        // const option = {
        //   legend: {
        //     top: '15%'
        //   },
        //   title: {
        //     top: '5%',
        //     text: '本月不同类别检测量对比',
        //     left: 'center',
        //     textStyle: {
        //       color: '#04A7B3',
        //       fontWeight: 'normal',
        //       fontSize: '14px'
        //     },
        //   },
        //   grid: {
        //     containLabel: true,
        //     top: '25%',
        //     bottom: '5%'
        //   },
        //   color: ["#179f84", "#9AB958", "#EE7C00", "#BE382B", "#F4C40C", "#00AAFF", "#D9037C"],
        //   tooltip: {},
        //   dataset: {
        //     source: [
        //       ['product', '果蔬', '水产', '生鲜', '禽蛋', '加工', '阳性'],
        //       ['20日', 85, 35, 60, 75, 24, 1],
        //       ['21日', 81, 33, 55, 72, 21, 0],
        //       ['22日', 83, 31, 61, 71, 22, 2],
        //       ['23日', 82, 32, 62, 74, 25, 1],
        //       ['24日', 84, 35, 58, 70, 23, 1],
        //     ]
        //   },
        //   xAxis: { type: 'category' },
        //   yAxis: {},
        //   series: [
        //     { type: 'bar', barWidth: "15" },
        //     { type: 'bar', barWidth: "15" },
        //     { type: 'bar', barWidth: "15" },
        //     { type: 'bar', barWidth: "15" },
        //     { type: 'bar', barWidth: "15" },
        //   ]
        // };
        option && myChart.setOption(option);
        window.addEventListener('resize', () => {
          myChart.resize();
        }, false);
      },
      initNumChart1(data = []) {
        console.log(3333, this.chartList)
        let chartDom = document.getElementsByClassName('chart_box'); // 对应地使用ByClassName
        for (var i = 0; i < chartDom.length; i++) { // 通过for循环，在相同class的dom内绘制元素
          const myChart = this.echarts.init(chartDom[i]);
          const option = {
            tooltip: {
              trigger: 'item'
            },
            graphic: [{ //环形图中间添加文字
              type: 'text', //通过不同top值可以设置上下显示
              left: 'center',
              top: 'center',
              style: {
                text: this.chartList[i].ypxl,
                textAlign: 'center',
                fill: '#000', //文字的颜色
                fontSize: 12,
                lineHeight: 16,
              }
            }, { //环形图中间添加文字
              type: 'text', //通过不同top值可以设置上下显示
              left: 'center',
              top: '92%',
              style: {
                text: `${this.chartList[i].yxsl}/${this.chartList[i].total}`,
                textAlign: 'center',
                fill: '#000', //文字的颜色
                fontSize: 12,
                lineHeight: 16,
              }
            }],
            color: ["#179f84", "#FFE209", "#EE7C00", "#BE382B", "#F4C40C", "#00AAFF", "#9AB958"],
            series: [
              {
                name: this.chartList[i].yxsl,
                type: 'pie',
                radius: ['60%', '80%'],
                label: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  label: {
                    show: false,
                    fontSize: 14,
                    fontWeight: 'bold'
                  }
                },
                labelLine: {
                  show: false
                },
                data: [
                  { value: this.chartList[i].yxsl, name: '合格率' },
                  { value: this.chartList[i].total - this.chartList[i].yxsl, name: '不合格率' },
                ]
              }
            ]
          };
          option && myChart.setOption(option);
          window.addEventListener('resize', () => {
            myChart.resize();
          }, false);
        }
  
      },
      initNumChart2() {
        const chartDom = document.getElementById('chart2');
        const myChart = this.echarts.init(chartDom);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          graphic: [{ //环形图中间添加文字
            type: 'text', //通过不同top值可以设置上下显示
            left: 'center',
            top: 'center',
            style: {
              text: '果蔬类农药残',
              textAlign: 'center',
              fill: '#000', //文字的颜色
              fontSize: 12,
              lineHeight: 16,
            }
          }, { //环形图中间添加文字
            type: 'text', //通过不同top值可以设置上下显示
            left: 'center',
            top: '92%',
            style: {
              text: '231/235次',
              textAlign: 'center',
              fill: '#000', //文字的颜色
              fontSize: 12,
              lineHeight: 16,
            }
          }],
          color: ['#04A7B3', '#FFE209'],
          series: [
            {
              name: '果蔬农药残',
              type: 'pie',
              radius: ['60%', '80%'],
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: false,
                  fontSize: 14,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 92, name: '合格率' },
                { value: 8, name: '不合格率' },
              ]
            }
          ]
        };
        option && myChart.setOption(option);
        window.addEventListener('resize', () => {
          myChart.resize();
        }, false);
      },
      initNumChart3() {
        const chartDom = document.getElementById('chart3');
        const myChart = this.echarts.init(chartDom);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          graphic: [{ //环形图中间添加文字
            type: 'text', //通过不同top值可以设置上下显示
            left: 'center',
            top: 'center',
            style: {
              text: '果蔬类农药残',
              textAlign: 'center',
              fill: '#000', //文字的颜色
              fontSize: 12,
              lineHeight: 16,
            }
          }, { //环形图中间添加文字
            type: 'text', //通过不同top值可以设置上下显示
            left: 'center',
            top: '92%',
            style: {
              text: '231/235次',
              textAlign: 'center',
              fill: '#000', //文字的颜色
              fontSize: 12,
              lineHeight: 16,
            }
          }],
          color: ['#04A7B3', '#FFE209'],
          series: [
            {
              name: '果蔬农药残',
              type: 'pie',
              radius: ['60%', '80%'],
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: false,
                  fontSize: 14,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 92, name: '合格率' },
                { value: 8, name: '不合格率' },
              ]
            }
          ]
        };
        option && myChart.setOption(option);
        window.addEventListener('resize', () => {
          myChart.resize();
        }, false);
      },
      initNumChart4() {
        const chartDom = document.getElementById('chart4');
        const myChart = this.echarts.init(chartDom);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          graphic: [{ //环形图中间添加文字
            type: 'text', //通过不同top值可以设置上下显示
            left: 'center',
            top: 'center',
            style: {
              text: '果蔬类农药残',
              textAlign: 'center',
              fill: '#000', //文字的颜色
              fontSize: 12,
              lineHeight: 16,
            }
          }, { //环形图中间添加文字
            type: 'text', //通过不同top值可以设置上下显示
            left: 'center',
            top: '92%',
            style: {
              text: '231/235次',
              textAlign: 'center',
              fill: '#000', //文字的颜色
              fontSize: 12,
              lineHeight: 16,
            }
          }],
          color: ['#04A7B3', '#FFE209'],
          series: [
            {
              name: '果蔬农药残',
              type: 'pie',
              radius: ['60%', '80%'],
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: false,
                  fontSize: 14,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 92, name: '合格率' },
                { value: 8, name: '不合格率' },
              ]
            }
          ]
        };
        option && myChart.setOption(option);
        window.addEventListener('resize', () => {
          myChart.resize();
        }, false);
      },
      getTime() {
        const date = new Date();
        const year = date.getFullYear();
        let month = date.getMonth() + 1;
        month = (month > 9) ? month : ("0" + month);
        let day = date.getDate();
        day = (day < 10) ? ("0" + day) : day;
        this.currentDay = day
        this.today = year + "-" + month + "-" + day;
      },
      handleChange(val) {
        window.console.log(val);
      }
    },
  };
  </script>
  
  <style scoped lang="less">
  .right_top {
    width: 100%;
    height: 170px;
    display: flex;
    justify-content: space-between;
  
    .right_pie {
      width: 47%;
      height: 100%;
      background: #F3F6F9;
      border: 1px solid #ccc;
    }
  }
  
  .right_center {
    width: 100%;
    height: 260px;
    background: #F3F6F9;
    border: 1px solid #ccc;
    margin-top: 15px;
    overflow: hidden;
  
    .title_div {
      width: 100%;
      height: 35px;
      line-height: 35px;
      text-align: center;
      border-bottom: 1px dotted #ccc;
      color: #04A7B3;
    }
  
    .title_div2 {
      color: rgba(249, 165, 39, 1);
    }
  
    .item_div {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 0 10px;
      color: #04A7B3;
      margin-top: 8px;
    }
  
    .item_div2 {
      color: rgba(249, 165, 39, 1);
    }
  }
  
  .right_line {
    width: 100%;
    height: 300px;
    background: #F3F6F9;
    border: 1px solid #ccc;
    margin-top: 15px;
  }
  
  .center_bottom {
    width: 100%;
    height: 400px;
    margin-top: 10px;
  }
  
  .center_center {
    width: 100%;
    height: 400px;
    background: #F3F6F9;
    border: 1px solid #ccc;
  }
  
  .center_top {
    width: 100%;
    display: flex;
  
    .notice_box {
      width: calc(100% - 120px);
      height: 105px;
      overflow: auto;
      background: rgba(4, 167, 179, 0.2);
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
  
      .notice_item {
        display: flex;
        align-items: center;
        color: rgba(4, 167, 179, 1);
        padding: 0 10px;
  
        img {
          width: 15px;
          height: auto;
          margin-right: 5px;
        }
      }
    }
  
    .today_box {
      width: 105px;
      height: 105px;
      background: #04A7B3;
      display: flex;
      flex-direction: column;
      color: #fff;
      justify-content: center;
      align-items: center;
  
      .day_box {
        font-size: 28px;
        margin-bottom: 10px;
      }
    }
  }
  
  .chart_box {
    width: 50%;
    height: 200px;
    padding: 10px;
    box-sizing: border-box;
  }
  
  .rate_img {
    width: 18px;
    height: auto;
    position: relative;
    top: 3px;
  }
  
  .check_title {
    width: 100%;
    color: #04A7B3;
    text-align: center;
    font-size: 14px;
  }
  
  .check_box {
    width: 100%;
    background: #F3F6F9;
    padding: 20px 10px;
    border: 1px solid #ccc;
  
    .pie_box {
      width: 100%;
      display: flex;
      flex-flow: row wrap;
    }
  }
  
  .total_item {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    font-size: 14px;
    border: 1px solid #094F6C;
    border-radius: 5px;
    color: #A6A3A3;
  
    img {
      width: 25px;
      height: auto;
    }
  }
  
  .el-font-size {
    font-size: 14px;
  }
  
  .el-row {
  
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .el-col {
    border-radius: 4px;
  }
  
  .bg-purple-dark {
    background: #99a9bf;
  }
  
  .bg-purple {
    background: #d3dce6;
  }
  
  .bg-purple-light {
    background: #e5e9f2;
  }
  
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
  
  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }
  
  .left2 {
    border: 1px solid #084E6C;
    height: 120px;
    border-radius: 5px;
    font-size: 0.7vw;
  }
  
  .leftTitle {
    color: #F0CE09;
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .left3 {
    border: 1px solid #084E6C;
    height: 280px;
    border-radius: 5px;
    margin-top: 20px
  }
  
  .left3T {
    width: 100%;
    text-align: center;
    color: rgb(11, 229, 245);
    font-weight: bold;
    margin-top: 10px
  }
  
  .left4T {
    width: 100%;
    text-align: center;
    color: rgb(11, 229, 245);
    font-weight: bold;
    margin-top: 10px;
    font-size: 0.8vw;
  }
  
  .left4 {
    border: 1px solid #084E6C;
    height: 410px;
    border-radius: 5px;
    margin-top: 20px
  }
  
  .main {
    position: relative;
    margin-top:-40px
  }
  
  .moon {
    background-color: rgb(11, 11, 46);
  }
  
  .lighr {
    background-color: #fff;
  }
  
  .right1 {
    border: 1px solid #084E6C;
    border-radius: 5px;
    height: 200px;
  }
  
  .right2 {
    border: 1px solid #084E6C;
    border-radius: 5px;
    height: 200px;
  }
  
  .right4 {
    border: 1px solid #084E6C;
    margin-top: 20px;
    border-radius: 5px;
    height: 520px;
    margin-bottom: 30px;
  }
  
  .rightT2 {
    width: 100%;
   
    text-align: center;
    font-size: 16px;
    height: 30px;
    line-height: 30px;
   
  }
  .rightT21{
    color: #fff;
    border-bottom: 1px dashed #fff;
  }
  .rightT22{
    color: #999;
    border-bottom: 1px dashed #999;
  }
  .text1 {
    width: 65%;
    min-width: 65%;
    color: #EE9E08;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 10px;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    font-size: 0.7vw;
  }
  
  .text2 {
    color: #EE9E08;
    margin-right: 10px;
    height: 40px;
    margin-left: 20px;
    font-size: 14px;
    position: absolute;
    margin-top: 4px;
    font-size: 0.7vw;
  }
  
  .tips {
    position: absolute;
    color: #A6A3A3;
    margin-top: -160px;
    float: right;
    right: 30px;
    font-size: 0.8vw;
  }
  
  .center {
    border: 1px solid #084E6C;
    margin-top: 10px;
    border-radius: 5px;
    height: 325px;
    margin-bottom: 30px;
  }
  
  .title2 {
    width: 50%;
    margin-left: 25%;
    text-align: center;
    color: rgb(11, 229, 245);
    font-size: 30px;
    height: 80px;
    line-height: 80px;
    margin-top: 40px;
  }
  
  .time {
    position: absolute;
    color: #A6A3A3;
  
    height: 80px;
    line-height: 80px;
  }
  
  .action {
    float: right;
    color: red;
    height: 80px;
    line-height: 80px;
    position: absolute;
    right: 160px;
    margin-top: -80px;
    display: flex;
  }
  
  .img1 {
    position: absolute;
    float: right;
    width: 20px;
    height: 30px;
    margin-left: 240px;
    margin-top: 24px;
  }
  
  .img1 {
    position: absolute;
    float: right;
    width: 20px;
    height: 20px;
    margin-left: 240px;
    margin-top: 30px;
  }
  
  .img2 {
    position: absolute;
    float: right;
    width: 20px;
    height: 20px;
    margin-left: 270px;
    margin-top: 30px;
  }
  
  .img3 {
    position: absolute;
    float: right;
    width: 20px;
    height: 20px;
    margin-left: 300px;
    margin-top: 30px;
  }
  
  .ditu {
    border: 1px solid #084E6C;
    border-radius: 5px;
    background-image: url('../../assets/img/300.jpg');
    background-size: 100% 100%;
    background-repeat: no-repeat
  }
  </style>
  