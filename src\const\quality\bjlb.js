export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  addBtn: false,
  editBtn: false,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  align: 'center',
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "报警ID",
      prop: "valertid",
      type: "input",
      hide: true,
    },
    {
      label: "视频ID",
      prop: "videoid",
      type: "input",
    },
    {
      label: "视频名称",
      prop: "videoname",
      type: "input",
      search: true,
    },
    {
      label: "报警时间",
      prop: "valerttime",
      type: "input",
    },
    {
      label: "理论工作时长",
      prop: "theoreticalWorkingHours",
      type: "input",
    },
    {
      label: "实际工作时长",
      prop: "realityWorkingHours",
      type: "input",
    },
    {
      label: "是否报警",
      prop: "isEarlyWarning",
      type: "select",
      dicData: [
        {
          label: '否',
          value: '0',
        },
        {
          label: '是',
          value: '1',
        },
      ],
    },
    // {
    //   label: "报警内容",
    //   prop: "valertcontent",
    //   type: "input",
    // },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
