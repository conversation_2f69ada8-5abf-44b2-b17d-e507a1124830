<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button v-if="permission.cysj_import" type="primary"
                   size="small"
                   icon="el-icon-download"
                   plain
                   @click="downExcel">
          下载模板
        </el-button>
        <el-upload v-if="permission.cysj_import" :headers="uploadHeaders" :on-success="handleAvatarSuccess" :show-file-list="false" action="/api/jc/cysj/addExcel" style="display: inline-block">
          <el-button type="primary"
                     size="small"
                     icon="el-icon-upload "
                     plain>
            导入
          </el-button>
        </el-upload>
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.cysj_delete"
          @click="handleDelete"
        >删 除
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="qrcode">
        <el-button type="text" size="small" @click="showQr(row)"> 二维码 </el-button>
      </template>
    </avue-crud>

    <el-dialog
      append-to-body
      title="采样二维码"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <div
        style="
          display: flex;
          justify-content: center;
          align-items: center;
          padding-bottom: 20px;
        "
      >
        <Qrcode1 id="qrcode" :url="qrcodeUrl"></Qrcode1>
      </div>
    </el-dialog>
    <!-- <div class="box">
      <div class="dn">下载</div>
      <div class="sx">刷新</div>
      <div class="cx">查询</div>
    </div> -->
  </basic-container>
</template>

<script>
import {add, downLoadModel, getList, remove, update} from "@/api/check/cysj";
import option from "@/const/check/cysj";
import {mapGetters} from "vuex";
import Qrcode1 from "@/components/Qrcode";
import {getToken} from '@/util/auth';
import {getDept} from '@/api/system/dept'

export default {
  data() {
    return {
      qrcodeUrl: "https://www.baidu.com",
      dialogVisible: false,
      form: {
        sjdwmc: '',
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      uploadHeaders:{},
      deptDetail: {}
    };
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
      // export excel, avue reconfig
      option.excelBtn = this.vaildData(this.permission.cysj_export, false);

      return {
        addBtn: this.vaildData(this.permission.cysj_add, false),
        viewBtn: this.vaildData(this.permission.cysj_view, false),
        delBtn: this.vaildData(this.permission.cysj_delete, false),
        editBtn: this.vaildData(this.permission.cysj_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  components: {
    Qrcode1,
  },
  watch:{
    'form.ypdl': {
      handler(newVal) {
        let type = null
        if(newVal == 1){
          type = 'A'
        }
        if(newVal == 2){
          type = 'B'
        }
        if(newVal == 3){
          type = 'C'
        }
        if(newVal == 4){
          type = 'D'
        }
        if(newVal == 5){
          type = 'E'
        }
        if(newVal == 6){
          type = 'F'
        }
        if(newVal == 7){
          type = 'G'
        }
        this.form.cybh = this.deptDetail.remark + type + new Date().getTime()
        this.form.cydbh = this.deptDetail.remark + type + new Date().getTime()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getDeptDetail()
    this.uploadHeaders = {
      'Blade-Auth': 'bearer ' + getToken() ,
      'Authorization': 'Basic c2FiZXI6c2FiZXJfc2VjcmV0',
      'Tenant-Id': '000000'
    }
    window.__cysj_component__ = this;
  },
  beforeDestroy() {
    delete window.__cysj_component__;
  },
  methods: {
    handleNodeClick(data,node) {
      //将data.ypdl 转成数字
      data.ypdl = parseInt(data.ypdl)
      const ypdlColumn = this.option.column.find(item => item.prop === 'ypdl');
      this.setColumnValue('ypdlmc',  data.ypmc)
      this.$set(ypdlColumn, 'cascaderIndex',  data.ypdl-1); // 示例使用节点层级
    },
    handleNodeClick2(data,val,prop) {
      console.log(data)
      this.form[prop] = data[val];
      // 如果需要强制更新视图
      this.$set(this.form, prop, data[val]);
    },
    setColumnValue(prop, value) {
      const column = this.option.column.find(item => item.prop === prop);
      if (column) {
        this.$set(column, 'value', value);
      } else {
        console.warn(`未找到 ${prop} 列配置`);
      }
    },
    getDeptDetail(){
      getDept(this.userInfo.dept_id).then(res=>{
        this.deptDetail = res.data.data
      })
    },
    timestampToDateTime() {
      var date = new Date();
      var YY = `${date.getFullYear()}`.slice(-2);
      var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);
      var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
      var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours());
      var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes());
      var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
      const str = YY + MM + DD + hh + mm
      let str2 = date.getHours() + date.getMinutes() + date.getSeconds()
      str2 = str2<10 ? '00'+str2:str2<100?'0'+str2:str2
      return str + str2
    },
    downExcel(){
      downLoadModel().then(res=>{
        this.$exportCsv(res.data,'采样数据模板')
      })
    },
    handleAvatarSuccess(res, file) {
      if(res.code != 200){
        this.$message.error('导入信息不正确')
      }else{
        this.$message.success(res.msg)
        this.onLoad(this.page);
      }
    },
    showQr(data) {
      // 使用深拷贝创建新对象，确保响应式更新
      this.detailInfo = Object.assign({}, data)
      console.log(JSON.stringify(data))
      const params = new URLSearchParams({
        cybh: data.cybh,
        ssrwmc: data.sjdwmc,
        cyrymc: data.cyrymc,
        cysj: data.cysj,
        ypmc: data.ypmc,
        ypxl: data.ypxl,
        shidmc: data.$rwid,
        sjdwmc: data.sjdwmc
      });
      const href = window.location.href.split('#');
      const url = `${href[0]}#/check/qrcode?${ params }`

      // 强制二维码组件重新渲染
      this.qrcodeUrl = null
      this.$nextTick(() => {
        this.qrcodeUrl = url
        this.dialogVisible = true;
      })
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowEdit() {
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        // getDetail(this.form.id).then(res => {
        //   this.form = res.data.data;
        // });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      if(params.createTime){
        this.query = {
          ...params,
          startDate: params.createTime[0],
          endDate: params.createTime[1],
        };
        delete this.query.createTime
      }else{
        this.query = params
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style >
.el-button--small.is-circle {
  padding: 12px !important;
}
/* .box {
  display: flex;
  position: absolute;
  top: 149px;
  z-index: 1000;
  right: 4.5%;
  font-size: 12px;
}
.dn {
  position: relative;
  left: -21px;
}
.sx {
  position: relative;
  left: 5px;
}
.cx {
  position: relative;
  left: 29px;
} */

</style>
