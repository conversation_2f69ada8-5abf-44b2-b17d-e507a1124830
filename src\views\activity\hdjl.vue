<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <div style="display: flex;float: right">
          <el-button v-if="permission.hdjl_import" type="primary"
                     size="small"
                     icon="el-icon-download"
                     plain
                     style="margin-left: 10px"
                     @click="downExcel">
            下载模板
          </el-button>
          <el-upload v-if="permission.hdjl_import" :headers="uploadHeaders" :on-success="handleAvatarSuccess" :show-file-list="false" action="/api/xchd/hdjl/addExcel">
            <el-button type="primary"
                       size="small"
                       icon="el-icon-upload "
                       plain
                       style="margin-left: 10px">
              导入
            </el-button>
          </el-upload>
          <el-button type="danger"
                     size="small"
                     icon="el-icon-delete"
                     plain
                     style="margin-left: 10px"
                     v-if="permission.hdjl_delete"
                     @click="handleDelete">删 除
          </el-button>
        </div>

      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, downLoadModel} from "@/api/activity/hdjl";
  import option from "@/const/activity/hdjl";
  import {mapGetters} from "vuex";
  import {getToken} from '@/util/auth';

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        uploadHeaders:{}
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        // export excel, avue reconfig
        // option.excelBtn = this.vaildData(this.permission.hdjl_export, false);

        return {
          addBtn: this.vaildData(this.permission.hdjl_add, false),
          viewBtn: this.vaildData(this.permission.hdjl_view, false),
          delBtn: this.vaildData(this.permission.hdjl_delete, false),
          editBtn: this.vaildData(this.permission.hdjl_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    mounted() {
      this.uploadHeaders = {
        'Blade-Auth': 'bearer ' + getToken() ,
        'Authorization': 'Basic c2FiZXI6c2FiZXJfc2VjcmV0',
        'Tenant-Id': '000000'
      }
    },
    methods: {
      handleAvatarSuccess(res, file) {
        if(res.code != 200){
          this.$message.error(res.msg)
        }else{
          this.$message.success(res.msg)
          this.onLoad(this.page);
        }
      },
      downExcel(){
        downLoadModel().then(res=>{
          this.$exportCsv(res.data,'宣传好的模板')
        })
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
