<template>
  <div>
    <div @click="print" class="print">打印</div>
    <div id="print">
      <h3 class="title">电子天平期间核查原始记录表</h3>
      <table>
        <tr>
          <td class="w10">电子天平仪器编号</td>
          <td class="w15">{{ dztphc.sbbh }}</td>
          <td class="w20">规格型号</td>
          <td class="w20">{{ dztphc.ggxh }}</td>
          <td class="w15">期间核查依据</td>
          <td class="w15">{{ dztphc.zcfg }}</td>
        </tr>
        <tr>
          <td class="w10">期间核查日期</td>
          <td class="w15">{{ dztphc.qjhcrq }}</td>
          <td class="w20">环境温度</td>
          <td class="w20">{{ dztphc.hjwd }}</td>
          <td class="w15">环境湿度</td>
          <td class="w15">{{ dztphc.hjsd }}</td>
        </tr>
        <tr>
          <td class="w10">期间核查所用砝码设备编号</td>
          <td class="w15">{{ dztphc.fmjdzsbh }}</td>
          <td class="w20">砝码检定证书编号</td>
          <td class="w20">{{ dztphc.fmjdzsbh }}</td>
          <td class="w15">砝码检定日期</td>
          <td class="w15">{{ dztphc.fmjdrq }}</td>
        </tr>
        <tr>
          <td class="tf w10" colspan="6">期间核查：</td>
        </tr>
        <tr>
          <td class="tf w10">一、外观检查记录</td>
          <td class="tl" colspan="5">
            <!-- □可以正常使用，外观符合要求 <br />□外观不符合要求： -->
            {{ dztphc.wgjcjl }}
          </td>
        </tr>
        <tr>
          <td class="tf w10" colspan="6">二、称量核查：</td>
        </tr>
      </table>
      <table>
        <tr>
          <td class="w5" rowspan="2">序号</td>
          <td class="w5" rowspan="2">*核查点/g</td>
          <td class="w20" colspan="4">天平读数/g</td>
          <td class="w5" rowspan="2">平均值/g</td>
          <td class="w5" rowspan="2">相对误差/g</td>
          <td class="w5" rowspan="2">*允差/g</td>
          <td class="w5" rowspan="2">内部校准结果</td>
        </tr>
        <tr>
          <td class="w5">第一次</td>
          <td class="w5">第二次</td>
          <td class="w5">第三次</td>
          <td class="w5">第四次</td>
        </tr>
        <tr v-for="(item, index) in clhc">
          <td class="w5">{{ index + 1 }}</td>
          <td class="w5">{{ item.hcd }}</td>
          <td class="w5">{{ item.tpds1 }}</td>
          <td class="w5">{{ item.tpds2 }}</td>
          <td class="w5">{{ item.tpds3 }}</td>
          <td class="w5">{{ item.tpds4 }}</td>
          <td class="w5">{{ item.pjz }}</td>
          <td class="w5">{{ item.wc }}</td>
          <td class="w5">{{ item.yxwc }}</td>
          <td class="w5">{{ item.nbxzjg }}</td>
        </tr>
      </table>
      <table>
        <tr>
          <td class="tf w10" colspan="9">三、偏载核查：</td>
        </tr>
        <tr>
          <td class="tl w10" colspan="3">偏载校准位置示意图：</td>
          <td class="w5"><img src="./7-1.png" /></td>
          <td class="w5">□</td>
          <td class="w5"></td>
          <td class="w5"><img src="./7-2.png" /></td>
          <td class="w5">□</td>
          <td class="w5"><img src="./7-3.png" /></td>
          <td class="w5">□</td>
        </tr>
        <tr>
          <td class="w5" rowspan="2">序号</td>
          <td class="w5" rowspan="2">偏载校准点/g</td>
          <td class="w20" colspan="4">天平读数/g</td>
          <td class="w5" rowspan="2">平均值/g</td>
          <td class="w5" rowspan="2">相对误差/g</td>
          <td class="w5" rowspan="2">*允差/g</td>
          <td class="w5" rowspan="2">内部校准结果</td>
        </tr>
        <tr>
          <td class="w5">第一次</td>
          <td class="w5">第二次</td>
          <td class="w5">第三次</td>
          <td class="w5">第四次</td>
        </tr>
        <tr v-for="(item, index) in pzhy">
          <td class="w5">{{ index + 1 }}</td>
          <td class="w5">{{ item.hcd }}</td>
          <td class="w5">{{ item.tpds1 }}</td>
          <td class="w5">{{ item.tpds2 }}</td>
          <td class="w5">{{ item.tpds3 }}</td>
          <td class="w5">{{ item.tpds4 }}</td>
          <td class="w5">{{ item.pjz }}</td>
          <td class="w5">{{ item.wc }}</td>
          <td class="w5">{{ item.yxwc }}</td>
          <td class="w5">{{ item.nbxzjg }}</td>
        </tr>
      </table>
      <table>
        <tr>
          <td class="tf w10" colspan="9">四、重复性核查：</td>
        </tr>
        <tr>
          <td class="w5" rowspan="2">序号</td>
          <td class="w5" rowspan="2">重复性校准点/g</td>
          <td class="w20" colspan="4">天平读数/g</td>
          <td class="w5" rowspan="2">平均值/g</td>
          <td class="w5" rowspan="2">相对误差/g</td>
          <td class="w5" rowspan="2">*允差/g</td>
          <td class="w5" rowspan="2">内部校准结果</td>
        </tr>
        <tr>
          <td class="w5">第一次</td>
          <td class="w5">第二次</td>
          <td class="w5">第三次</td>
          <td class="w5">第四次</td>
        </tr>
        <tr v-for="(item, index) in cfxhc">
          <td class="w5">{{ index + 1 }}</td>
          <td class="w5">{{ item.hcd }}</td>
          <td class="w5">{{ item.tpds1 }}</td>
          <td class="w5">{{ item.tpds2 }}</td>
          <td class="w5">{{ item.tpds3 }}</td>
          <td class="w5">{{ item.tpds4 }}</td>
          <td class="w5">{{ item.pjz }}</td>
          <td class="w5">{{ item.wc }}</td>
          <td class="w5">{{ item.yxwc }}</td>
          <td class="w5">{{ item.nbxzjg }}</td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">期间核查最终结论：</td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">
            {{ dztphc.hcjl }}
          </td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">
            <div class="flex">
              核查人：
              <img v-if="dztphc.tjrqm" :src="dztphc.tjrqm" class="avatar" />
              &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;
              <div class="flex">
                审核人 ：
                <img v-if="dztphc.tjrqm" :src="dztphc.tjrqm" class="avatar" />
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td class="tl w10" colspan="10">
            *：允差：0g\2g：±0.05g；<br />
            &nbsp;&nbsp;&nbsp;&nbsp;3g\5g：±0.10g；<br />
            &nbsp;&nbsp;&nbsp;&nbsp;50g\100g\200g\300g\500g：±0.50g
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import table from "../../views/util/table.vue";
import * as jsPDF from "jspdf";

export default {
  components: { table },
  name: "qctif07",
  data() {
    return {};
  },
  props: {
    dztphc: null,
    cfxhc: null,
    clhc: null,
    pzhy: null,
  },
  created() {},

  methods: {
    print() {
      const printContents = document.getElementById("print").innerHTML;
      const originalContents = document.body.innerHTML;
      // const doc = new jsPDF();

      document.body.innerHTML = printContents;
      window.print();
      //   html2canvas(printContents).then(canvas => {
      //   // 添加 Canvas 对象到 PDF 文档
      //   doc.addImage(canvas.toDataURL('image/png'), 'PNG', 0, 0);

      //   // 下载 PDF 文件
      //   doc.save('example.pdf');
      // });
      // document.body.innerHTML = originalContents;
      window.location.reload();
    },
  },
};
</script>

<style  scoped>
.title {
  text-align: center;
  position: relative;
}
table {
  border-collapse: collapse;
  width: 100%;
}
table th,
table td {
  padding: 5px;
  text-align: center;
  border: 1px solid #ddd;
}
.flex {
  display: flex;
}
table th {
  background-color: #f2f2f2;
  font-weight: bold;
  font-size: 1em;
}
.w15 {
  width: 15%;
}
.tf {
  text-align: left;
  font-weight: bold;
}
.tl {
  text-align: left;
}
.w20 {
  width: 20%;
}
.w10 {
  width: 10%;
}
.w5 {
  width: 5%;
}
img {
  width: 60px;
}
.print {
  width: 100px;
  background: #04a7b3;
  color: #fff;
  text-align: center;
  padding: 10px;
}
.flex {
  display: flex;
}
</style>
