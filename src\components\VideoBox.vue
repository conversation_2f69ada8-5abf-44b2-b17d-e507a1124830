<template>
  <div :id="'videobox-' + id">
    <video :id="id" style="width: 100%;height:100%;"
           controls
           class="video-js"
           :autoplay="autoplay1"
           muted
           preload="auto"
           data-setup="{}">
      <source id="source" :src="srcUrl" type="application/x-mpegURL">
    </video>
  </div>
</template>

<script>
import Videojs from "video.js"; // 引入Videojs

export default {
  name: 'VideoBox',
  components: {},
  props: {
    "id": {
      type: String,
      required: true
    },
    "srcUrl": {
      type: String,
      default: ''
    },
    "autoplay1": {  // 添加自动播放控制参数
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isPlay: false,
      myPlayer: null,
    }
  },
  computed: {
  },
  watch: {
    // 监听 srcUrl 变化，重新初始化视频
    srcUrl(newVal) {
      if (newVal) {
        this.initVideo(newVal);
      }
    }
  },
  mounted() {
    this.initVideo(this.srcUrl);
  },
  destroyed() {
    // 销毁时释放资源
    if (this.myPlayer) {
      this.myPlayer.dispose();
    }
  },
  methods: {
    initVideo(nowPlayVideoUrl) {
      // 如果已存在播放器，先销毁
      if (this.myPlayer) {
        this.myPlayer.pause();
        this.myPlayer.dispose();
        this.myPlayer = null;
      }

      // 清空容器并重新创建video元素
      const videoBoxId = 'videobox-' + this.id;
      const videoBox = document.getElementById(videoBoxId);
      if (videoBox) {
        videoBox.innerHTML = '';
        const str2 = `<video id="${this.id}" style="width: 100%;height:100%;"
               controls
               class="video-js"
               ${this.autoplay1 ? 'autoplay="autoplay"' : ''}
               muted
               preload="auto"
               data-setup="{}">
          <source id="source" src="${nowPlayVideoUrl}" type="application/x-mpegURL">
        </video>`;
        videoBox.innerHTML = str2;
      }

      // 使用传入的 id 初始化 video.js 播放器
      this.$nextTick(() => {
        this.myPlayer = Videojs(this.id, {
          bigPlayButton: true,
          textTrackDisplay: false,
          posterImage: true,
          errorDisplay: false,
          autoplay: this.autoplay1
        }, () => {
          if (this.autoplay1) {
            this.myPlayer.play();
          }
        });
      });
    }
  },
}
</script>

<style scoped lang="less">
.video-js {
  width: 100%;
  height: auto;
}
</style>
