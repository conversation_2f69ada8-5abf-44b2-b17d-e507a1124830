import request from '@/router/axios';

/**
 * 
 *  设备-各快检室设备类型 
 */
export const sblxtj = _ => {
    return request({
        url: '/tb/sblxtj',
        method: 'post',
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

/**
 * 设备-核查统计 
 */
export const xzsblx = (type = 1) => {
    return request({
        url: '/tb/xzsblx',
        method: 'post',
        params: {
            type
        }
    })
}
/**
 * 设备-核查统计
 */
export const hctj =()=>{
    return request({
        url: '/tb/hctj',
        method: 'get',
    })
}