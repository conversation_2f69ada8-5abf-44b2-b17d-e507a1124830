<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="primary"
                   size="small"
                   icon="el-icon-plus"
                   plain
                   @click="showAdd">新 增
        </el-button>
      </template>
      <template slot="menu" slot-scope="{row}">
        <el-button type="text"
          size="small"
          icon="el-icon-delete"
          @click="showDetail(row)">查 看
        </el-button>
      </template>
<!--      <template slot-scope="{row}" slot="hzd">-->
<!--        <el-button type="primary" size="mini" @click="showDetail(row)">查看</el-button>-->
<!--      </template>-->
    </avue-crud>

    <el-dialog title="新增巡查统计" :visible.sync="addShow" append-to-body>
      <div>
        <el-select
          style="width: 100%"
          size="small"
          v-model="currentTem"
          placeholder="请先选择模板"
        >
          <el-option
            v-for="item in templateList"
            :key="item.id"
            :label="item.mbmc"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <div style="text-align: center">
        <el-button
          style="margin-top: 20px;width: 180px"
          size="small"
          type="primary"
          @click="selectOK"
        >确 定</el-button>
        <br>
        <el-button
          style="margin-top: 20px;width: 180px"
          size="small"
          @click="goAdd"
        >管理巡查单模板</el-button>
      </div>
    </el-dialog>

    <el-dialog title="巡查回执单" :visible.sync="diaShow" append-to-body>
      <div>
        <el-descriptions :label-style="des_label" :column="2" size="mini" border>
          <el-descriptions-item label="检查单位">{{detail.bjdwmc}}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{detail.bjdwmc}}</el-descriptions-item>
          <el-descriptions-item label="单位类型">{{detail.bjdwmc}}</el-descriptions-item>
          <el-descriptions-item label="巡查时间">{{detail.bjdwmc}}</el-descriptions-item>
          <el-descriptions-item label="不符合项说明">{{detail.bjdwmc}}</el-descriptions-item>
          <el-descriptions-item label="综合得分">{{detail.bjdwmc}}</el-descriptions-item>
          <el-descriptions-item label="评级">{{detail.bjdwmc}}</el-descriptions-item>
        </el-descriptions>
        <div>被检查单位人员签名</div>
        <div>二维码</div>
      </div>
    </el-dialog>
     <!-- <div class="box">
      <div class="dn">下载</div>
      <div class="sx">刷新</div>
      <div class="cx">查询</div>
    </div> -->
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, getTemList} from "@/api/patrol/xctj";
  import {option, closeOption} from "@/const/patrol/xctj";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        tabValue: 1,
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        closeOption: closeOption,
        data: [],
        diaShow: false,
        detail: {},
        des_label:{
          'width': '100px'
        },
        addShow: false,
        templateList: [],
        currentTem: '',
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        // export excel, avue reconfig
        option.excelBtn = this.vaildData(this.permission.xctj_export, false);

        return {
          addBtn: this.vaildData(this.permission.cysj_add, false),
          viewBtn: this.vaildData(this.permission.cysj_view, false),
          delBtn: this.vaildData(this.permission.cysj_delete, false),
          editBtn: this.vaildData(this.permission.cysj_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    mounted() {
      this.getTemList()
    },
    methods: {
      showAdd(){
        this.addShow = true;
      },
      selectOK(){
        if(!this.currentTem){
          this.$message.error('请选择模板后操作')
          return
        }
        this.addShow = false;
        this.$router.push({
          path: '/patrol/xctj/new',
          query: {
            id: this.currentTem
          }
        })
      },
      showDetail(row){
        this.$router.push({
          path: '/patrol/xctj/detail',
          query: {
            id: row.id,
            mbid: row.mbid
          }
        })
      },
      goAdd() {
        this.$router.push({
          path: "/patrol/glxcmb",
        });
      },
      getTemList() {
        getTemList(1, 10000).then((res) => {
          this.templateList = res.data.data.records;
        });
      },

      closeItem(){},
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style lang="less" scoped>
</style>
