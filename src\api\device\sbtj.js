import request from '@/router/axios';
export const downLoadModel = () => {
  return request({
    url: '/api/sb/sbtj/downLoadModel',
    method: 'get',
    responseType: 'blob',
    params: {
    }
  })
}
export const getList = (current, size, params) => {
  return request({
    url: '/api/sb/sbtj/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/sb/sbtj/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/sb/sbtj/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/sb/sbtj/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/sb/sbtj/submit',
    method: 'post',
    data: row
  })
}

