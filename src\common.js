//下载excel文件
export function exportCsv(csv, title) {
  const filename = `${title}.csv` // 拼接文件名
  const blob = new Blob([csv])  //创建一个新的 Blob 对象
  const url = window.URL.createObjectURL(blob)  //  把一个blob对象转化为一个Blob URL，创建下载链接
  const downloadLink = document.createElement('a')  // 生成一个a标签
  downloadLink.href = url
  downloadLink.download = filename  // // dowload属性指定下载后文件名
  document.body.appendChild(downloadLink) //将a标签添加到body中
  downloadLink.click() // 点击下载
  document.body.removeChild(downloadLink)  // 下载完成后移除元素
  window.URL.revokeObjectURL(url); // 释放掉blob对象
}

//下载文件
export function exportFile(csv, title) {
  const filename = `${title}` // 拼接文件名
  const blob = new Blob([csv])  //创建一个新的 Blob 对象
  const url = window.URL.createObjectURL(blob)  //  把一个blob对象转化为一个Blob URL，创建下载链接
  const downloadLink = document.createElement('a')  // 生成一个a标签
  downloadLink.href = url
  downloadLink.download = filename  // // dowload属性指定下载后文件名
  document.body.appendChild(downloadLink) //将a标签添加到body中
  downloadLink.click() // 点击下载
  document.body.removeChild(downloadLink)  // 下载完成后移除元素
  window.URL.revokeObjectURL(url); // 释放掉blob对象
}
