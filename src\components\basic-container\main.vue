<template>
  <div class="basic-container"
       :style="styleName"
       :class="{'basic-container--block':block}">
    <div class="top_box" v-if="top">
      <div class="name_box">
        <el-button @click="goBack" v-if="back" type="text" icon="el-icon-arrow-left" style="font-size: 14px;margin-right: 15px">返回</el-button>
        {{routeName}}
      </div>
      <div class="input_box" v-if="search">
<!--        <el-input class="input_main_box" placeholder="快速搜索" v-model="keyword">-->
<!--          <el-button slot="append" icon="el-icon-search"></el-button>-->
<!--        </el-input>-->
      </div>
    </div>

    <el-card class="basic-container__card">
      <slot></slot>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "basicContainer",
  props: {
    radius: {
      type: [String, Number],
      default: 10
    },
    background: {
      type: String
    },
    block: {
      type: Boolean,
      default: false
    },
    search: {
      type: Boolean,
      default: true
    },
    top: {
      type: Boolean,
      default: true
    },
    back: {
      type: <PERSON>olean,
      default: false
    }
  },
  data(){
    return {
      routeName: '',
      keyword: ''
    }
  },
  computed: {
    styleName () {
      return {
        borderRadius: this.setPx(this.radius),
        background: this.background,
      }
    }
  },
  mounted() {
    this.routeName = this.$route.name
  },
  methods:{
    goBack(){
      this.$router.back()
    }
  }
};
</script>

<style lang="scss">
  .top_box{
    width: 100%;
    box-sizing: border-box;
    height: 60px;
    background: url("../../assets/img/basic.jpg");
    background-size: cover;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    .input_box{
      width: 280px;
    }
    .name_box{
      color: #04A7B3;
    }
  }
  .basic-container {
  padding: 10px 6px;
  box-sizing: border-box;
  overflow: auto;
  &--block {
    height: 100%;
    .basic-container__card {
      height: 100%;
    }
  }
  &__card {
    width: 100%;
  }
  &:first-child {
    padding-top: 0;
  }
}
</style>
