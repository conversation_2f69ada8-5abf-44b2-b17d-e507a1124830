<template>
  <div>
    <basic-container>
      <avue-form :option="option"
                 v-model="form"
                 @tab-click="handleTabClick"
                 @submit="handleSubmit"></avue-form>

      <avue-form :option="optionPassword"
                 v-model="formPassword"
                 @tab-click="handleTabClick"
                 @submit="changePassword"></avue-form>
    </basic-container>
  </div>
</template>

<script>
  import option from "@/option/user/info";
  import {getUserInfo, updateInfo, updatePassword} from "@/api/system/user";
  import md5 from 'js-md5';
  import func from "@/util/func";


  export default {
    data() {
      return {
        index: 0,
        option: option,
        form: {},
        optionPassword: {
          group: [
            {
              label: '修改密码',
              prop: 'password',
              column: [{
                label: '原密码',
                span: 12,
                row: true,
                type: 'password',
                prop: 'oldPassword'
              }, {
                label: '新密码',
                span: 12,
                row: true,
                type: 'password',
                prop: 'newPassword',
                rules: [{
                  required: true,
                  message: "请输入新密码",
                  trigger: "blur"
                }],
                tip: '密码需包含字母、数字、特殊字符中的至少两种，长度8-20位',
                regExp: /^(?![a-zA-Z]+$)(?!\d+$)(?![^\da-zA-Z]+$).{8,20}$/
              },
                {
                  label: '确认密码',
                  span: 12,
                  row: true,
                  type: 'password',
                  prop: 'newPassword1',
                  rules: [{
                    validator: (rule, value, callback) => {
                      if (value !== this.formPassword.newPassword) {
                        callback(new Error("两次输入密码不一致"));
                      } else {
                        callback();
                      }
                    },
                    trigger: 'blur'
                  }]
                }]
            }
          ]

        },
        formPassword: {}
      };
    },
    created() {
      this.handleWitch();
    },
    methods: {
      changePassword(form,done){
        // 新增密码强度校验
        const passwordRegex = /^(?![a-zA-Z]+$)(?!\d+$)(?![^\da-zA-Z]+$).{8,20}$/;
        if (!passwordRegex.test(form.newPassword)) {
          this.$message.error('密码需包含字母、数字、特殊字符中的至少两种，长度8-20位');
          return done();
        }
        updatePassword(md5(form.oldPassword), md5(form.newPassword), md5(form.newPassword1)).then(res => {
          if (res.data.success) {
            this.$message({
              type: "success",
              message: "修改密码成功!"
            });
          } else {
            this.$message({
              type: "error",
              message: res.data.msg
            });
          }
          done();
        }, error => {
          window.console.log(error);
          done();
        })
      },
      handleSubmit(form, done) {
        if (this.index === 0) {
          updateInfo(form).then(res => {
            if (res.data.success) {
              this.$message({
                type: "success",
                message: "修改信息成功!"
              });
            } else {
              this.$message({
                type: "error",
                message: res.data.msg
              });
            }
            done();
          }, error => {
            window.console.log(error);
            done();
          })
        } else {

        }
      },
      handleWitch() {
        if (this.index === 0) {
          getUserInfo().then(res => {
            const user = res.data.data;
            this.form = {
              id: user.id,
              avatar: user.avatar,
              name: user.name,
              realName: user.realName,
              phone: user.phone,
              email: user.email,
            }
          });
        }
      },
      handleTabClick(tabs) {
        this.index = func.toInt(tabs.index);
        this.handleWitch();
      }
    }
  };
</script>

<style>
/* 在style标签中添加 */
.avue-form__tip {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}
.avue-form__error {
  color: #ff4d4f;
}
</style>
