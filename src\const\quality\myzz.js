export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  addBtn: false,
  delBtn: false,
  viewBtn: true,
  dialogClickModal: false,
  labelWidth: 120,
  searchLabelWidth:100,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  align: 'center',
  menu: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "样品编号",
      prop: "样品编号",
      type: "input",
    },
    {
      label: "样品名称",
      prop: "样品名称",
      type: "input",
    },
    {
      label: "待测项目",
      prop: "待测项目",
      type: "input",
    },
    {
      label: "复溶加水量",
      prop: "复溶加水量",
      type: "input",
    },
    {
      label: "备注",
      prop: "备注",
      type: "input",
    },
    // {
    //   label: "样品名称",
    //   prop: "ypmc",
    //   type: "input",
    // },
    // {
    //   label: "检测方法",
    //   prop: "jcff",
    //   type: "input",
    // },
    // {
    //   label: "考核制样数量",
    //   prop: "sl",
    //   type: "input",
    // },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
