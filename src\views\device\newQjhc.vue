<template>
  <basic-container back>

    <el-tabs type="border-card" style="margin-bottom: 20px">
      <el-tab-pane label="移液器">
        <div class="card_title">基本信息</div>
        <el-form style="width: 1800px" label-position="top" :inline="true" size="small" label-width="80px"
                 :model="yyqhc">
<!--          <el-form-item label="所属三级任务">-->
<!--            <el-input style="width: 700px" v-model="yyqhc.sjrw"></el-input>-->
<!--          </el-form-item>-->
          <el-form-item label="期间核查日期">
            <el-date-picker
              style="width: 700px" v-model="yyqhc.qjhcrq"
              type="date" value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="设备名称">
            <el-select style="width: 700px" @change="changeDevice" v-model="yyqhc.sbid" placeholder="请选择">
              <el-option
                v-for="item in yyqList"
                :key="item.id"
                :label="item.sbmc"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
<!--          <el-form-item label="设备编号">-->
<!--            <el-input  style="width: 700px" v-model="yyqhc.sbbh"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="规格型号">-->
<!--            <el-input  style="width: 700px" v-model="yyqhc.ggxh"></el-input>-->
<!--          </el-form-item>-->
          <el-form-item label="期间核查依据">
            <el-input  style="width: 700px" v-model="yyqhc.zcfg"></el-input>
          </el-form-item>
          <el-form-item label="核查用电子天平编号">
            <el-input style="width: 700px" v-model="yyqhc.dztpbh"></el-input>
          </el-form-item>
          <el-form-item label="电子天平校准日期">
            <el-date-picker
              style="width: 700px" v-model="yyqhc.dztpxzsj"
              type="date" value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="电子天平精度">
            <el-input style="width: 700px" v-model="yyqhc.dztpjd"></el-input>
          </el-form-item>
          <el-form-item label="环境温度℃">
            <el-input style="width: 700px" v-model="yyqhc.hjwd"></el-input>
          </el-form-item>
          <el-form-item label="环境湿度RH">
            <el-input style="width: 700px" v-model="yyqhc.hjsd"></el-input>
          </el-form-item>
        </el-form>
        <div class="card_title">外观检测记录</div>
        <div class="out_box">经目测和触摸观察，表面平整，无废边裂纹，活塞上下自动灵活等，设备标示清晰可见，外观检查合格。</div>
        <div class="card_title">称量记录</div>
        <div class="info_title">称量记录（1）</div>
        <table>
          <thead>
          <tr>
            <th>序列</th>
            <th>核查点/ul</th>
            <th>质量值/mg</th>
            <th>温度/C</th>
            <th>K(T)值/cm3/g</th>
            <th>V20实际容积值/ul</th>
            <th>平均值/ul</th>
            <th>测试相对误差/%</th>
            <th>允许误差</th>
            <th>计算标准偏差</th>
            <th>测试重复性/%</th>
            <th>要求重复性/%</th>
            <th>单项核查结论（1）</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in infoOne" :key="index">
            <td>{{index + 1}}</td>
            <td>
              <el-input size="mini"  v-model="item.hcd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.zlz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.ktz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.vsj" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.pjz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.csxdwc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.yxwc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.jsbzpc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.cscfx" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.yqcfx" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.dxhcjl" placeholder="请输入"></el-input>
            </td>

          </tr>
          </tbody>
        </table>
        <div class="info_title">称量记录（2）</div>
        <table>
          <thead>
          <tr>
            <th>序列</th>
            <th>核查点/ul</th>
            <th>质量值/mg</th>
            <th>温度/C</th>
            <th>K(T)值/cm3/g</th>
            <th>V20实际容积值/ul</th>
            <th>平均值/ul</th>
            <th>测试相对误差/%</th>
            <th>允许误差</th>
            <th>计算标准偏差</th>
            <th>测试重复性/%</th>
            <th>要求重复性/%</th>
            <th>单项核查结论（2）</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in infoTwo" :key="index">
            <td>{{index + 1}}</td>
            <td>
              <el-input size="mini" v-model="item.hcd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.zlz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.ktz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.vsj" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.pjz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.csxdwc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.yxwc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.jsbzpc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.cscfx" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.yqcfx" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.dxhcjl" placeholder="请输入"></el-input>
            </td>

          </tr>
          </tbody>
        </table>
        <div class="info_title">称量记录（3）</div>
        <table>
          <thead>
          <tr>
            <th>序列</th>
            <th>核查点/ul</th>
            <th>质量值/mg</th>
            <th>温度/C</th>
            <th>K(T)值/cm3/g</th>
            <th>V20实际容积值/ul</th>
            <th>平均值/ul</th>
            <th>测试相对误差/%</th>
            <th>允许误差</th>
            <th>计算标准偏差</th>
            <th>测试重复性/%</th>
            <th>要求重复性/%</th>
            <th>单项核查结论（3）</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in infoThree" :key="index">
            <td>{{index + 1}}</td>
            <td>
              <el-input size="mini" v-model="item.hcd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.zlz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.ktz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.vsj" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.pjz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.csxdwc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.yxwc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.jsbzpc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.cscfx" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.yqcfx" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.dxhcjl" placeholder="请输入"></el-input>
            </td>
          </tr>
          </tbody>
        </table>
        <div style="margin: 15px 0">
          核查结论
          <el-select style="margin-left: 10px" v-model="yyqhc.hcjl" placeholder="请选择" size="small">
            <el-option label="合格" value="合格"></el-option>
            <el-option label="不合格" value="不合格"></el-option>
          </el-select>
        </div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div style="margin: 10px 0">提交人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload">
              <img v-if="yyqhc.tjrqm" :src="yyqhc.tjrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">核查人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="handleAvatarSuccess2"
              :before-upload="beforeAvatarUpload">
              <img v-if="yyqhc.hcrqm" :src="yyqhc.hcrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">审核人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="handleAvatarSuccess3"
              :before-upload="beforeAvatarUpload">
              <img v-if="yyqhc.shrqm" :src="yyqhc.shrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <div class="btn_box">
          <el-button size="small" @click="saveYyq(1)">保存</el-button>
          <el-button style="margin-left: 20px" size="small" type="primary" @click="saveYyq(0)">提交</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane label="电子天平">
        <div class="card_title">基本信息</div>
        <el-form style="width: 1800px" label-position="top" :inline="true" size="small" label-width="80px"
                 :model="dztphc">
          <el-form-item label="期间核查日期">
            <el-date-picker
              style="width: 700px" v-model="dztphc.qjhcrq"
              type="date" value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="设备名称">
            <el-select style="width: 700px" v-model="dztphc.sbid" placeholder="请选择">
              <el-option
                v-for="item in dztpList"
                :key="item.id"
                :label="item.sbmc"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
<!--          <el-form-item label="设备编号">-->
<!--            <el-input  style="width: 700px" v-model="dztphc.sbbh"></el-input>-->
<!--          </el-form-item>-->
          <el-form-item label="规格型号">
            <el-input  style="width: 700px" v-model="dztphc.name"></el-input>
          </el-form-item>
          <el-form-item label="期间核查依据">
            <el-input  style="width: 700px" v-model="dztphc.zcfg"></el-input>
          </el-form-item>
          <el-form-item label="核查用砝码设备编号">
            <el-input style="width: 700px" v-model="dztphc.fmbh"></el-input>
          </el-form-item>
          <el-form-item label="砝码检定证书编号">
            <el-input style="width: 700px" v-model="dztphc.fmjdzsbh"></el-input>
          </el-form-item>
          <el-form-item label="砝码检定日期">
            <el-date-picker
              style="width: 700px" v-model="dztphc.fmjdrq"
              type="date" value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="环境温度℃">
            <el-input style="width: 700px" v-model="dztphc.hjwd"></el-input>
          </el-form-item>
          <el-form-item label="环境湿度RH">
            <el-input style="width: 700px" v-model="dztphc.hjsd"></el-input>
          </el-form-item>
        </el-form>
        <div class="card_title">外观检测记录</div>
        <el-radio-group v-model="dztphc.wgjcjl" style="margin-top: 15px">
          <el-radio label="可以正常使用，外观符合要求">可以正常使用，外观符合要求</el-radio>
          <el-radio label="外观不符合要求">外观不符合要求</el-radio>
        </el-radio-group>
        <div class="card_title">称量记录（每个核查点记录1次）</div>
        <div class="info_title">称量核查</div>
        <table>
          <thead>
          <tr>
            <th>序列</th>
            <th>核查点/g</th>
            <th>称量核查点</th>
            <th>天平读数（第1次）/g</th>
            <th>天平读数（第2次）/g</th>
            <th>天平读数（第3次）/g</th>
            <th>天平读数（第4次）/g</th>
            <th>平均值/g</th>
            <th>误差/g</th>
            <th>允许误差/g</th>
            <th>内部校准结果</th>
            <th>允差校验</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in clhc" :key="index">
            <td>{{index + 1}}</td>
            <td>
              <el-select size="mini" v-model="item.hcd" placeholder="请选择">
                <el-option label="0" value="0"></el-option>
                <el-option label="2" value="2"></el-option>
                <el-option label="3" value="3"></el-option>
                <el-option label="5" value="5"></el-option>
                <el-option label="50" value="50"></el-option>
                <el-option label="100" value="100"></el-option>
                <el-option label="200" value="200"></el-option>
              </el-select>
            </td>
            <td>
              <el-input size="mini" v-model="item.clhcd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds1" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds2" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds3" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds4" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.pjz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.yxwc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.nbxzjg" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.ycxy" placeholder="请输入"></el-input>
            </td>
          </tr>
          </tbody>
        </table>
        <div class="info_title">偏载核验</div>
        <img src="../../assets/img/dztp.jpg" style="width: 500px" alt="">
        <div>
          <el-radio-group v-model="pzhy.type" style="margin-bottom: 15px">
            <el-radio :label="3">方形偏载校准位置</el-radio>
            <el-radio :label="6">圆形偏载校准位置</el-radio>
            <el-radio :label="9">三角形偏载校准位置</el-radio>
          </el-radio-group>
        </div>
        <table>
          <thead>
          <tr>
            <th>序列</th>
            <th>偏载校准点/g</th>
            <th>偏载核查点</th>
            <th>天平读数（第1次）/g</th>
            <th>天平读数（第2次）/g</th>
            <th>天平读数（第3次）/g</th>
            <th>天平读数（第4次）/g</th>
            <th>平均值/g</th>
            <th>误差/g</th>
            <th>允许误差/g</th>
            <th>内部校准结果</th>
            <th>允差校验</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in pzhy" :key="index">
            <td>{{index + 1}}</td>
            <td>
              <el-input size="mini" v-model="item.hcd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.clhcd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds1" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds2" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds3" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds4" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.pjz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.yxwc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.nbxzjg" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.ycxy" placeholder="请输入"></el-input>
            </td>
          </tr>
          </tbody>
        </table>
        <div class="info_title">重复性核查（只核查一次）</div>
        <table>
          <thead>
          <tr>
            <th>序列</th>
            <th>重复性校准点/g</th>
            <th>重复性核查点</th>
            <th>天平读数（第1次）/g</th>
            <th>天平读数（第2次）/g</th>
            <th>天平读数（第3次）/g</th>
            <th>天平读数（第4次）/g</th>
            <th>平均值/g</th>
            <th>误差/g</th>
            <th>允许误差/g</th>
            <th>内部校准结果</th>
            <th>允差校验</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in cfxhc" :key="index">
            <td>{{index + 1}}</td>
            <td>
              <el-input size="mini" v-model="item.hcd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.clhcd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds1" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds2" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds3" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.tpds4" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.pjz" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.yxwc" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.nbxzjg" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.ycxy" placeholder="请输入"></el-input>
            </td>
          </tr>
          </tbody>
        </table>
        <div style="margin: 15px 0">
          核查结论
          <el-select style="margin-left: 10px" v-model="yyqhc.hcjl" placeholder="请选择" size="small">
            <el-option label="合格" value="合格"></el-option>
            <el-option label="不合格" value="不合格"></el-option>
          </el-select>
        </div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div style="margin: 10px 0">提交人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="uploadDztp1"
              :before-upload="beforeAvatarUpload">
              <img v-if="dztphc.tjrqm" :src="dztphc.tjrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">核查人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="uploadDztp2"
              :before-upload="beforeAvatarUpload">
              <img v-if="dztphc.hcrqm" :src="dztphc.hcrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">审核人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="uploadDztp3"
              :before-upload="beforeAvatarUpload">
              <img v-if="dztphc.shrqm" :src="dztphc.shrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <div class="btn_box">
          <el-button size="small" @click="saveDztp(1)">保存</el-button>
          <el-button style="margin-left: 20px" size="small" type="primary" @click="saveDztp(0)">提交</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane label="水浴锅">
        <div class="card_title">基本信息</div>
        <el-form style="width: 1800px" label-position="top" :inline="true" size="small" label-width="80px"
                 :model="syghc">
          <el-form-item label="期间核查日期">
            <el-date-picker
              style="width: 700px" v-model="syghc.qjhcrq"
              type="date" value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="设备名称">
            <el-select style="width: 700px" v-model="syghc.sbid" placeholder="请选择">
              <el-option
                v-for="item in sygList"
                :key="item.id"
                :label="item.sbmc"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备编号">
            <el-input  style="width: 700px" v-model="syghc.sbbh"></el-input>
          </el-form-item>
          <el-form-item label="期间核查依据">
            <el-input  style="width: 700px" v-model="syghc.zcfg"></el-input>
          </el-form-item>
          <el-form-item label="核查用温度计编号">
            <el-input style="width: 700px" v-model="syghc.wdjbh"></el-input>
          </el-form-item>
          <el-form-item label="温度计校准证书编号">
            <el-input style="width: 700px" v-model="syghc.fmjdzsbh"></el-input>
          </el-form-item>
          <el-form-item label="温度计校准日期">
            <el-date-picker
              style="width: 700px" v-model="syghc.wdjxzrq"
              type="date" value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="环境温度℃">
            <el-input style="width: 700px" v-model="syghc.hjwd"></el-input>
          </el-form-item>
          <el-form-item label="环境湿度RH">
            <el-input style="width: 700px" v-model="syghc.hjsd"></el-input>
          </el-form-item>
        </el-form>
        <div class="card_title">外观检测记录</div>
        <el-radio-group v-model="syghc.wgjcjl" style="margin-top: 15px">
          <el-radio label="可以正常使用，外观符合要求">可以正常使用，外观符合要求</el-radio>
          <el-radio label="外观不符合要求">外观不符合要求</el-radio>
        </el-radio-group>
        <div class="card_title">位点图</div>
        <img src="../../assets/img/syg.jpg" style="width: 300px" alt="">
        <div>水浴锅/恒温培养箱的可接受温度波动度和均匀度是+-2C</div>
        <div class="card_title">上层期间核查记录</div>
        <table style="margin-top: 15px">
          <thead>
          <tr>
            <th>序列</th>
            <th>第1次实测温度值/C</th>
            <th>第2次实测温度值/C</th>
            <th>第3次实测温度值/C</th>
            <th>上层温度波动度/C</th>
            <th>上层温度均匀度/C</th>
            <th>核查结论</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in schc" :key="index">
            <td>{{index + 1}}</td>
            <td>
              <el-input size="mini" v-model="item.wd1" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wd2" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wd3" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wdbdd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wdjyd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.hcjl" placeholder="请输入"></el-input>
            </td>
          </tr>
          </tbody>
        </table>
        <div class="card_title">下层期间核查记录</div>
        <table style="margin-top: 15px">
          <thead>
          <tr>
            <th>序列</th>
            <th>第1次实测温度值/C</th>
            <th>第2次实测温度值/C</th>
            <th>第3次实测温度值/C</th>
            <th>下层温度波动度/C</th>
            <th>下层温度均匀度/C</th>
            <th>下层核查结论</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in xchc" :key="index">
            <td>{{index + 1}}</td>
            <td>
              <el-input size="mini" v-model="item.wd1" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wd2" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wd3" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wdbdd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.wdjyd" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.hcjl" placeholder="请输入"></el-input>
            </td>
          </tr>
          </tbody>
        </table>
        <div style="margin: 15px 0">
          核查结论
          <el-select style="margin-left: 10px" v-model="syghc.hcjl" placeholder="请选择" size="small">
            <el-option label="合格" value="合格"></el-option>
            <el-option label="不合格" value="不合格"></el-option>
          </el-select>
        </div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div style="margin: 10px 0">提交人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="uploadSyg1"
              :before-upload="beforeAvatarUpload">
              <img v-if="syghc.tjrqm" :src="syghc.tjrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">核查人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="uploadSyg2"
              :before-upload="beforeAvatarUpload">
              <img v-if="syghc.hcrqm" :src="syghc.hcrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">审核人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="uploadSyg3"
              :before-upload="beforeAvatarUpload">
              <img v-if="syghc.shrqm" :src="syghc.shrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <div class="btn_box">
          <el-button size="small" @click="saveSyg(1)">保存</el-button>
          <el-button style="margin-left: 20px" size="small" type="primary" @click="saveSyg(0)">提交</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane label="分光光度计">
        <div class="card_title">基本信息</div>
        <el-form style="width: 1800px" label-position="top" :inline="true" size="small" label-width="80px"
                 :model="fggdjhc">
          <el-form-item label="期间核查日期">
            <el-date-picker
              style="width: 700px" v-model="fggdjhc.qjhcrq"
              type="date" value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="设备名称">
            <el-select style="width: 700px" v-model="fggdjhc.sbid" placeholder="请选择">
              <el-option
                v-for="item in gdjList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备编号">
            <el-input  style="width: 700px" v-model="fggdjhc.sbbh"></el-input>
          </el-form-item>
          <el-form-item label="期间核查依据">
            <el-input  style="width: 700px" v-model="fggdjhc.zcfg"></el-input>
          </el-form-item>
          <el-form-item label="工作液申请日期">
            <el-date-picker
              style="width: 700px" v-model="fggdjhc.gzysqrq"
              type="date" value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="检测项目">
            <el-input style="width: 700px" v-model="fggdjhc.jcxm"></el-input>
            <!--            <el-select style="width: 700px" v-model="fggdjhc.jcxm" placeholder="请选择">-->
<!--              <el-option-->
<!--                v-for="item in deviceList"-->
<!--                :key="item.value"-->
<!--                :label="item.label"-->
<!--                :value="item.value">-->
<!--              </el-option>-->
<!--            </el-select>-->
          </el-form-item>
          <el-form-item label="标准储备液浓度（mg/L）">
            <el-input style="width: 700px" v-model="fggdjhc.bzcbynd"></el-input>
          </el-form-item>
          <el-form-item label="标准储备液编号">
            <el-input style="width: 700px" v-model="fggdjhc.bzcbybh"></el-input>
          </el-form-item>
          <el-form-item label="工作液浓度">
            <el-input style="width: 700px" v-model="fggdjhc.gzynd"></el-input>
          </el-form-item>
          <el-form-item label="标准物质CAS号">
            <el-input style="width: 700px" v-model="fggdjhc.bzwz"></el-input>
          </el-form-item>
          <el-form-item label="环境温度℃">
            <el-input style="width: 700px" v-model="fggdjhc.hjwd"></el-input>
          </el-form-item>
          <el-form-item label="环境湿度RH">
            <el-input style="width: 700px" v-model="fggdjhc.hjsd"></el-input>
          </el-form-item>
        </el-form>
        <div class="card_title">外观检测记录</div>
        <el-radio-group v-model="fggdjhc.wgjcjl" style="margin-top: 15px">
          <el-radio label="可以正常使用，外观符合要求">可以正常使用，外观符合要求</el-radio>
          <el-radio label="外观不符合要求">外观不符合要求</el-radio>
        </el-radio-group>
        <div class="card_title">分光光度计核查记录（每个加标倍数记录2次）</div>
        <table style="margin-top: 15px">
          <thead>
          <tr>
            <th>序列</th>
            <th>加标倍数</th>
            <th>加标项目</th>
            <th>0.5倍加标量</th>
            <th>1倍加标量</th>
            <th>加标量/ul</th>
            <th>检测结果（抑制率）</th>
            <th>允差范围</th>
            <th>允差校验/C</th>
            <th>核查结论</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in hcjl" :key="index">
            <td>{{index + 1}}</td>

            <td>
              <el-input size="mini" v-model="item.jbbs" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.jbxm" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.hjbl" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.djbl" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.jbl" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.jcjg" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.ycfw" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.ycxy" placeholder="请输入"></el-input>
            </td>
            <td>
              <el-input size="mini" v-model="item.hcjgpd" placeholder="请输入"></el-input>
            </td>
          </tr>
          </tbody>
        </table>
        <div style="margin: 15px 0">
          核查结论
          <el-select style="margin-left: 10px" v-model="fggdjhc.hcjl" placeholder="请选择" size="small">
            <el-option label="合格" value="合格"></el-option>
            <el-option label="不合格" value="不合格"></el-option>
          </el-select>
        </div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div style="margin: 10px 0">提交人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="uploadGdj1"
              :before-upload="beforeAvatarUpload">
              <img v-if="fggdjhc.tjrqm" :src="fggdjhc.tjrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">核查人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="uploadGdj2">
              <img v-if="fggdjhc.hcrqm" :src="fggdjhc.hcrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
          <el-col :span="6">
            <div style="margin: 10px 0">审核人签名</div>
            <el-upload
              class="avatar-uploader"
              action="/api/manage/put-object"
              :show-file-list="false"
              :on-success="uploadGdj3">
              <img v-if="fggdjhc.shrqm" :src="fggdjhc.shrqm" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <div class="btn_box">
          <el-button size="small">保存</el-button>
          <el-button style="margin-left: 20px" size="small" type="primary" @click="saveGdj(1)">提交</el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
  </basic-container>
</template>

<script>
  import {getDeviceList, yyqSubmit, dztpSubmit, sygSubmit, fggdjSubmit} from "@/api/device/newQjhc";
  export default {
    name: "newQjhc",
    data() {
      return {
        yyqList:[],
        dztpList:[],
        sygList:[],
        gdjList:[],
        deviceList:[],
        yyqhc: {

        },
        infoOne: [
          {
            jlgs: 1,sort:1
          },
          {jlgs: 1,sort:2},
          {jlgs: 1,sort:3},
          {jlgs: 1,sort:4},
          {jlgs: 1,sort:5},
          {jlgs: 1,sort:6},
        ],
        infoTwo:[
          {jlgs: 2,sort:1},
          {jlgs: 2,sort:2},
          {jlgs: 2,sort:3},
          {jlgs: 2,sort:4},
          {jlgs: 2,sort:5},
          {jlgs: 2,sort:6},
        ],
        infoThree: [
          {jlgs: 3,sort:1},
          {jlgs: 3,sort:2},
          {jlgs: 3,sort:3},
          {jlgs: 3,sort:4},
          {jlgs: 3,sort:5},
          {jlgs: 3,sort:6},
        ],
        dztphc:{},
        clhc:[{jlgs: 1,sort:1},{jlgs: 1,sort:2},{jlgs: 1,sort:3},{jlgs: 1,sort:4},{jlgs: 1,sort:5},{jlgs: 1,sort:6},{jlgs:1,sort:7}],
        pzhy:[{jlgs: 2,sort:1},{jlgs: 2,sort:2},{jlgs: 2,sort:2},{jlgs: 2,sort:4},{jlgs: 2,sort:5}],
        cfxhc: [{}],
        scqjhcjl: [{},{},{},{},{},{}],
        xcqjhcjl: [{},{},{},{},{},{}],
        fggdjhc:{},
        hcjl:[{},{},{},{},{},{}],
        syghc:{},
        schc:[{},{},{},{},{},{}],
        xchc:[{},{},{},{},{},{}],
      }
    },
    mounted() {
      this.onLoad()
    },
    methods:{
      saveYyq(e){
        yyqSubmit({
          infoOne: this.infoOne,
          infoThree: this.infoThree,
          infoTwo: this.infoTwo,
          yyqhc: {
            ...this.yyqhc,
            status: e
          },
        }).then(res=>{
          if(res.data.code == 200){
            this.$message.success(res.data.msg)
          }else{
            this.$message.error(res.data.msg)
          }
        })
      },
      saveDztp(e){
        dztpSubmit({
          dztphc: {
            ...this.dztphc,
            status: e
          },
          cfxhc: this.cfxhc,
          clhc: this.clhc,
          pzhy: this.pzhy,
        }).then(res=>{
          if(res.data.code == 200){
            this.$message.success(res.data.msg)
          }else{
            this.$message.error(res.data.msg)
          }
        })
      },
      saveSyg(e){
        sygSubmit({
          syghc: {
            ...this.syghc,
            status: e
          },
          cfxhc: this.cfxhc,
          clhc: this.clhc,
          pzhy: this.pzhy,
        }).then(res=>{
          if(res.data.code == 200){
            this.$message.success(res.data.msg)
          }else{
            this.$message.error(res.data.msg)
          }
        })
      },
      saveGdj(e){
        fggdjSubmit({
          fggdjhc: {
            ...this.fggdjhc,
            status: e
          },
          hcjl: this.hcjl,
        }).then(res=>{
          if(res.data.code == 200){
            this.$message.success(res.data.msg)
          }else{
            this.$message.error(res.data.msg)
          }
        })
      },
      changeDevice(e){
        console.log(33,e)
      },
      onLoad(){
        getDeviceList(1,100000,{
          bzyqmc: '1'
        }).then(res=>{
          this.yyqList = res.data.data.records
        })
        getDeviceList(1,100000,{
          bzyqmc: '2'
        }).then(res=>{
          this.dztpList = res.data.data.records
        })
        getDeviceList(1,100000,{
          bzyqmc: '3'
        }).then(res=>{
          this.syqList = res.data.data.records
        })
        getDeviceList(1,100000,{
          bzyqmc: '4'
        }).then(res=>{
          this.gdjList = res.data.data.records
        })
      },
      handleAvatarSuccess3(res, file) {
        this.yyqhc = {
          ...this.yyqhc,
          shrqm: res.data
        };
      },
      handleAvatarSuccess2(res, file) {
        this.yyqhc = {
          ...this.yyqhc,
          hcrqm: res.data
        };
      },
      handleAvatarSuccess(res, file) {
        this.yyqhc = {
          ...this.yyqhc,
          tjrqm: res.data
        };
      },

      uploadGdj1(res, file){
        this.fggdjhc = {
          ...this.fggdjhc,
          tjrqm: res.data
        };
      },
      uploadGdj2(res, file){
        this.fggdjhc = {
          ...this.fggdjhc,
          hcrqm: res.data
        };
      },
      uploadGdj3(res, file){
        this.fggdjhc = {
          ...this.fggdjhc,
          shrqm: res.data
        };
      },

      uploadSyg1(res, file){
        this.syghc = {
          ...this.syghc,
          tjrqm: res.data
        };
      },
      uploadSyg2(res, file){
        this.syghc = {
          ...this.syghc,
          hcrqm: res.data
        };
      },
      uploadSyg3(res, file){
        this.syghc = {
          ...this.syghc,
          shrqm: res.data
        };
      },
      uploadDztp1(res, file){
        this.dztphc = {
          ...this.dztphc,
          tjrqm: res.data
        };
      },
      uploadDztp2(res, file){
        this.dztphc = {
          ...this.dztphc,
          hcrqm: res.data
        };
      },
      uploadDztp3(res, file){
        this.dztphc = {
          ...this.dztphc,
          shrqm: res.data
        };
      },
      beforeAvatarUpload(file) {
        // const isLt2M = file.size / 1024 / 1024 < 2;
        // if (!isLt2M) {
        //   this.$message.error('上传头像图片大小不能超过 2MB!');
        // }
        // return isJPG && isLt2M;
        return true
      }
    }
  }
</script>

<style>
  .avatar-uploader .el-upload {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 24px;
    color: #8c939d;
    width: 128px;
    height: 128px;
    line-height: 128px;
    text-align: center;
  }
  .avatar {
    width: 128px;
    height: 128px;
    display: block;
  }
</style>

<style scoped lang="less">

  .btn_box{
    width: 100%;
    text-align: right;
    margin-bottom: 50px;
  }

  .info_title{
    padding: 15px 0;

  }
  .card_title{
    margin-top: 15px;
    padding-bottom: 15px;
    border-bottom: 2px dotted #ccc;
  }
  table,
  td,
  th {
    text-align: center;
    border: 1px solid #ccc;
    border-collapse: collapse;
  }

  table td {
    padding: 5px;
  }

  .out_box {
    padding: 15px;
    border: 1px solid #ccc;
    margin-top: 15px;
  }
</style>
