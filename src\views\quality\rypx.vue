<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.rypx_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template slot-scope="{row}" slot="pxzp">
        <el-upload
          v-if="!row.pxzp"
          class="upload-demo"
          action="/api/manage/put-object"
          :on-success="uploadSuccess"
          :limit="1">
          <el-button size="small" type="primary" @click="changeFile(row)">点击上传</el-button>
        </el-upload>
        <span v-else>
          <a class="down_html" v-for="(item,index) in row.pxzp.split(',')" :href="item" :key="index">下载文件{{index+1}}</a>
        </span>
      </template>
      <template slot-scope="{row}" slot="cpmd">
        <el-button type="primary" size="small" @click="showPeople(row)">查看</el-button>
      </template>
<!--      <template slot-scope="{row}" slot="detail">-->
<!--        <el-button type="primary" size="mini" @click="showDetail(row)">查看</el-button>-->
<!--      </template>-->
    </avue-crud>

    <el-dialog title="人员名单" :visible.sync="peopleShow" append-to-body>
      <avue-crud :option="peoOption"
                 :table-loading="peoloading"
                 :data="peoData"
                 :page.sync="peoPage"
                 v-model="peoForm"
                 ref="crud"
                 @current-change="currentPeoChange"
                 @size-change="sizePeoChange"
                 @on-load="onPeoLoad">
        <template slot-scope="{row}" slot="khjg">
          <el-input placeholder="请输入内容" size="mini" v-model="row.khjg">
            <template slot="append">
              <el-button type="primary" size="mini" @click="save(row)">保存</el-button>
            </template>
          </el-input>
        </template>
      </avue-crud>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/quality/rypx";
  import {getPeoList, upPeoDate} from "@/api/quality/rypxmd";

  import option from "@/const/quality/rypx";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {
          wczt:'未完成',
        },
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        currentId: null,

        peoloading:true,
        peopleShow: false,
        peoPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        peoForm: {},
        peoData: [],
        peoOption:{
          height:'500px',
          calcHeight: 30,
          tip: false,
          searchShow: false,
          searchMenuSpan: 6,
          border: true,
          index: false,
          addBtn: false,
          viewBtn: false,
          selection: false,
          dialogClickModal: false,
          labelWidth: 120,
          align: 'center',
          menu: false,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "参培人员",
              prop: "userid",
              type: "select",
              dicUrl: "/api/blade-user/page?current=1&size=100000&deptId=",
              props: {
                label: "name",
                value: "id",
                res: 'data.records'
              },
            },
            {
              label: "培训课程",
              prop: "pxid",
              type: "select",
              dicUrl: "/api/zlgl/rypx/page?current=1&size=100000",
              props: {
                label: "pxkc",
                value: "id",
                res: 'data.records'
              },
            },
            {
              label: "考核结果",
              prop: "khjg",
              type: "input",
            },
            {
              label: "租户ID",
              prop: "tenantId",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "创建人",
              prop: "createUser",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "创建部门",
              prop: "createDept",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "修改人",
              prop: "updateUser",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "修改时间",
              prop: "updateTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "状态(0:关闭 1:开启)",
              prop: "status",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "是否已删除(0:正常 1:删除)",
              prop: "isDeleted",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
          ]
        }
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.rypx_add, false),
          viewBtn: this.vaildData(this.permission.rypx_view, false),
          delBtn: this.vaildData(this.permission.rypx_delete, false),
          editBtn: this.vaildData(this.permission.rypx_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    mounted() {

      window.__rypx_component__ = this;
    },
    beforeDestroy() {
      delete window.__rypx_component__;
    },
    methods: {
      setColumnValue(val,prop) {
        this.form[prop] = val;
        // // 如果需要强制更新视图
        this.$set(this.form, prop, val);
      },
      changeFile(e){
        this.currentId = e.id
      },
      uploadSuccess(res, file, fileList){
        const row = {
          id: this.currentId,
          pxzp: res.data,
          wczt: '完成'
        }
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          console.log(error);
        });
      },
      save(e){
        upPeoDate({
          id: e.id,
          khjg: e.khjg
        }).then(res=>{
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        })
      },
      showPeople(e){
        this.peopleShow = true
        this.onPeoLoad(this.peoPage,{
          pxid: e.id
        })
      },
      rowSave(row, done, loading) {

        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {

        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      currentPeoChange(currentPage){
        this.peoPage.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      sizePeoChange(pageSize){
        this.peoPage.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onPeoLoad(peoPage, params = {}){
        this.peoloading = true;
        getPeoList(peoPage.currentPage, peoPage.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.peoPage.total = data.total;
          this.peoData = data.records;
          this.peoloading = false;
        });
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
  .down_html{
    display: block;
  }
  .down_html:hover{
    color: #04A7B3;
  }
</style>
