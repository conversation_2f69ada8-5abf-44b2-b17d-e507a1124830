export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchShow: true,
  searchMenuSpan:6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  columnBtn: false,
  align: 'center',
  column: [
    {
      label: "商户id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "所属任务",
      prop: "ssrwid",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      parent:false,
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      search: true,
      width: 100,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "受检单位",
      prop: "sjdw",
      type: "tree",
      filterable: true,
      dicUrl: "/api/sh/sczt/list",
      props: {
        label: "scmc",
        value: "id",
        res: 'data.records'
      },
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      nodeClick: (data, node, nodeComp) => {
        if (window.__scsy_component__) {
          window.__scsy_component__.handleNodeClick(data, 'scmc','sjdwmc');
        }
      },
    },
    {
      label: "受检单位名称",
      prop: "sjdwmc",
      type: "input",
      display: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "商品名称",
      prop: "spmc",
      type: "tree",
      filterable: true,
      dicUrl: "/api/ypgl/ypxx/list?current=1&size=9999",
      props: {
        label: "ypmc",
        value: "ypmc",
        res: 'data.records'
      },
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      search: true
    },
    {
      label: "经营户",
      prop: "jyhid",
      type: "tree",
      filterable: true,
      dicUrl: "/api/sh/jyh/page?size=99999&current=1",
      props: {
        label: "jyhmc",
        value: "id",
        res: 'data.records'
      },
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      nodeClick: (data, node, nodeComp) => {
        if (window.__scsy_component__) {
          window.__scsy_component__.handleNodeClick(data,'twh','twh');
        }
      },
    },
    {
      label: "摊位号",
      prop: "twh",
      type: "input",
    },
    {
      label: "销售单号",
      prop: "xsdh",
      type: "input",
    },
    {
      label: "销售日期",
      prop: "xsrq",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      searchSpan:6,
      search: true,
      searchRange:true,
    },
    {
      label: "销售数量",
      prop: "xssl",
      type: "input",
    },
    {
      label: "购货商",
      prop: "ghs",
      type: "input",
    },
    {
      label: "地址",
      prop: "dz",
      type: "input",
    },
    {
      label: "上传日期",
      prop: "scri",
      type: "datetime",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      searchSpan:6,
      search: true,
      searchRange:true,
    },
    {
      label: "凭证",
      prop: "pz",
      type: 'upload',
      listType: 'picture-card',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: false,
      hide: true,
      multiple: true,
    },

    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
