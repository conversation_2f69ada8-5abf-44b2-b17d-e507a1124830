<template>
  <basic-container>
    <el-row :gutter="20">
      <div class="chart_title_box">
        <span>食品质量安全指数</span>
        <el-select size="mini" v-model="day" placeholder="请选择" onchange="selectChange">
          <el-option label="日" :value="1"></el-option>
          <el-option label="周" :value="2"></el-option>
          <el-option label="月" :value="3"></el-option>
        </el-select>
      </div>
      <el-row :gutter="20">
        <el-col :span="4" v-for="(item,index) in chartList" :key="index">
          <div class="chart_box" ></div>
        </el-col>
      </el-row>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="chart_title_box">
          <span>检测量统计</span>
          <el-select size="mini" v-model="numType" placeholder="请选择" @change="changeNumber">
            <el-option label="日" :value="1"></el-option>
            <el-option label="周" :value="2"></el-option>
            <el-option label="月" :value="3"></el-option>
          </el-select>
        </div>
        <el-table v-loading="loading" :data="tableData" height="600" border style="width: 100%">
          <el-table-column prop="kjsmc" label="检测室"></el-table-column>
          <el-table-column prop="yps" label="样品数"></el-table-column>
          <el-table-column prop="jls" label="检测数"></el-table-column>
        </el-table>
      </el-col>
      <!-- <el-col :span="10">
        <div class="chart_title_box">
          <span>主要检测项目占比分析</span>
          <el-select size="mini" v-model="day" placeholder="请选择">
            <el-option label="日" :value="1"></el-option>
            <el-option label="周" :value="2"></el-option>
            <el-option label="月" :value="3"></el-option>
          </el-select>
        </div>
        <div id="pie_box1"></div>
        <div class="chart_title_box" style="margin-top: 10px">
          <span>不同市场类型占比分析</span>
          <el-select size="mini" v-model="day" placeholder="请选择">
            <el-option label="日" :value="1"></el-option>
            <el-option label="周" :value="2"></el-option>
            <el-option label="月" :value="3"></el-option>
          </el-select>
        </div>
        <div id="pie_box2"></div>
      </el-col> -->
    </el-row>

  </basic-container>
</template>

<script>
import { spzlaq, jcltj } from "@/api/check/home";
export default {
  name: "chart",
  data() {
    return {
      chartList: [],
      day: 3,
      numType: 3,
      tableData: [],
      loading: false,
    }
  },
  async mounted() {
    this.getspzlaq(this.day);
    this.getCheckNumber(this.numType)
    this.initNumChart2()
    this.initNumChart3()
    this.initNumChart4()
    this.initNumChart5()
  },
  methods: {
    changeNumber(e){
      this.getCheckNumber(e)
    },
    selectChange(value){
      this.getspzlaq(value);
    },
    getCheckNumber(type){
      this.loading = true
      jcltj(type).then(({data: resultData}) => {
        this.loading = false
        if (resultData.code == 200) {
          let { data } = resultData;
          this.tableData = data
        }
      })
    },
    getspzlaq(type) {
      spzlaq(type).then(({ data: resultData }) => {
        if (resultData.code == 200) {
          let { data } = resultData;
          this.chartList = data;

          this.$nextTick(()=>{
            this.initNumChart1(data)
          })
        }
      })
    },
    initNumChart1(data) {

      let chartDom = document.getElementsByClassName('chart_box'); // 对应地使用ByClassName
      console.log(chartDom);
      for (var i = 0; i < chartDom.length; i++) { // 通过for循环，在相同class的dom内绘制元素

        const myChart = this.echarts.init(chartDom[i]);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          graphic: [{ //环形图中间添加文字
            type: 'text', //通过不同top值可以设置上下显示
            left: 'center',
            top: 'center',
            style: {
              text: data[i].jcxm,
              textAlign: 'center',
              fill: '#000', //文字的颜色
              fontSize: 12,
              lineHeight: 16,
            }
          }, { //环形图中间添加文字
            type: 'text', //通过不同top值可以设置上下显示
            left: 'center',
            top: '92%',
            style: {
              text: `${data[i].yxsl}/${data[i].zl}次`,
              textAlign: 'center',
              fill: '#000', //文字的颜色
              fontSize: 12,
              lineHeight: 16,
            }
          }],
          color: ['#04A7B3', '#FFE209'],
          series: [
            {
              name: data[i].jcxm,
              type: 'pie',
              radius: ['60%', '80%'],
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: false,
                  fontSize: 14,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: data[i].yxsl, name: '合格率' },
                { value: data[i].zl - data[i].yxsl, name: '不合格率' },
              ]
            }
          ]
        };
        option && myChart.setOption(option);
      }


    },
    initNumChart2() {
      const chartDom = document.getElementById('chart2');
      const myChart = this.echarts.init(chartDom);
      const option = {
        graphic: [{ //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: 'center',
          style: {
            text: '水产类农药残留',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }, { //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: '92%',
          style: {
            text: '235/235次',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }],
        color: ['#04A7B3', '#FFE209'],
        series: [
          {
            name: '水产类农药残留',
            type: 'pie',
            radius: ['60%', '80%'],
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 100, name: '合格率' },
            ]
          }
        ]
      };
      option && myChart.setOption(option);
    },
    initNumChart3() {
      const chartDom = document.getElementById('chart3');
      const myChart = this.echarts.init(chartDom);
      const option = {
        tooltip: {
          trigger: 'item'
        },
        graphic: [{ //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: 'center',
          style: {
            text: '生鲜肉类瘦肉精',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }, { //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: '92%',
          style: {
            text: '235/235次',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }],
        color: ['#04A7B3', '#FFE209'],
        series: [
          {
            name: '生鲜肉类瘦肉精',
            type: 'pie',
            radius: ['60%', '80%'],
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 100, name: '合格率' },
            ]
          }
        ]
      };
      option && myChart.setOption(option);
    },
    initNumChart4() {
      const chartDom = document.getElementById('chart4');
      const myChart = this.echarts.init(chartDom);
      const option = {
        tooltip: {
          trigger: 'item'
        },
        graphic: [{ //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: 'center',
          style: {
            text: '果蔬类农药残',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }, { //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: '92%',
          style: {
            text: '231/235次',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }],
        color: ['#04A7B3', '#FFE209'],
        series: [
          {
            name: '果蔬农药残',
            type: 'pie',
            radius: ['60%', '80%'],
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 92, name: '合格率' },
              { value: 8, name: '不合格率' },
            ]
          }
        ]
      };
      option && myChart.setOption(option);
    },
    initNumChart5() {
      const chartDom = document.getElementById('chart5');
      const myChart = this.echarts.init(chartDom);
      const option = {
        tooltip: {
          trigger: 'item'
        },
        graphic: [{ //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: 'center',
          style: {
            text: '果蔬类农药残',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }, { //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: '92%',
          style: {
            text: '231/235次',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }],
        color: ['#04A7B3', '#FFE209'],
        series: [
          {
            name: '果蔬农药残',
            type: 'pie',
            radius: ['60%', '80%'],
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 92, name: '合格率' },
              { value: 8, name: '不合格率' },
            ]
          }
        ]
      };
      option && myChart.setOption(option);
    },
  }

}
</script>

<style scoped>
#pie_box1,
#pie_box2 {
  width: 100%;
  height: 275px;
  padding: 10px;
  box-sizing: border-box;
  border: 1px solid #04A7B3;
}

.chart_box {
  width: 100%;
  height: 200px;
  padding: 10px;
  box-sizing: border-box;
}

.chart_title_box {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
