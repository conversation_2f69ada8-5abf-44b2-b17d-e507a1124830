export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  delBtn: false,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  labelWidth: 140,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  align: 'center',
  searchLabelWidth:100,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "受检单位",
      prop: "sjdw",
      type: "tree",
      filterable: true,
      dicUrl: "/api/sh/sczt/list?current=1&size=9999",
      props: {
        label: "scmc",
        value: "id",
        res: 'data.records'
      },
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      nodeClick: (data, node, nodeComp) => {
        if (window.__cysj_component__) {
          window.__cysj_component__.handleNodeClick2(data, 'scmc','sjdwmc');

        }
      },
    },
    {
      label: "受检单位名称",
      prop: "sjdwmc",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "商户名称",
      filterable: true,
      prop: "shid",
      type: "tree",
      width: 150,
      dicUrl: "/api/sh/jyh/page?current=1&size=100000",
      props: {
        label: "jyhmc",
        value: "id",
        res: 'data.records'
      },
      nodeClick: (data, node, nodeComp) => {
        if (window.__cysj_component__) {
          window.__cysj_component__.handleNodeClick2(data, 'twh','twh');

        }
      },
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      search: true,
    },
    {
      label: "样品名称",
      prop: "ypid",
      type: "tree",
      filterable: true,
      dicUrl: "/api/ypgl/ypxx/list?current=1&size=9999",
      props: {
        label: "ypmc",
        value: "id",
        res: 'data.records'
      },
      cascader: ['ypdl'],
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      nodeClick: (data, node, nodeComp) => {
        if (window.__cysj_component__) {
          window.__cysj_component__.handleNodeClick(data,node);
          window.__cysj_component__.setColumnValue('ypmc',  data.ypmc);
        }
      },
      addDisplay: true,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "样品名称",
      prop: "ypmc",
      type: "input",
      row: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
      hide: false,
    },

    {
      label: "样品大类",
      prop: "ypdlmc",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: false,
      row:false
    },
    {
      label: "样品大类",
      prop: "ypdl",
      type: "select",
      dicUrl: "/api/blade-system/dict/dictionary?code=sample_big_type",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      cascader: ['ypxl'],
      filterable: true,
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      row: false,
      addDisplay: true,
      editDisplay: true,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "样品细类",
      prop: "ypxl",
      type: "select",
      dicUrl: "/api/ypgl/ypxx/getYpxl?ypdl={{ypdl}}",
      props: {
        label: "label",
        value: "value"
      },
      filterable: true,
      cascaderIndex: 0,
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "采样时间",
      prop: "cysj",
      type: "datetime",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "所属城市",
      prop: "sscs",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "所属任务",
      prop: "rwid",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      parent:false,
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      search: true,
      width: 100,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "样品编号",
      prop: "cybh",
      width: 150,
      type: "input",
      readonly: true,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "抽样单编号",
      prop: "cydbh",
      width: 150,
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "采样地址",
      prop: "cydz",
      width: 150,
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "采样人员",
      prop: "cyry",
      width: 150,
      type: "select",
      dicUrl: "/api/blade-user/page?current=1&size=100000&deptId=",
      props: {
        label: "name",
        value: "id",
        res: 'data.records'
      },
      row: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "检测状态",
      prop: "jczt",
      type: "select",
      dicUrl: "/api/blade-system/dict/dictionary?code=check_status",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      search: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "抽样基数",
      prop: "cyjs",
      type: "input",
      hide: true,
    },
    {
      label: "抽样数量",
      prop: "cysl",
      type: "input",
      hide: true,
    },
    {
      label: "生产单位",
      prop: "scdw",
      type: "input",
      hide: true,
    },
    {
      label: "生产/进货日期",
      prop: "scrq",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      hide: true,
    },
    {
      label: "摊位号",
      prop: "twh",
      type: "input",
      hide: true,
    },
    {
      label: "产品商标",
      prop: "cpsb",
      type: "input",
      hide: true,
    },
    {
      label: "产品规格",
      prop: "cpgg",
      type: "input",
      hide: true,
    },
    {
      label: "进货单号",
      prop: "jhdh",
      type: "input",
      hide: true,
    },
    {
      label: "供货者",
      prop: "ghz",
      type: "input",
      hide: true,
    },
    {
      label: "产地",
      prop: "cd",
      type: "input",
      hide: true,
    },

    {
      label: "产量",
      prop: "cl",
      type: "input",
      hide: true,
    },
    {
      label: "委托单位",
      prop: "wtdw",
      type: "input",
      hide: true,
    },
    {
      label: "备注",
      prop: "bz",
      type: "textarea",
      span: 24,
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "采样员签名",
      prop: "cyryqm",
      type: 'upload',
      listType: 'picture-img',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: false,
      rules: [
        {
          required: true,
          message: "请上传",
          trigger: "blur"
        }
      ],
      hide: true,
      multiple: true,
    },
    {
      label: "样品照片",
      prop: "url",
      type: 'upload',
      listType: 'picture-card',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      rules: [
        {
          required: true,
          message: "请上传",
          trigger: "blur"
        }
      ],
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: false,
      hide: true,
      multiple: true,
    },

    {
      label: "二维码",
      prop: "qrcode",
      type: "upload",
      listType: 'picture-img',
      span: 12,
      width: 100,
      row: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      search: true,
      searchRange:true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
