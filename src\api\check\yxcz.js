import request from '@/router/axios';
//修改采样数据
export const updateCysj = (row) => {
  return request({
    url: '/api/jc/cysj/submit',
    method: 'post',
    data: row
  })
}

export const getFhList = (current, size, params) => {
  return request({
    url: '/api/jc/yxfh/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
//检测数据列表
export const getJcList =  (current, size, params) => {
  return request({
    url: '/api/jc/jcjl/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
//检测阳性复核列表
export const getReviewList =  (current, size, params) => {
  return request({
    url: '/api/jc/yxfh/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

//阳性处置详情
export const getYxczDetail =  (jcid) => {
  return request({
    url: '/api/jc/yxcz/detail',
    method: 'get',
    params: jcid
  })
}

//阳性结果复核
export const jgFh = (row) => {
  return request({
    url: '/api/jc/jcsj/update',
    method: 'post',
    data: row
  })
}


//阳性处置保存
export const czSumbit = (row) => {
  return request({
    url: '/api/jc/yxcz/submit',
    method: 'post',
    data: row
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/jc/jcsj/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

// export const getList = (current, size, params) => {
//   return request({
//     url: '/api/jc/yxcz/list',
//     method: 'get',
//     params: {
//       ...params,
//       current,
//       size,
//     }
//   })
// }

export const getDetail = (id) => {
  return request({
    url: '/api/jc/yxcz/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/jc/yxcz/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/jc/yxcz/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/jc/yxcz/submit',
    method: 'post',
    data: row
  })
}

