export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  labelWidth: 120,
  searchLabelWidth:100,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    // {
    //   label: "考核项目名称",
    //   prop: "xqid",
    //   type: "select",
    //   dicUrl: "/api/zlgl_/mykhxq/list?current=1&size=99999",
    //   props: {
    //     label: "jcxm",
    //     value: "id",
    //     res: 'data.records',
    //   },
    //   slot:true,
    //   rules: [
    //     {
    //       required: true,
    //       message: "请输入",
    //       trigger: "blur"
    //     }
    //   ],
    // },
    {
      label: "任务名称",
      prop: "xqid",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      parent:false,
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "样品编号",
      prop: "ypbh",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    // {
    //   label: "检测室名称",
    //   prop: "khxmmc",
    //   type: "input",
    //   rules: [
    //     {
    //       required: true,
    //       message: "请输入",
    //       trigger: "blur"
    //     }
    //   ],
    // },
    {
      label: "检测项目",
      prop: "jcxm",
      type: "select",
      filterable: true,
      dicUrl: "/api/ypgl/jcxm/page?code=check_method?current=1&size=9999",
      props: {
        label: "xmmc",
        value: "xmbh",
        res: 'data.records'
      },
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "样品名称",
      prop: "ypmc",
      type: "input",
      search: true,
      searchSpan: 4,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "检测方法",
      prop: "jcff",
      type: "select",
      dicUrl: "/api/blade-system/dict/dictionary?code=check_method",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      rules: [{
        required: true,
        message: "请选择",
        trigger: "blur"
      }],
    },
    {
      label: "检测时间",
      prop: "jcsj",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      searchSpan:6,
      search: true,
      searchRange:true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "检测员",
      prop: "jcy",
      type: "input",
    },
    {
      label: "检测结果",
      prop: "jcjg",
      type: "select",
      dicData: [
        {
          label: '阴性',
          value: '阴性',
        },
        {
          label: '阳性',
          value: '阳性',
        },
      ],
      searchSpan:4,
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "考核结果",
      prop: "khjg",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
      hide: false,
    },

    {
      label: "上传记录",
      prop: "jl",
      type: "upload",
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      rules: [
        {
          required: true,
          message: "请上传",
          trigger: "change"
        }
      ],
    },

    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
