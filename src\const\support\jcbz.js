export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchShow: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  columnBtn: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
    },
    {
      label: "类型名称",
      prop: "lxmc",
      type: "input",
    },
    {
      label: "样品名称",
      prop: "ypmc",
      type: "input",
      search: true,
    },
    {
      label: "国标名称",
      prop: "gbmc",
      type: "input",
      search: true,
    },
    {
      label: "标准值",
      prop: "bzz",
      type: "input",
    },
    {
      label: "符号",
      prop: "fh",
      type: "select",
      dicData: [
        {
          dictValue: '<',
          dictKey: '1',
        },
        {
          dictValue: '=',
          dictKey: '2',
        },
        {
          dictValue: '>',
          dictKey: '3',
        },
        {
          dictValue: '<=',
          dictKey: '4',
        },
        {
          dictValue: '>=',
          dictKey: '5',
        },
      ],
      props: {
        label: "dictValue",
        value: "dictKey"
      },
    },
    {
      label: "单位",
      prop: "dw",
      type: "input",
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
