export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  labelWidth: 120,
  searchLabelWidth:100,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  align: 'center',
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "视频id",
      prop: "videoid",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "视频名称",
      prop: "videoname",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "视频 URL",
      prop: "videourl",
      type: "input",
      hide: true,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    {
      label: "视频密码",
      prop: "videopw",
      type: "input",
      rules: [
        {
          message: "请输入",
          trigger: "blur"
        }
      ],
    },
    // {
    //   label: "视频状态",
    //   prop: "videostatus",
    //   type: "input",
    // },
    {
      label: "休息日",
      prop: "restday",
      type: "select",
      multiple: true,
      width: 120,
      dicData: [
        {
          label: '周一',
          value: '1',
        },
        {
          label: '周二',
          value: '2',
        },
        {
          label: '周三',
          value: '3',
        },
        {
          label: '周四',
          value: '4',
        },
        {
          label: '周五',
          value: '5',
        },
        {
          label: '周六',
          value: '6',
        },
        {
          label: '周日',
          value: '7',
        }
      ],
    },
    {
      label: "上班时间A开始",
      prop: "videoworktime1",
      type: "time",
      format:"HH:mm",
      valueFormat:'HH:mm',
      width: 120,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "上班时间A结束",
      prop: "videoworktime1end",
      type: "time",
      format:"HH:mm",
      valueFormat:'HH:mm',
      width: 120,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "上班时间B开始",
      prop: "videoworktime2",
      type: "time",
      format:"HH:mm",
      valueFormat:'HH:mm',
      width: 120,
      clearable:true,
      // rules: [
      //   {
      //     required: true,
      //     message: "请选择",
      //     trigger: "change"
      //   }
      // ],
    },
    {
      label: "上班时间B结束",
      prop: "videoworktime2end",
      type: "time",
      format:"HH:mm",
      valueFormat:'HH:mm',
      width: 120,
      clearable:true,
      // rules: [
      //   {
      //     required: true,
      //     message: "请选择",
      //     trigger: "change"
      //   }
      // ],
    },
    {
      label: "理论上班时长",
      prop: "videlworkduration",
      type: "input",
    },
    {
      label: "实际工作时长",
      prop: "videorealduration",
      type: "input",
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "加权值",
      prop: "videorightval",
      type: "input",
    },
    {
      label: "封面截图",
      prop: "videoimageurl",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "告警邮件地址",
      prop: "videoemails",
      type: "input",
      width: 300,

    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
