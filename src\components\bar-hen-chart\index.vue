<template>
    <div class="card_bottom" id="card_bottom"></div>
</template>
<script>
import * as echarts from "echarts";
export default {
    name: "lineHen",
    props: {
        title: {
            type: String,
            default: "",
        },
        yData: {
            type: Array,
            default: [],
        },
        series: {
            type: Array,
            default: [],
        },
    },
    mounted() {
        this.getCardBottom();
    },
  watch:{
    yData:{
      handler(val){
        this.$nextTick(() => {
          this.getCardBottom();
        })
      },
      deep: true,
      immediate: true
    }
  },
    methods: {
        getCardBottom() {
            var chartDom = document.getElementById("card_bottom");
            var myChart = echarts.init(chartDom);
            var option;

            option = {
                title: {
                    text: this.title,
                    top: "2%",
                    left: "center", //居中 相当于Y
                    textStyle: {
                        color: "#16CBD9", //文字颜色
                        fontSize: "16", //文字大小
                    },
                },
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "shadow",
                    },
                },
                legend: {
                    top: "10%",
                    textStyle: {
                        color: "#fff", //文字颜色
                        fontSize: "14", //文字大小
                    },
                },
                grid: {
                    left: "1%",
                    top: "20%",
                    right: "4%",
                    bottom: "5%",
                    containLabel: true,
                },
                xAxis: {
                    type: "value",
                    boundaryGap: [0, 0.01],
                    axisLabel: {
                        //y轴文字的配置
                        textStyle: {
                            color: "RGBA(22, 203, 217, 0.5)", //Y轴内容文字颜色
                        },
                    },
                  splitLine: { // 添加网格线配置
                    lineStyle: {
                      color: ['#666'], // 浅灰色
                    }
                  }
                },
                yAxis: {
                    type: "category",
                    data: this.yData,
                    axisLabel: {
                        //y轴文字的配置
                        textStyle: {
                            color: "RGBA(22, 203, 217, 0.5)", //Y轴内容文字颜色
                        },
                    },

                    axisLine: {
                        //y轴线的配置
                        show: true, //是否展示
                        lineStyle: {
                            color: "#ECEDF0", //y轴线的颜色（若只设置了y轴线的颜色，未设置y轴文字的颜色，则y轴文字会默认跟设置的y轴线颜色一致）
                            width: 1, //y轴线的宽度
                            type: "solid", //y轴线为实线
                        },
                    },
                  splitLine: { // 添加网格线配置
                    lineStyle: {
                      color: ['#666'], // 浅灰色
                    }
                  }
                },
                series: this.series,
            };

            option && myChart.setOption(option);
        },
    },
};
</script>
