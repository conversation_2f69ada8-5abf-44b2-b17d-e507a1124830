export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  searchShowBtn: true,   // 栏目折叠显隐
  searchShow: false,
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  align: 'center',
  labelWidth: 120,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "项目编号",
      prop: "xmbh",
      width: 150,
      type: "input",
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "项目名称",
      prop: "xmmc",
      type: "input",
      search: true,
      searchSpan:4,
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "检测方法",
      prop: "jclx",
      type: "select",
      dicUrl: "/api/blade-system/dict/dictionary?code=check_method",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      rules: [{
        required: true,
        message: "请选择",
        trigger: "blur"
      }],
    },
    {
      label: "检测灯光",
      prop: "jcdg",
      type: "select",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      dicData: [
        {
          dictValue: '绿灯',
          dictKey: 'green',
        },
        {
          dictValue: '红灯',
          dictKey: 'red',
        },
        {
          dictValue: '蓝灯',
          dictKey: 'blue',
        },
        {
          dictValue: '黄灯',
          dictKey: 'yellow',
        },
        {
          dictValue: '紫灯',
          dictKey: 'purple',
        },
        {
          dictValue: '白灯',
          dictKey: 'white',
        },
      ],
    },
    {
      label: "检测时长",
      prop: "jcsc",
      type: "input",
    },
    {
      label: "曲线名称",
      prop: "qxid",
      type: "select",
      dicUrl: "/api/gnsz/jqqx/list?current=1&size=9999",
      props: {
        label: "qxmc",
        value: "id",
        res: 'data.records',
      },
    },
    // {
    //   label: "曲线id",
    //   prop: "qxid",
    //   type: "input",
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   hide: true,
    // },
    {
      label: "标准类型名称",
      prop: "lxmc",
      cascader: ['jcbz'],
    },
    {
      label: "标准编号",
      prop: "jcbz",
      width: 150,
      type: "select",
      filterable: true,
      dicUrl: '/api/jszc/jcbz/list?current=1&size=9999&lxmc={{lxmc}}',
      props: {
        label: "ypmc",
        value: "id",
        res: 'data.records'
      },
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
    },
    {
      label: "同步/未同步",
      prop: "tbqk",
      type: "select",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      row: true,
      dicData: [
        {
          dictValue: '未同步',
          dictKey: '0',
        },
        {
          dictValue: '同步',
          dictKey: '1',
        },
      ],
    },
    {
      label: '项目图片',
      type: 'upload',
      listType: 'picture-img',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      row: true,
      prop: 'records',
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

  ]
}
