import request from '@/router/axios';

export const getDetail = (id) => {
  return request({
    url: '/api/sb/sbtj/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/sb/hcjl/getHcjl',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
// 移液器详情
export const getYyqDetail = (hcid) => {
  return request({
    url: '/api/sb/hcjl/yyqDetail',
    method: 'get',
    params: {
      hcid
    }
  })
}
// 电子天平详情
export const getDztpDetail = (hcid) => {
  return request({
    url: '/api/sb/hcjl/dztpDetail',
    method: 'get',
    params: {
      hcid
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/sb/hcjl/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/sb/sbtj/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/sb/sbtj/submit',
    method: 'post',
    data: row
  })
}

