export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchMenuSpan: 4,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  labelWidth: 120,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  align: 'center',
  searchLabelWidth:120,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "快检室",
      prop: "kjsids",
      width: 240,
      type: "tree",
      multiple: true,
      dicUrl: "/api/blade-system/dept/threeLevelTree",
      readonly: true,
      props: {
        label: "deptName",
        value: "id",
        res: 'data',
      },
      rules: [{
        required: true,
        message: "请选择",
        trigger: "change"
      }],

    },
    // {
    //   label: "考核项目名称",
    //   prop: "khxmmc",
    //   type: "input",
    //   searchSpan:4,
    //   search: true,
    //   width: 100,
    // },
    {
      label: "截至日期",
      prop: "jzrq",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      searchSpan:4,
      search: true,
      searchRange:true,
      rules: [{
        required: true,
        message: "请选择",
        trigger: "change"
      }],
    },
    {
      label: "检测项目",
      prop: "jcxm",
      searchSpan:4,
      search: true,
      type: "select",
      filterable:true,
      dicUrl: "/api/ypgl/jcxm/page?current=1&size=9999",
      props: {
        label: "xmmc",
        value: "id",
        res: 'data.records'
      },
      rules: [{
        required: true,
        message: "请选择",
        trigger: "change"
      }],
    },
    {
      label: "盲样制样人员",
      prop: "myzyry",
      type: "input",
      width: 100,
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "样品名称",
      prop: "ypmc",
      type: "input",
      searchSpan:4,
      search: true,
      rules: [{
        required: true,
        message: "请输入",
        trigger: "blur"
      }],
    },
    {
      label: "检测方法",
      prop: "jcff",
      type: "select",
      dicUrl: "/api/blade-system/dict/dictionary?code=check_method",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      rules: [{
        required: true,
        message: "请选择",
        trigger: "change"
      }],
    },
    // {
    //   label: "考核制样数量",
    //   prop: "sl",
    //   type: "input",
    //   width: 100,
    // },
    {
      label: "是否应答",
      prop: "yd",
      type: "select",
      searchSpan:4,
      search: true,
      addDisplay: false,
      editDisplay: false,
      dicData: [
        {
          label: '否',
          value: '否',
        },
        {
          label: '是',
          value: '是',
        },
      ],
      rules: [{
        required: true,
        message: "请选择",
        trigger: "change"
      }],
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
