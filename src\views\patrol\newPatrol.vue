<template>
  <basic-container back>
    <div id="print">
      <div class="title_box">{{ temName }}</div>
      <el-divider></el-divider>
      <avue-form v-if="id" :option="option" v-model="jlVO"></avue-form>
      <div class="item_title">食品安全巡检类目</div>
      <table>
        <thead>
        <tr>
          <th>序列</th>
          <th>巡查项目</th>
          <th>巡查内容</th>
          <th>巡查结果</th>
          <th>不符合描述</th>
          <th>备注</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(item,index) in modelXq2" :key="index">
          <td>{{index + 1}}</td>
          <td>{{item.zdmc}}</td>
          <td>{{item.nr}}</td>
          <td>
            <el-select v-model="item.jg" size="small" placeholder="请选择">
              <el-option label="√" value="1"></el-option>
              <el-option label="×" value="2"></el-option>
              <el-option label="-" value="0"></el-option>
            </el-select>
          </td>
          <td>
            <el-input v-if="item.jg == 2" size="mini" v-model="item.ms" placeholder=""></el-input>
          </td>
          <td>{{item.xjxbz}}</td>

        </tr>
        </tbody>
      </table>
      <div style="font-size: 14px; margin: 10px 0">
        说明：★为关键项目，◎仅为餐饮配送和中央厨房单位关键项目，⊙代表仅为小型餐饮（加工面积小于200平方）适用为一般项目，它类型为关键项目；
        关键项目在不合格处标注下横线,并注简单信息；符合项目栏内打“√”，如不符合应在相应栏内标示关键项为“★”或一般项目为“×”，若无此项目内容的，用“—”标注；
        统计检查结果，在结论处须打“√”。新提A级的单位报辖区局审定、市局公布。
      </div>
      <div>
        <div class="item_title">备注</div>
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          v-model="jlVO.bz"
        >
        </el-input>
      </div>
      <avue-crud
        style="width: 100%; margin: 20px 0"
        :data="footTable"
        :option="footOption"
        :span-method="spanMethod"
      >
        <template slot="menuLeft">
          <div class="item_title">评分方式</div>
        </template>
        <template slot-scope="{ row }" slot="zhpf">
          <el-input
            v-model="jlVO.zhpf"
            size="small"
            style="width: 80%"
            placeholder="请输入分数"
          ></el-input>
        </template>
        <template slot-scope="{ row }" slot="pj">
          <el-select
            v-model="jlVO.pj"
            placeholder="请选择"
            size="small"
            style="width: 80%"
            readonly
          >
            <el-option
              v-for="item in pjList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
      </avue-crud>

      <div style="margin: 10px 0">
        <div class="item_title">
          <span class="required_span">*</span>
          巡查结果
        </div>
        <el-select
          size="small"
          style="width: 400px"
          v-model="jlVO.xcjg"
          placeholder="请选择"
        >
          <el-option label="合格" value="合格"></el-option>
          <el-option label="不合格" value="不合格"></el-option>
        </el-select>
      </div>
      <div class="sign_box">
        <div style="margin-right: 25px" v-for="(item,index) in modelXq3" :key="index" v-if="item.zdmc.includes('签名')">
          <div style="margin-bottom: 10px">{{item.zdmc}}</div>
          <el-upload
            class="upload-demo"
            action="/api/manage/put-object"
            :limit="1"
            :on-success="handleAvatarSuccess2.bind(null,{'index':index,'item':item})"
            :show-file-list="false"
          >
            <div style="border: 1px solid #eee">
              <img
                v-if="item.jg"
                :src="item.jg"
                style="width: 150px; height: 150px"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </div>
          </el-upload>
        </div>
      </div>
      <div class="report_box">
        <div style="margin-right: 25px" v-for="(item,index) in modelXq3" :key="index" v-if="item.zdmc.includes('报告')">
          <div style="margin-bottom: 10px">{{item.zdmc}}</div>
          <el-upload
            class="upload-demo"
            action="/api/manage/put-object"
            :limit="1"
            :on-success="handleAvatarSuccess2.bind(null,{'index':index,'item':item})"
            :show-file-list="false"
          >
            <div style="border: 1px solid #eee">
              <img
                v-if="item.jg"
                :src="item.jg"
                style="width: 150px; height: 150px"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </div>
          </el-upload>
        </div>
      </div>
      <el-divider></el-divider>
      <div class="btn_box">
        <el-button style="width: 220px" type="primary" @click="save">保 存</el-button>
      </div>
    </div>
  </basic-container>
</template>

<script>
  import { getDetail, getList } from "@/api/patrol/model";
import { add } from "@/api/patrol/jl";
import {mapGetters} from "vuex";

export default {
  name: "newPatrol",
  data() {
    return {
      option: {
        labelPosition: "top",
        labelWidth: 120,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "所属任务",
            prop: "rwid",
            type: "tree",
            dicUrl: "/api/blade-system/dept/tree",
            props: {
              label: "title",
              value: "id",
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change"
              }
            ],
          },
          {
            label: "单位类型",
            prop: "dwlx",
            type: "select",
            dicUrl: "/api/blade-system/dict/dictionary?code=shop_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change"
              }
            ],
          },
          {
            label: "巡查日期",
            prop: "xcrq",
            type: "date",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change"
              }
            ],
          },
          {
            label: "被检单位名称",
            prop: "bjdwmc",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur"
              }
            ],
          },
          {
            label: "食安负责人",
            prop: "safzr",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur"
              }
            ],
          },
          {
            label: "食安负责人电话",
            prop: "safzrdh",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur"
              }
            ],
          },
        ],
      },
      jlVO:{},
      footTable: [
        {
          zhpf: "",
          pjfj: "优",
          df: "80-100分",
          pj: "",
        },
        {
          pjfj: "良",
          df: "70-80分",
          pj: "",
        },
        {
          pjfj: "中",
          df: "60-70分",
          pj: "",
        },
        {
          pjfj: "差",
          df: "60分以下",
          pj: "",
        },
      ],
      footOption: {
        border: true,
        menu: false,
        addBtn: false,
        refreshBtn: false,
        filterBtn: false,
        searchShowBtn: false,
        columnBtn: false,
        align: "center",
        column: [
          {
            label: "综合评分",
            prop: "zhpf",
          },
          {
            label: "评价分级",
            prop: "pjfj",
          },
          {
            label: "得分",
            prop: "df",
          },
          {
            label: "评级",
            prop: "pj",
          },
        ],
      },
      pjList: [
        {
          label: "优",
          value: "优",
        },
        {
          label: "良",
          value: "良",
        },
        {
          label: "中",
          value: "中",
        },
        {
          label: "差",
          value: "差",
        },
      ],
      jg: "",
      temName: "",
      checkType: "",
      modelXq1: [],
      modelXq2: [],
      modelXq3: [],
      id: '',
      noLeaveprompt: false
    };
  },
  computed: {
    ...mapGetters(["tagList"]),
  },
  beforeRouteLeave(to, from, next) {
    if (!this.noLeaveprompt) { // 判断表单数据是否变化，以及提交后不进行此保存提示
      setTimeout(() => { // hash模式下，此处必须要加延迟执行，主要解决浏览器前进后退带来的闪现
        this.$confirm('您的数据尚未保存，是否离开？', '离开页面', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          next()
          let {tag, key} = this.findTag('/patrol/xctj/new');
          this.$store.commit("DEL_TAG", tag);
        }).catch(() => {
          next(false)
        })
      }, 200)
    } else {
      next()
    }
  },

  mounted() {
    this.id = this.$route.query.id
    this.getDetail(this.id);
    this.$router.beforeEach((to,from,next)=>{
      next()
    })
  },

  methods: {
    handleAvatarSuccess2(e,res) {
      e.item.jg = res.data
    },
    save() {
      if(!this.jlVO.xcjg){
        this.$message.error('请输入信息后操作')
        return
      }
      this.modelXq1 = this.modelXq1.map(item=>{
        return {
          ...item,
          jg: this.jlVO[item.label]
        }
      })
      const modelXq = (this.modelXq1.concat(this.modelXq2).concat(this.modelXq3)).map(item=>{
        return {
          ...item,
          jlid: 0,
          id: 0
        }
      })
      const notOK = this.modelXq2.filter(item=>item.jg == '2').length
      add({
        jlVO: {
          ...this.jlVO,
          mbid: this.id,
          bhgx: notOK
        },
        jlXq: modelXq
      }).then(res=>{
        if(res.data.code == 200){
          this.noLeaveprompt = true
          this.$message.success(res.data.msg)
          this.$router.push('/patrol/xctj')
        }
      })
    },
    spanMethod({ rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 3) {
        if (rowIndex == 0) {
          return {
            rowspan: 4,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    print() {
      const printContents = document.getElementById("print").innerHTML;
      const originalContents = document.body.innerHTML;
      // const doc = new jsPDF();
      document.body.innerHTML = printContents;
      window.print();
      window.location.reload();
    },
    getDetail(id) {
      getDetail(id).then((res) => {
        const detail = res.data.data;
        this.temName = detail.model.mbmc;
        this.jlVO.pflx = detail.model.pflx;
        detail.modelXq.map(item=>{
          if(item.mblx == 1){
            if (item.zdmc == "食品经营许可证发放时间") {
              this.option.column.push({
                label: item.zdmc,
                prop: "input" + `${this.option.column.length + 1}`,
                type: "date",
                valueFormat: "yyyy-MM-dd",
              });
            } else {
              this.option.column.push({
                label: item.zdmc,
                prop: "input" + `${this.option.column.length + 1}`,
                type: "input",
              });
            }
            this.modelXq1.push({
              ...item,
              label: "input" + this.option.column.length,
            })
          }
          if(item.mblx == 2){
            this.modelXq2.push({
              ...item,
              ...item,
              ms: '',
              jg: ''
            })
          }
          if(item.mblx == 3){
            this.modelXq3.push({
              ...item,
              ms: '',
              jg: ''
            })
          }
        })
        if (this.jlVO.pflx == 1) {
          this.footTable = this.footTable.map((item, index) => {
            return {
              ...item,
              pjfj:
                index == 0
                  ? "优"
                  : index == 1
                  ? "良"
                  : index == 2
                  ? "中"
                  : index == 3
                  ? "差"
                  : "",
            };
          });
          this.pjList = [
            {
              label: "优",
              value: "优",
            },
            {
              label: "良",
              value: "良",
            },
            {
              label: "中",
              value: "中",
            },
            {
              label: "差",
              value: "差",
            },
          ];
        }
        if (this.jlVO.pflx == 2) {
          this.footTable = this.footTable.map((item, index) => {
            return {
              ...item,
              pjfj:
                index == 0
                  ? "A"
                  : index == 1
                  ? "B"
                  : index == 2
                  ? "C"
                  : index == 3
                  ? "D"
                  : "",
            };
          });
          this.pjList = [
            {
              label: "A",
              value: "A",
            },
            {
              label: "B",
              value: "B",
            },
            {
              label: "C",
              value: "C",
            },
            {
              label: "D",
              value: "D",
            },
          ];
        }
      });
    },
    findTag(value) {
      let tag, key;
      this.tagList.map((item, index) => {
        if (item.value.indexOf(value) > -1) {
          tag = item;
          key = index;
        }
      });
      return {tag: tag, key: key};
    },
  },
};
</script>

<style scoped lang="less">
  .required_span {
    color: #F56C6C;
    font-size: 14px;
    margin-right: 4px;
    font-weight: normal;
  }
  table {
    width: 100%;
    table-layout: fixed;
    font-size: 12px !important;
  }

  table,
  td,
  th {
    text-align: center;
    border: 1px solid #ccc;
    border-collapse: collapse;
    font-weight: normal;
    font-size: 12px !important;
  }

  table tr {
    height: 30px;
    padding: 0 5px !important;
    font-size: 12px !important;
    word-wrap: break-word !important;
  }

  table td {
    padding: 5px;
    font-size: 12px !important;
    word-wrap: break-word !important;
  }

  .btn_box {
  width: 100%;
  margin: 20px 0;
  text-align: center;
}
.item_title {
  font-size: 14px;
  line-height: 36px;
  font-weight: bolder;
  margin-bottom: 10px;
}
.sign_box {
  width: 100%;
  display: flex;
  font-size: 14px;
  margin: 20px 0;
}
.report_box {
  width: 100%;
  display: flex;
  font-size: 14px;
  margin: 20px 0;
}
.title_box {
  width: 100%;
  line-height: 40px;
  color: #04a7b3;
  text-align: center;
}
</style>
