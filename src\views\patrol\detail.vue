<template>
  <basic-container back>
    <div id="print">
      <div class="title_box">巡查单详情</div>
      <el-divider></el-divider>
      <el-descriptions column="2" title="">
        <el-descriptions-item label="所属任务">{{jlVO.deptName}}</el-descriptions-item>
        <el-descriptions-item label="单位类型">{{jlVO.typeName}}</el-descriptions-item>
        <el-descriptions-item label="巡查日期">{{jlVO.xcrq}}</el-descriptions-item>
        <el-descriptions-item label="被检单位名称">{{jlVO.bjdwmc}}</el-descriptions-item>
        <el-descriptions-item label="食安负责人">{{jlVO.safzr}}</el-descriptions-item>
        <el-descriptions-item label="食安负责人电话">{{jlVO.safzrdh}}</el-descriptions-item>
        <el-descriptions-item v-for="(item,index) in modelXq1" :key="index" :label="item.zdmc">{{item.jg}}</el-descriptions-item>
      </el-descriptions>
      <div class="item_title">食品安全巡检类目</div>
      <table>
        <thead>
        <tr>
          <th>序列</th>
          <th>巡查项目</th>
          <th>巡查内容</th>
          <th>巡查结果</th>
          <th>不符合描述</th>
          <th>备注</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(item,index) in modelXq2" :key="index">
          <td>{{index + 1}}</td>
          <td>{{item.zdmc}}</td>
          <td>{{item.nr}}</td>
          <td>{{item.jg == 1?'符合':item.jg == 2?'不符合':item.jg == 0?'-':''}}</td>
          <td>{{item.ms}}</td>
          <td>{{item.xjxbz}}</td>
        </tr>
        </tbody>
      </table>
      <div style="font-size: 14px; margin: 10px 0">
        说明：★为关键项目，◎仅为餐饮配送和中央厨房单位关键项目，⊙代表仅为小型餐饮（加工面积小于200平方）适用为一般项目，它类型为关键项目；
        关键项目在不合格处标注下横线,并注简单信息；符合项目栏内打“√”，如不符合应在相应栏内标示关键项为“★”或一般项目为“×”，若无此项目内容的，用“—”标注；
        统计检查结果，在结论处须打“√”。新提A级的单位报辖区局审定、市局公布。
      </div>
      <div>
        <div class="item_title">备注</div>
        <div>{{jlVO.bz}}</div>
      </div>
      <avue-crud
        style="width: 100%; margin: 20px 0"
        :data="footTable"
        :option="footOption"
        :span-method="spanMethod"
      >
        <template slot="menuLeft">
          <div class="item_title">评分方式</div>
        </template>
        <template slot-scope="{ row }" slot="zhpf">
          <span>{{jlVO.zhpf}}</span>
        </template>
        <template slot-scope="{ row }" slot="pj">
          <span>{{jlVO.pj}}</span>
        </template>
      </avue-crud>
      <div style="margin: 10px 0">
        <div class="item_title">巡查结果</div>
        <div>{{jlVO.xcjg}}</div>
      </div>
      <!-- sign -->
      <div v-if="showSignTitle">
        <div style="margin-top: 10px" class="item_title">签名栏</div>
        <div class="sign_box">
          <div style="margin-right: 25px" v-for="(item,index) in modelXq3" :key="index" v-if="item.zdmc.includes('签名')">
            <div style="margin-bottom: 10px">{{item.zdmc}}</div>
            <img
              v-if="item.jg"
              :src="item.jg"
              @click="openPreview(item.jg)"
              style="width: 150px; height: 150px;cursor:pointer;"
            />
          </div>
        </div>
      </div>
      <!-- report -->
      <div v-if="showReportTitle">
        <div style="margin-top: 10px" class="item_title">报告栏</div>
        <div class="report_box">
          <div style="margin-right: 25px" v-for="(item,index) in modelXq3" :key="index" v-if="item.zdmc.includes('报告')">
            <div style="margin-bottom: 10px">{{item.zdmc}}</div>
            <img
              v-if="item.jg"
              :src="item.jg"
              @click="openPreview(item.jg)"
              style="width: 150px; height: 150px;cursor:pointer;"
            />
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <div class="btn_box">
        <el-button style="width: 220px" type="primary" @click="goBack">返 回</el-button>
        <el-button style="width: 100px" @click="print">打印</el-button>
      </div>
    </div>
  </basic-container>
</template>

<script>
  import { getRecordDetail,getDepart,getType } from "@/api/patrol/model";

  export default {
        name: "detail",
      data(){
          return {
            option: {
              labelPosition: "left",
              labelWidth: 140,
              readonly: true,
              submitBtn: false,
              emptyBtn: false,
              column: [
                {
                  label: "单位类型",
                  prop: "dwlx",
                  type: "select",
                  dicUrl: "/api/blade-system/dict/dictionary?code=shop_type",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                  },
                },
                {
                  label: "食安负责人电话",
                  prop: "safzrdh",
                  type: "input",
                  rules: [
                    {
                      required: true,
                      message: "请输入",
                      trigger: "blur"
                    }
                  ],
                },
              ],
            },
            jlVO:{},
            footTable: [
              {
                zhpf: "",
                pjfj: "优",
                df: "80-100分",
                pj: "",
              },
              {
                pjfj: "良",
                df: "70-80分",
                pj: "",
              },
              {
                pjfj: "中",
                df: "60-70分",
                pj: "",
              },
              {
                pjfj: "差",
                df: "60分以下",
                pj: "",
              },
            ],
            footOption: {
              border: true,
              menu: false,
              addBtn: false,
              refreshBtn: false,
              filterBtn: false,
              searchShowBtn: false,
              columnBtn: false,
              align: "center",
              column: [
                {
                  label: "综合评分",
                  prop: "zhpf",
                  type: "input",
                },
                {
                  label: "评价分级",
                  prop: "pjfj",
                },
                {
                  label: "得分",
                  prop: "df",
                },
                {
                  label: "评级",
                  prop: "pj",
                },
              ],
            },
            pjList: [
              {
                label: "优",
                value: "优",
              },
              {
                label: "良",
                value: "良",
              },
              {
                label: "中",
                value: "中",
              },
              {
                label: "差",
                value: "差",
              },
            ],
            jg: "",
            modelXq1: [],
            modelXq2: [],
            modelXq3: [],
            id: '',
          }
      },
      mounted() {
        this.id = this.$route.query.id
        this.getDetail(this.id);
      },
      methods:{
        openPreview (e) {
          const list = [{
            url: e
          }]
          this.$ImagePreview(list,0, {
            closeOnClickModal: true,
          });
        },
        goBack(){
          this.$router.back()
        },
        spanMethod({ rowIndex, columnIndex }) {
          if (columnIndex === 0 || columnIndex === 3) {
            if (rowIndex == 0) {
              return {
                rowspan: 4,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        },
        handleAvatarSuccess2(e,res) {
          e.item.jg = res.data
        },
        print() {
          const printContents = document.getElementById("print").innerHTML;
          const originalContents = document.body.innerHTML;
          // const doc = new jsPDF();

          document.body.innerHTML = printContents;
          document.body.style = {
            padding: '20px'
          }
          window.print();
          window.location.reload();
        },
        getDepart(id){
          getDepart(id).then(res=>{
            this.jlVO = {
              ...this.jlVO,
              deptName: res.data.data.deptName
            }
          })
        },
        getType(e){
          getType().then(res=>{
            this.jlVO = {
              ...this.jlVO,
              typeName: (res.data.data.find(item=>item.dictKey==e)).dictValue
            }
          })
        },
        getDetail(id) {
          getRecordDetail(id).then((res) => {
            const detail = res.data.data;
            this.jlVO = detail.jlVO
            detail.jlXq.map(item=>{
              if(item.mblx == 1){
                if (item.zdmc == "食品经营许可证发放时间") {
                  this.option.column.push({
                    label: item.zdmc,
                    prop: "input" + `${this.option.column.length + 1}`,
                    type: "date",
                    valueFormat: "yyyy-MM-dd",
                  });
                } else {
                  this.option.column.push({
                    label: item.zdmc,
                    prop: "input" + `${this.option.column.length + 1}`,
                    type: "input",
                  });
                }
                this.modelXq1.push({
                  ...item,
                  label: "input" + this.option.column.length,
                })
                this.jlVO["input" + this.option.column.length] = item.jg
              }
              if(item.mblx == 2){
                this.modelXq2.push(item)
              }
              if(item.mblx == 3){
                this.modelXq3.push(item)
              }
            })
            if (this.jlVO.pflx == 1) {
              this.footTable = this.footTable.map((item, index) => {
                return {
                  ...item,
                  pjfj:
                    index == 0
                      ? "优"
                      : index == 1
                      ? "良"
                      : index == 2
                        ? "中"
                        : index == 3
                          ? "差"
                          : "",
                };
              });
              this.pjList = [
                {
                  label: "优",
                  value: "优",
                },
                {
                  label: "良",
                  value: "良",
                },
                {
                  label: "中",
                  value: "中",
                },
                {
                  label: "差",
                  value: "差",
                },
              ];
            }
            if (this.jlVO.pflx == 2) {
              this.footTable = this.footTable.map((item, index) => {
                return {
                  ...item,
                  pjfj:
                    index == 0
                      ? "A"
                      : index == 1
                      ? "B"
                      : index == 2
                        ? "C"
                        : index == 3
                          ? "D"
                          : "",
                };
              });
              this.pjList = [
                {
                  label: "A",
                  value: "A",
                },
                {
                  label: "B",
                  value: "B",
                },
                {
                  label: "C",
                  value: "C",
                },
                {
                  label: "D",
                  value: "D",
                },
              ];
            }
            this.getDepart(detail.jlVO.rwid)
            this.getType(detail.jlVO.dwlx)
          });

        },
      },
      computed :{
          showSignTitle() {
            return this.modelXq3.some(item => item.zdmc.includes('签名'));
          },
          showReportTitle() {
            return this.modelXq3.some(item => item.zdmc.includes('报告'));
          }
      }
    }
</script>

<style lang="less" scoped>
  .btn_box {
    width: 100%;
    margin: 20px 0;
    text-align: center;
  }
  .item_title {
    font-size: 14px;
    line-height: 36px;
    font-weight: bolder;
    margin-bottom: 5px;
  }
  .sign_box {
    width: 100%;
    display: flex;
    font-size: 14px;
    margin-bottom: 20px;
  }
  .report_box {
    width: 100%;
    display: flex;
    font-size: 14px;
    margin-bottom: 20px;
  }
  .title_box {
    width: 100%;
    line-height: 40px;
    color: #04a7b3;
    text-align: center;
  }
@page {
  margin: 0.5in 20px;
}
  table {
    width: 100%;
    table-layout: fixed;
    font-size: 12px !important;
  }

  table,
  td,
  th {
    text-align: center;
    border: 1px solid #ccc;
    border-collapse: collapse;
    font-weight: normal;
    font-size: 12px !important;
  }

  table tr {
    height: 30px;
    padding: 0 5px !important;
    font-size: 12px !important;
    word-wrap: break-word !important;
  }

  table td {
    padding: 5px;
    font-size: 12px !important;
    word-wrap: break-word !important;
  }


</style>
