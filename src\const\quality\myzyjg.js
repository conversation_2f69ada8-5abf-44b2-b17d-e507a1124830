export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  addBtn: false,
  labelWidth: 120,
  searchLabelWidth:100,
  searchShow: false,
  searchShowBtn: true,   // 栏目折叠显隐
  columnBtn: false,       // 操作列显隐
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  refreshBtn: true,      // 刷新
  printBtn: false,       // 表格打印导出
  filterBtn: false,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "样品名称",
      prop: "ypmc",
      type: "input",
    },
    {
      label: "样品编号",
      prop: "ypbh",
      type: "input",
    },
    {
      label: "检测项目",
      prop: "jcxm",
      type: "input",
    },
    {
      label: "称量样",
      prop: "cll",
      type: "input",
    },
    {
      label: "复溶加水量",
      prop: "frjsl",
      type: "input",
    },
    {
      label: "检出限",
      prop: "jcx",
      type: "input",
    },
    {
      label: "样品大类",
      prop: "ypdl",
      type: "input",
    },
    {
      label: "样品细类",
      prop: "ypxl",
      type: "input",
    },

    {
      label: "制样时间",
      prop: "zysj",
      type: "input",
    },
    {
      label: "需要盲样时间",
      prop: "xymysj",
      type: "input",
    },
    {
      label: "加标量",
      prop: "jbl",
      type: "input",
    },
    {
      label: "加标标液浓度",
      prop: "jbbynd",
      type: "input",
    },
    {
      label: "加标结果",
      prop: "jbjg",
      type: "input",
    },
    {
      label: "提交人",
      prop: "tjr",
      type: "input",
    },
    {
      label: "提交时间",
      prop: "tjsj",
      type: "input",
    },
    {
      label: "更新时间",
      prop: "gxsj",
      type: "input",
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
