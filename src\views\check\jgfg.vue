<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="jcsjData"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button
          type="primary"
                   size="small"
                   icon="el-icon-plus"
                   @click="handleAdd">生成检测抽检单
        </el-button>
      </template>
      <template slot-scope="{row}" slot="detail">
        <el-button type="text" size="mini" @click="showDetail(row)">查看</el-button>
      </template>
<!--      <template slot-scope="{row}" slot="other">-->
<!--        <el-button type="text" size="mini" @click="showOther(row)">其他信息</el-button>-->
<!--      </template>-->
<!--      <template slot-scope="{row}" slot="hgz">-->
<!--        <img :src="row.scope.hgz" style="width: 20px;" alt="">-->
<!--      </template>-->
      <template slot-scope="{row}" slot="zzjg">
        <div v-if="row.zzjg==1" style="color: #606266">阴性</div>
        <div v-if="row.zzjg==2" style="color: red">阳性</div>
      </template>

    </avue-crud>

    <el-dialog :visible.sync="dialogFormVisible" append-to-body title="检测详情">
      <div style="width: 100%">
        <avue-form v-model="checkForm" :option="checkOption"></avue-form>
      </div>
      <div slot="footer" style="display: flex;justify-content: space-between">
        <el-button @click="dialogFormVisible = false">阳性处置</el-button>
        <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="otherShow" append-to-body title="其他信息">
      <avue-form v-model="otherForm" :option="otherOption" @submit="saveOther"></avue-form>
    </el-dialog>

    <el-dialog :visible.sync="quicklyShow" width="70%" append-to-body title="快速抽检记录单">
      <el-tabs type="card">
        <el-tab-pane label="正面">
          <div style="width: 100%">
            <div class="quickly_title">食品快速抽检记录单</div>
            <el-descriptions  :colon="false" title="" :column="2">
              <el-descriptions-item label="快检室信息："></el-descriptions-item>
              <el-descriptions-item label="检测单位：">18100000000</el-descriptions-item>
              <el-descriptions-item label="抽检员签名：">
                <div style="width: 200px;height: 20px;border-bottom: 1px solid #999"></div>
              </el-descriptions-item>
              <el-descriptions-item label="受检单位签名：">
                <div style="width: 200px;height: 20px;border-bottom: 1px solid #999"></div>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions :colon="false" title="" :column="2" border labelClassName="desc_title">
              <el-descriptions-item label="记录单生成日期"></el-descriptions-item>
              <el-descriptions-item label="抽样单编号"></el-descriptions-item>
              <el-descriptions-item label="抽样地点"></el-descriptions-item>
              <el-descriptions-item label="委托单位"></el-descriptions-item>
              <el-descriptions-item label="受检单位"></el-descriptions-item>
              <el-descriptions-item label="经营者姓名"></el-descriptions-item>
              <el-descriptions-item label="联系电话"></el-descriptions-item>
            </el-descriptions>
            <el-table
              height="250"
              :data="quickTable"
              border
              style="width: 100%;margin-top: 10px">
              <el-table-column
                prop="date"
                label="采样编号">
              </el-table-column>
              <el-table-column
                prop="name"
                label="样品名称">
              </el-table-column>
              <el-table-column
                prop="address"
                label="抽样基数">
              </el-table-column>
              <el-table-column
                prop="date"
                label="规格">
              </el-table-column>
              <el-table-column
                prop="name"
                label="生产/进货日期">
              </el-table-column>
              <el-table-column
                prop="address"
                label="产地">
              </el-table-column>
              <el-table-column
                prop="name"
                label="生产单位">
              </el-table-column>
              <el-table-column
                prop="address"
                label="供货者信息">
              </el-table-column>
            </el-table>
            <div class="quick_remark">备注：</div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="背面">
          <div style="width: 100%">
            <div class="quickly_title">食品快速抽检记录单（B）</div>
            <el-descriptions :colon="false" title="" :column="2">
              <el-descriptions-item label="快检室信息："></el-descriptions-item>
              <el-descriptions-item label="检测单位："></el-descriptions-item>
              <el-descriptions-item label="抽检员签名：">
                <div style="width: 200px;height: 20px;border-bottom: 1px solid #999"></div>
              </el-descriptions-item>
              <el-descriptions-item label="受检单位签名：">
                <div style="width: 200px;height: 20px;border-bottom: 1px solid #999"></div>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions title="" :column="2" border labelClassName="desc_title">
              <el-descriptions-item label="抽样检测日期"></el-descriptions-item>
              <el-descriptions-item label="抽样单编号"></el-descriptions-item>
              <el-descriptions-item label="抽样地点"></el-descriptions-item>
              <el-descriptions-item label="委托单位"></el-descriptions-item>
              <el-descriptions-item label="受检单位"></el-descriptions-item>
              <el-descriptions-item label="证件号码"></el-descriptions-item>
              <el-descriptions-item label="受检单位地址"></el-descriptions-item>
              <el-descriptions-item label="经营者姓名"></el-descriptions-item>
              <el-descriptions-item label="联系电话"></el-descriptions-item>
              <el-descriptions-item label="分光空白值"></el-descriptions-item>
              <el-descriptions-item label="可溯源设备名及编号"></el-descriptions-item>
            </el-descriptions>
            <el-table
              height="250"
              :data="quickTable"
              border
              style="width: 100%;margin-top: 10px">
              <el-table-column
                prop="date"
                label="采样编号">
              </el-table-column>
              <el-table-column
                prop="name"
                label="样品名称">
              </el-table-column>
              <el-table-column
                prop="address"
                label="受检项目">
              </el-table-column>
              <el-table-column
                prop="date"
                label="检测方法">
              </el-table-column>
              <el-table-column
                prop="name"
                label="检测值">
              </el-table-column>
              <el-table-column
                prop="address"
                label="检测结果">
              </el-table-column>
              <el-table-column
                prop="name"
                label="后续处理">
              </el-table-column>
              <el-table-column
                prop="address"
                label="试剂信息">
              </el-table-column>
            </el-table>
            <div class="quick_remark">备注：本次抽取8批次样品进行检测，无阳性样品</div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" style="display: flex;justify-content: space-between">
        <div>
<!--          <el-button >导出</el-button>-->
<!--          <el-button >打印</el-button>-->
        </div>
        <el-button type="primary" @click="quicklyShow = false">确 定</el-button>
      </div>
    </el-dialog>

  </basic-container>
</template>

<script>
  import {getCyList, getList, getDetail, saveOther} from "@/api/check/jgfh";
  import option from "@/const/check/jgfh";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        quicklyShow: false,
        quickTable:[],
        jcxx: {},
        dialogFormVisible: false,
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        jcsjData: [],
        detailShow: false,
        checkForm:{},
        checkOption: {
          submitBtn:false,
          emptyBtn:false,
          readonly: true,
          column: [
            {
              label:'样品编号',
              prop:'input',
              type:'text'
            },
            {
              label:'检测时间',
              prop:'input1',
              type: "text",
              valueFormat: "yyyy-MM-dd"
            },
            {
              label:'所属城市',
              prop:'sscs',
              type: "text",
              valueFormat: "yyyy-MM-dd"
            },
            {
              label: "所属任务",
              prop: "sbbh",
              type: "text",
              parent:false,
              dicUrl: "/api/blade-system/dept/tree",
              props: {
                label: "title",
                value: "id",
                res: 'data',
              },
            },
            {
              label: "样品大类",
              prop: "ypid",
              type: "text",
              dicUrl: "/api/blade-system/dict/dictionary?code=sample_big_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
            },
            {
              label:'样品名称',
              prop:'input',
              type:'text'
            },
            {
              label:'检测项目',
              prop:'input',
              type:'text'
            },
            {
              label: "检测人员",
              prop: "cyry",
              type: "text",
              dicUrl: "/api/blade-user/page?current=1&size=100000&deptId=",
              props: {
                label: "name",
                value: "id",
                res: 'data.records'
              },
            },
            {
              label: "检测方法",
              prop: "ypid",
              type: "text",
              dicUrl: "/api/blade-system/dict/dictionary?code=check_method",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
            },
            {
              label:'试剂信息',
              prop:'input',
              type:'text'
            },
            {
              label:'检测结果',
              prop:'input',
              type:'text'
            },
            {
              label:'检测值',
              prop:'input',
              type:'text'
            },
            {
              label:'最终结果',
              prop:'input',
              type:'text'
            },
            {
              label: "无需复核",
              prop: "ypid",
              type: "text",
              dicUrl: "/api/blade-system/dict/dictionary?code=review_status",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
            },
          ]
        },
        otherForm:{
          title1:'受检单位信息',
          title2:'溯源仪器信息',
          title3:'检测相关信息',
        },
        otherOption: {
          labelWidth: 110,
          column: [
            {
              labelWidth:10,
              type:'title',
              prop: "title1",
              span:24,
              styles:{
                fontSize:'14px',
                borderBottom: '1px dotted #ccc'
              }
            },
            {
              label:'受检单位',
              prop:'sjdw',
              type:'input'
            },
            {
              label:'档口号',
              prop:'dkh',
              type: "input",
            },
            {
              label:'受检单位地址',
              prop:'sjdwdz',
              type:'input'
            },
            {
              label:'经营者姓名',
              prop:'jyzxm',
              type:'input'
            },
            {
              label:'证件号码',
              prop:'zjhm',
              type:'input'
            },
            {
              label:'联系电话',
              prop:'lxdh',
              type:'input'
            },
            {
              labelWidth:10,
              type:'title',
              prop: "title2",
              span:24,
              styles:{
                fontSize:'14px',
                borderBottom: '1px dotted #ccc'
              }
            },
            {
              label: "溯源设备",
              prop: "sysb",
              type: "input",
            },
            {
              label:'分光空白值',
              prop:'fgkbz',
              type:'input'
            },
            // {
            //   prop: 'checkbox',
            //   type: 'checkbox',
            //   dicData:[{
            //     label:'未使用空白设备',
            //     value:0
            //   }]
            // },
            {
              labelWidth:10,
              type:'title',
              prop: "title3",
              span:24,
              styles:{
                fontSize:'14px',
                borderBottom: '1px dotted #ccc'
              }
            },
            {
              label:'总价',
              prop:'zj',
              type:'input'
            },
            {
              label:'大写金额',
              prop:'dxje',
              type:'input'
            },
            {
              label:'所属街道',
              prop:'scjd',
              type:'input'
            },
            {
              label:'抽样检测日期',
              prop:'cyjcrq',
              type:'date',
              valueFormat: "yyyy-MM-dd"
            },
            {
              label:'检测计数',
              prop:'jcjs',
              type:'input'
            },
            {
              label:'收据单号',
              prop:'sjdh',
              type:'input'
            },
            {
              label: "受检单位签名",
              prop: "sjdwqm",
              type: 'upload',
              fileText:'受检单位签名',
              loadText:'上传中...',
              limit:1,
              propsHttp: {
                name: 'data',
                url:'data'
              },
              action: '/api/manage/put-object',
              span: 8,
              hide: true,
            },
            {
              label: "抽检员1签名",
              prop: "cyyoqm",
              type: 'upload',
              fileText:'上传抽检员1签名',
              loadText:'上传中...',
              limit:1,
              propsHttp: {
                name: 'data',
                url:'data'
              },
              action: '/api/manage/put-object',
              span: 8,
              hide: true,
            },
            {
              label: "抽检员2签名",
              prop: "cyytqm",
              type: 'upload',
              fileText:'上传抽检员2签名',
              loadText:'上传中...',
              limit:1,
              propsHttp: {
                name: 'data',
                url:'data'
              },
              action: '/api/manage/put-object',
              span: 8,
              hide: true,
            },
            {
              label:'不合格问题备注',
              labelPosition:"top",
              prop:'bz',
              type:'input',
              span: 24
            },
          ]
        },
        otherShow: false,
        cyid: '',
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.sbtj_add, false),
          viewBtn: this.vaildData(this.permission.sbtj_view, false),
          delBtn: this.vaildData(this.permission.sbtj_delete, false),
          editBtn: this.vaildData(this.permission.sbtj_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      saveOther(){
        const par = {
          ...this.otherForm,
          sjdwqm: this.otherForm.sjdwqm.join(','),
          cyyoqm: this.otherForm.cyyoqm.join(','),
          cyytqm: this.otherForm.cyytqm.join(','),
          cyid: this.cyid
        }
        saveOther(par).then(res=>{
          this.$message({
            type: "success",
            message: res.msg
          });
        })
      },
      showOther(e){
        this.otherShow = true
        this.cyid = e.id
      },
      showPlan(){
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        sessionStorage.setItem()
        this.$router.push({
          path: '/device/qjhc/plan',
        })
      },
      showDetail(e){
        this.getDetail(e)
        this.dialogFormVisible = true
      },
      getDetail(e){
        getDetail(e.id).then(res=>{
        })
      },
      handleAdd(){
        this.quicklyShow = true
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const e = res.data.data;
          this.page.total = e.total;
          this.jcsjData = e.records;
          this.loading = false;
          // this.selectionClear();
        });
      }
    }
  };
</script>


<style lang="less" scoped>
  .quick_remark{
    width: 100%;
    height: 100px;
    margin-top: 10px;
  }
  .quickly_title{
    width: 100%;
    text-align: center;
    line-height: 50px;
    font-weight: bold;
  }
  .title_box{
    width: 100%;
    padding: 0 15px 10px 15px;
    border-bottom: 1px dotted #ccc;
    margin-bottom: 10px;
    font-weight: bold;
  }
</style>
<style>
  .desc_title{
    width: 150px;
    background: rgba(255,255,255,0) !important;
  }
  .el-dialog__body{
    padding: 10px 20px !important;
  }
  .avatar-uploader .el-upload {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 24px;
    color: #8c939d;
    width: 128px;
    height: 64px;
    line-height: 64px;
    text-align: center;
  }
  .avatar {
    width: 128px;
    height: 64px;
    display: block;
  }
</style>
