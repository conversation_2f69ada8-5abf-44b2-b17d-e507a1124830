<template>
  <basic-container>
    <el-row :gutter="20">
      <el-col :span="14">
        <div class="chart_title_box">各市场设备统计</div>
        <div id="num_chart"></div>
      </el-col>
      <el-col :span="10">
        <div class="chart_title_box">
          <span>检测设备类型占比</span>
          <el-select size="mini" v-model="day" placeholder="请选择" @change="getXzsblx">
            <el-option label="日" value="日"></el-option>
            <el-option label="周" value="周"></el-option>
            <el-option label="月" value="月"></el-option>
          </el-select>
        </div>
        <div id="type_chart"></div>
      </el-col>
    </el-row>

    <div class="chart_title_box" style="margin-top: 10px">设备期间核查统计</div>
    <div id="ratio_chart"></div>
  </basic-container>
</template>

<script>
import { sblxtj, xzsblx, hctj } from "@/api/work/chart";
export default {
  name: "chart",
  data() {
    return {
      day: '日',
    }
  },
  mounted() {
    this.getSblxtj();
    this.getXzsblx();
    this.gethctj();
  },
  methods: {
    gethctj(){
      hctj().then(({ data: resultData })=>{
        if (resultData.code == 200) {
        const enumData = [
        {
          key: "name",
          value: '市场'
        },
        {
          key: "sl",
          value: 'sl'
        }];
          let { data } = resultData;
          let formateData = enumData.map(({ key, value }) => {
            return {
              name: value,
              data: new Array(data.length).fill('').map((_, index) => {
                return data[index][key];
              }),
            }
          })
         this.initRatioChart([formateData[0].data , formateData[1].data]);
        }
      })
    },
    getXzsblx(type = '日'){
      xzsblx(type).then(({ data: resultData })=>{
        if (resultData.code == 200) {
          let { data } = resultData;
          this.initTypeChart(data.map(item => ({  name : item.name , value:item.sl})))
        }
      })
    },
    getSblxtj() {
      const enumData = [
        {
          key: "name",
          value: '市场'
        },
        {
          key: "dztp",
          value: '电子天平'
        },
        {
          key: 'fggdj',
          value: '分光光度计'
        },
        {
          key: 'srg',
          value: '水溶锅'
        },
        {
          key: 'yyq',
          value: '移液器'
        }];
      sblxtj().then(({ data: resultData }) => {
        if (resultData.code == 200) {
          let { data } = resultData;
         let formateData = enumData.map(({ key, value }) => {
            return {
              name: value,
              data: new Array(data.length).fill('').map((_, index) => {
                return data[index][key];
              }),
              type: 'bar',
              stack: 'Ad',
              barWidth: 50,
            }
          })

          let shichang = formateData.shift();

          this.initNumChart(formateData , enumData  , shichang );
        }
      });
    },
    initNumChart(formateData , enumData , shichang) {
      const chartDom = document.getElementById('num_chart');
      const myChart = this.echarts.init(chartDom);
      const option = {
        legend: {
          data: enumData.map(item => item.value)
        },
        xAxis: {
          type: 'category',
          data: shichang.data
        },
        yAxis: {
          type: 'value'
        },
        series:formateData
        // series: [
        //   {
        //     name: '快检仪',
        //     data: [120, 200, 150, 80, 70, 110],
        //     type: 'bar',
        //     stack: 'Ad',
        //     barWidth: 50,
        //     emphasis: {
        //       focus: 'series'
        //     },
        //   },
        //   {
        //     name: '均质器',
        //     data: [21, 32, 13, 17, 70, 110],
        //     type: 'bar',
        //     stack: 'Ad',
        //     barWidth: 50,
        //   }
        // ]
      };

      option && myChart.setOption(option);
    },
    initRatioChart(initRatioChart) {
      const chartDom = document.getElementById('ratio_chart');
      const myChart = this.echarts.init(chartDom);
      const option = {
        legend: {
        },
        xAxis: {
          type: 'category',
          data: initRatioChart[0]
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '核查',
            data: initRatioChart[1],
            type: 'bar',
            barWidth: 50,
          },
        ]
      };

      option && myChart.setOption(option);
    },
    initTypeChart(data) {
      const chartDom = document.getElementById('type_chart');
      const myChart = this.echarts.init(chartDom);
      const option = {
        legend: {
          top: 'center',
          orient: 'vertical'
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data
          }
        ]
      };

      option && myChart.setOption(option);
    },

  }

}
</script>

<style scoped>
.chart_title_box {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

#num_chart,
#ratio_chart,
#type_chart {
  width: 100%;
  height: 500px;
  border: 1px solid #04A7B3;
  padding: 10px;
  box-sizing: border-box;
}
</style>
