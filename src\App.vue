<template>
  <div id="app">
    <keep-alive>
      <router-view v-if="$route.meta.keepAlive" />
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive" />
  </div>
</template>

<script>
export default {
  name: "app",
  data() {
    return {};
  },
  watch: {},
  created() {
  },
  methods: {},
  computed: {}
};
</script>
<style lang="scss">
body {
  font-family: HanSerif !important;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

div {
  box-sizing: border-box;
}

.avue--detail .el-col {
  margin-bottom: 0;
}

.dialog__headerbtn,
.el-dialog__headerbtn {
  top: 18px !important;
}

.moon .el-input--mini .el-input__inner{
  background-color: #262424 !important;
  border-color: #085875 !important;
  color: rgb(11,229,245) !important;
}

.moon .el-form-item__label{
  color: #A6A3A3 !important;
  margin-bottom: 4px !important;
}

.light .el-form-item__label{
  color: #A6A3A3 !important;
  margin-bottom: 4px !important;
}
</style>
