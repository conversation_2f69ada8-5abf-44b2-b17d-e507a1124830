import request from '@/router/axios';
export const downLoadModel = () => {
  return request({
    url: '/api/jc/smsj/downLoadModel',
    method: 'get',
    responseType: 'blob',
    params: {
    }
  })
}
export const sendEmail = (row) => {
  return request({
    url: '/api/jc/smsj/sendEmail',
    method: 'post',
    data: row
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/jc/smsj/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/jc/smsj/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/jc/smsj/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/jc/smsj/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/jc/smsj/submit',
    method: 'post',
    data: row
  })
}

