export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchShow: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  delBtn: false,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  labelWidth: 110,
  align: 'center',

  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "供应商名称",
      prop: "cybh",
      type: "input",
    },
    {
      label: "供应商联系人",
      prop: "cysj",
      type: "input",
    },
    {
      label: "联系电话",
      prop: "sscs",
      type: "input",
    },
    {
      label: "填报人员",
      prop: "ypmc",
      type: "input",
    },
    {
      label: "填报地址",
      prop: "cydz",
      type: "input",
    },
    {
      label: "关联样品",
      prop: "shid",
      type: "input",
    },
    {
      label: "上传凭证",
      prop: "url",
      type: 'upload',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      row: true,
      hide: true,
    },
    {
      label: "转至",
      prop: "menuBtn",
      type: "tree",
      span: 24,
      dicUrl: "/api/blade-system/dept/tree",
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      hide: true,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
