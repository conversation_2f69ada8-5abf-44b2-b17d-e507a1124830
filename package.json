{"name": "saber-admin", "version": "3.0.1", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "avue-plugin-ueditor": "^0.1.4", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "classlist-polyfill": "^1.2.0", "crypto-js": "^4.0.0", "echarts": "^5.4.1", "echarts-gl": "^2.0.9", "element-ui": "^2.15.6", "js-base64": "^2.5.1", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "jspdf": "^2.5.1", "mathjs": "^11.9.1", "mockjs": "^1.0.1-beta3", "mux.js": "^5.7.0", "node-gyp": "^5.0.6", "nodemailer": "^6.9.4", "nprogress": "^0.2.0", "portfinder": "^1.0.23", "qrcodejs2": "^0.0.2", "sass": "^1.78.0", "screenfull": "^5.2.0", "script-loader": "^0.7.2", "video.js": "^8.0.4", "videojs-contrib-hls": "^5.15.0", "vue": "^2.6.10", "vue-axios": "^2.1.2", "vue-i18n": "^8.7.0", "vue-printjs": "^1.0.0", "vue-router": "^3.0.1", "vuex": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "babel-loader": "^9.2.1", "cache-loader": "^4.1.0", "chai": "^4.1.2", "eslint-loader": "^4.0.2", "less": "^4.1.1", "less-loader": "^5.0.0", "node-sass": "^6.0.1", "sass-loader": "^10.0.5", "vue-template-compiler": "^2.5.17", "webpack-bundle-analyzer": "^3.0.3"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}