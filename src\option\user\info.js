export default {
  tabs: false,
  tabsActive: 1,
  group: [
    {
      label: '个人信息',
      prop: 'info',
      column: [{
        label: '头像',
        type: 'upload',
        listType: 'picture-img',
        canvasOption: {
          text: ' ',
          ratio: 0.1
        },
        propsHttp: {
          url: 'data',
          name: 'data'
        },
        action: '/api/manage/put-object',
        tip: '只能上传jpg/png用户头像，且不超过500kb',
        span: 12,
        row: true,
        prop: 'avatar'
      }, {
        label: '姓名',
        span: 12,
        row: true,
        prop: 'realName'
      }, {
        label: '用户名',
        span: 12,
        row: true,
        prop: 'name'
      }, {
        label: '手机号',
        span: 12,
        row: true,
        prop: 'phone'
      }, {
        label: '邮箱',
        prop: 'email',
        span: 12,
        row: true,
      }]
    },
  ],
}
