import request from '@/router/axios';

export const downLoadModel = () => {
  return request({
    url: '/api/jc/jcsj/downLoadModel',
    method: 'get',
    responseType: 'blob',
    params: {
    }
  })
}
export const getCyList = (current, size, params) => {
  return request({
    url: '/api/jc/cysj/list?status=2',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/jc/jcsj/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/jc/jcsj/detail',
    method: 'get',
    params: {
      id
    }
  })
}
//经营户详情
export const getShopDetail = (id) => {
  return request({
    url: '/api/sh/jyh/detail',
    method: 'get',
    params: {
      id
    }
  })
}

// 移液器详情
export const getYyqDetail = (hcid) => {
  return request({
    url: '/api/sb/hcjl/yyqDetail',
    method: 'get',
    params: {
      hcid
    }
  })
}
// 电子天平详情
export const getDztpDetail = (hcid) => {
  return request({
    url: '/api/sb/hcjl/dztpDetail',
    method: 'get',
    params: {
      hcid
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/sb/sbtj/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/sb/sbtj/submit',
    method: 'post',
    data: row
  })
}

export const saveOther = (row) => {
  return request({
    url: '/api/jc/qtxx/save',
    method: 'post',
    data: row
  })
}
export const update = (row) => {
  return request({
    url: '/api/sb/sbtj/submit',
    method: 'post',
    data: row
  })
}

