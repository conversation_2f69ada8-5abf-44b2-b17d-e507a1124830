import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/zlgl/mykhyd/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/zlgl/mykhyd/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/zlgl/mykhyd/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const addYd = (row) => {
  return request({
    url: '/api/zlgl/mykhyd/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/zlgl/mykhyd/submit',
    method: 'post',
    data: row
  })
}

