export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchShow: true,
  searchMenuSpan:6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  columnBtn: false,
  align: 'center',
  column: [
    {
      label: "商户id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "受检单位",
      prop: "ssrwid",
      type: "tree",
      filterable: true,
      dicUrl: "/api/sh/sczt/list",
      props: {
        label: "scmc",
        value: "id",
        res: 'data.records'
      },
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      nodeClick: (data, node, nodeComp) => {
        if (window.__jhsy_component__) {
          window.__jhsy_component__.handleNodeClick(data, 'scmc','ssrwmc');
        }
      },
    },
    {
      label: "受检单位名称",
      prop: "ssrwmc",
      type: "input",
      display: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "经营户",
      prop: "jyhid",
      type: "tree",
      filterable: true,
      dicUrl: "/api/sh/jyh/page?size=99999&current=1",
      props: {
        label: "jyhmc",
        value: "id",
        res: 'data.records'
      },
      search: true,
      rules: [
        {
          required: true,
          message: "请选择",
          trigger: "change"
        }
      ],
      nodeClick: (data, node, nodeComp) => {
        if (window.__jhsy_component__) {
          window.__jhsy_component__.handleNodeClick(data,'twh','twh');
        }
      },
    },

    {
      label: "摊位号",
      prop: "twh",
      type: "input",
    },
    {
      label: "商品名称",
      prop: "spmc",
      type: "tree",
      filterable: true,
      dicUrl: "/api/ypgl/ypxx/list?current=1&size=9999",
      props: {
        label: "ypmc",
        value: "ypmc",
        res: 'data.records'
      },
      search: true
    },
    // {
    //   label: "街道",
    //   prop: "jd",
    //   type: "input",
    //   // searchSpan:4,
    //   // search: true,
    // },
    {
      label: "进货单号",
      prop: "jhdh",
      type: "input",
    },
    {
      label: "进货日期",
      prop: "jhrq",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      searchSpan:6,
      search: true,
      searchRange:true,
    },
    {
      label: "进货数量",
      prop: "jhsl",
      type: "input",
    },
    {
      label: "地址",
      prop: "jd",
      type: "input",
    },
    {
      label: "供货商",
      prop: "ghs",
      type: "input",
    },
    {
      label: "上传日期",
      prop: "createTime",
      type: "datetime",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      searchSpan:6,
      search: true,
      searchRange:true,
    },
    {
      label: "凭证",
      prop: "pz",
      type: 'upload',
      listType: 'picture-card',
      dataType: 'string',
      propsHttp: {
        url: 'data',
        name: 'data'
      },
      action: '/api/manage/put-object',
      span: 24,
      width: 300,
      row: false,
      hide: true,
      multiple: true,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
