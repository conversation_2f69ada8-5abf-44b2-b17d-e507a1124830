export default {
  height:'auto',
  calcHeight: 30,
  dialogWidth: '70%',
  tip: false,
  searchShow: false,
  searchMenuSpan: 3,
  border: true,
  index: true,
  viewBtn: true,
  addBtn: false,
  selection: true,
  dialogClickModal: false,
  columnBtn: false,
  menu: false,
  align: 'center',
  searchLabelWidth:100,
  excelBtn: false,       // 导出Excel，添加权限后默认设置false
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "设备id",
      prop: "sbid",
      type: "input",
      hide: true,
    },
    {
      label: "所属任务",
      width: 150,
      prop: "rwmc",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      props: {
        label: "title",
        value: "id",
        res: 'data',
      },
      searchSpan:4,
      search: true,
    },
    {
      label: "设备名称",
      prop: "sbmc",
      type: "input",
      searchSpan:4,
      search: true,
    },
    {
      label: "设备编号",
      prop: "sbbh",
      type: "input",
    },
    {
      label: "设备厂家",
      prop: "sbcj",
      type: "input",
    },
    {
      label: "标准仪器名称",
      prop: "sbcj",
      type: "input",
      dicUrl: "/api/blade-system/dict/dictionary?code=standard_device",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      width: 100,
    },
    {
      label: "设备类型",
      prop: "sblx",
      type: "input",
      dicData: [
        {
          label: '设备',
          value: '1',
        },
        {
          label: '设施',
          value: '2',
        }
      ],
    },
    {
      label: "设备管理员",
      prop: "sbgly",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      dicUrl: "/api/blade-user/page?current=1&size=100000&deptId=",
      props: {
        label: "name",
        value: "id",
        res: 'data.records'
      },
      width: 100,
    },
    {
      label: "校准记录",
      prop: "xzjl",
      type: "input",
    },
    {
      label: "下次校准时间",
      prop: "xcxzsj",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      searchSpan: 6,
      search: true,
      searchRange:true,
      width: 220,
    },
    {
      label: "到期提醒",
      prop: "dqtx",
      type: "input",
      width: 100,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "状态(0:关闭 1:开启)",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除(0:正常 1:删除)",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
