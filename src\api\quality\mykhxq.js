import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/zlgl_/mykhxq/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/zlgl_/mykhxq/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/zlgl_/mykhxq/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/zlgl_/mykhxq/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/zlgl_/mykhxq/submit',
    method: 'post',
    data: row
  })
}

